/**
 * 播放策略模板服务
 * 提供模板相关的API调用功能
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

class TemplateService {
  /**
   * 获取模板列表
   * @param {Object} params 查询参数
   * @param {string} params.type 模板类型 (system|user)
   * @param {boolean} params.isPublic 是否公开
   * @returns {Promise<Object>} 模板列表
   */
  async getTemplates(params = {}) {
    try {
      const response = await httpClient.get(
        API_ENDPOINTS.TEMPLATES.BASE,
        params
      );
      return response;
    } catch (error) {
      console.error("获取模板列表失败:", error);
      throw error;
    }
  }

  /**
   * 根据ID获取模板
   * @param {string} id 模板ID
   * @returns {Promise<Object>} 模板对象
   */
  async getTemplateById(id) {
    try {
      const response = await httpClient.get(
        `${API_ENDPOINTS.TEMPLATES.BASE}/${id}`
      );
      return response;
    } catch (error) {
      console.error("获取模板失败:", error);
      throw error;
    }
  }

  /**
   * 创建模板
   * @param {Object} templateData 模板数据
   * @param {string} templateData.name 模板名称
   * @param {string} templateData.description 模板描述
   * @param {Object} templateData.config 模板配置
   * @param {boolean} templateData.isPublic 是否公开
   * @returns {Promise<Object>} 创建的模板
   */
  async createTemplate(templateData) {
    try {
      const response = await httpClient.post(
        API_ENDPOINTS.TEMPLATES.BASE,
        templateData
      );
      return response;
    } catch (error) {
      console.error("创建模板失败:", error);
      throw error;
    }
  }

  /**
   * 更新模板
   * @param {string} id 模板ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<Object>} 更新后的模板
   */
  async updateTemplate(id, updateData) {
    try {
      const response = await httpClient.put(
        `${API_ENDPOINTS.TEMPLATES.BASE}/${id}`,
        updateData
      );
      return response;
    } catch (error) {
      console.error("更新模板失败:", error);
      throw error;
    }
  }

  /**
   * 删除模板
   * @param {string} id 模板ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteTemplate(id) {
    try {
      await httpClient.delete(`${API_ENDPOINTS.TEMPLATES.BASE}/${id}`);
      return true;
    } catch (error) {
      console.error("删除模板失败:", error);
      throw error;
    }
  }

  /**
   * 使用模板（增加使用次数）
   * @param {string} id 模板ID
   * @returns {Promise<boolean>} 是否成功
   */
  async useTemplate(id) {
    try {
      await httpClient.post(API_ENDPOINTS.TEMPLATES.USE(id));
      return true;
    } catch (error) {
      console.error("更新使用记录失败:", error);
      throw error;
    }
  }

  /**
   * 复制模板
   * @param {string} id 原模板ID
   * @param {string} newName 新模板名称
   * @returns {Promise<Object>} 新模板
   */
  async duplicateTemplate(id, newName) {
    try {
      const response = await httpClient.post(
        API_ENDPOINTS.TEMPLATES.DUPLICATE(id),
        {
          name: newName,
        }
      );
      return response;
    } catch (error) {
      console.error("复制模板失败:", error);
      throw error;
    }
  }

  /**
   * 验证模板配置格式
   * @param {Object} config 配置对象
   * @returns {Object} 验证结果
   */
  validateTemplateConfig(config) {
    const errors = [];

    if (!config || typeof config !== "object") {
      errors.push("配置必须是一个对象");
      return { valid: false, errors };
    }

    if (!config.sections || !Array.isArray(config.sections)) {
      errors.push("配置必须包含sections数组");
      return { valid: false, errors };
    }

    if (config.sections.length === 0) {
      errors.push("至少需要一个环节配置");
      return { valid: false, errors };
    }

    // 验证每个环节的配置
    config.sections.forEach((section, index) => {
      const requiredFields = [
        "title",
        "speed",
        "pauseDuration",
        "repeatCount",
        "enableTranslation",
        "translationLanguage",
        "translationPosition",
        "enableKeywords",
        "keywordPosition",
        "keywordRepeatCount",
        "keywordSpeed",
        "repeatSpeeds",
        "repeatPauses",
      ];

      requiredFields.forEach((field) => {
        if (section[field] === undefined) {
          errors.push(`环节${index + 1}缺少字段: ${field}`);
        }
      });

      // 验证数组长度
      if (
        section.repeatSpeeds &&
        section.repeatCount &&
        section.repeatSpeeds.length !== section.repeatCount
      ) {
        errors.push(
          `环节${index + 1}的repeatSpeeds数组长度与repeatCount不匹配`
        );
      }

      if (
        section.repeatPauses &&
        section.repeatCount &&
        section.repeatPauses.length !== section.repeatCount
      ) {
        errors.push(
          `环节${index + 1}的repeatPauses数组长度与repeatCount不匹配`
        );
      }

      // 验证数值范围
      if (
        section.speed !== undefined &&
        (section.speed < 0.5 || section.speed > 2.0)
      ) {
        errors.push(`环节${index + 1}的speed值应在0.5-2.0之间`);
      }

      if (
        section.repeatCount !== undefined &&
        (section.repeatCount < 1 || section.repeatCount > 10)
      ) {
        errors.push(`环节${index + 1}的repeatCount值应在1-10之间`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建默认模板配置
   * @param {number} sectionCount 环节数量
   * @returns {Object} 默认配置
   */
  createDefaultConfig(sectionCount = 1) {
    const sections = [];

    for (let i = 0; i < sectionCount; i++) {
      sections.push({
        title: `环节 ${i + 1}`, // 添加默认标题
        speed: 1.0,
        pauseDuration: 3000,
        repeatCount: 4,
        enableTranslation: false,
        translationLanguage: "",
        translationPosition: 2,
        enableKeywords: false,
        keywordRepeatCount: 2,
        keywordPosition: 2,
        keywordSpeed: 1.0,
        repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
        repeatPauses: [3000, 3000, 3000, 3000],
      });
    }

    return { sections };
  }

  /**
   * 应用模板到内容配置
   * @param {Object} serverConfig 服务器内容配置
   * @param {Object} template 模板对象
   * @returns {Object} 应用模板后的配置
   */
  applyTemplateToContent(serverConfig, template) {
    if (
      !serverConfig ||
      !serverConfig.sections ||
      !template ||
      !template.config
    ) {
      throw new Error("无效的配置或模板");
    }

    const templateSections = template.config.sections;
    const serverSections = serverConfig.sections;

    // 按照模板的环节数量来生成配置
    const finalConfig = {
      ...serverConfig,
      sections: templateSections.map((templateSection, index) => {
        // 如果服务器有对应的环节，使用服务器的控制字段
        const serverSection = serverSections[index];

        if (serverSection) {
          return {
            // 保留服务器控制的字段
            id: serverSection.id,
            name: serverSection.title, // 使用name字段显示标题
            title: serverSection.title,
            description: serverSection.description,
            processingMode: serverSection.processingMode,
            userEditable: serverSection.userEditable,
            sourceIndex: serverSection.sourceIndex,
            sourceNodeIds: serverSection.sourceNodeIds,

            // 应用模板的播放策略字段
            ...templateSection,
          };
        } else {
          // 如果服务器没有对应的环节，创建一个基础环节
          const sectionTitle = templateSection.title || `环节 ${index + 1}`;
          return {
            id: `section_template_${Date.now()}_${index}`,
            name: sectionTitle, // 使用name字段显示标题
            title: sectionTitle,
            description: "模板生成的环节",
            processingMode: "sequence",
            userEditable: true,
            sourceIndex: 0,

            // 应用模板的播放策略字段
            ...templateSection,
          };
        }
      }),
    };

    return finalConfig;
  }

  /**
   * 从当前配置创建模板
   * @param {Object} currentConfig 当前播放配置
   * @returns {Object} 模板配置
   */
  createTemplateFromConfig(currentConfig) {
    if (!currentConfig || !currentConfig.sections) {
      throw new Error("无效的配置");
    }

    const templateConfig = {
      sections: currentConfig.sections.map((section) => {
        // 只提取播放策略相关的字段
        const {
          title,
          speed,
          pauseDuration,
          repeatCount,
          enableTranslation,
          translationLanguage,
          translationPosition,
          enableKeywords,
          keywordPosition,
          keywordRepeatCount,
          keywordSpeed,
          repeatSpeeds,
          repeatPauses,
        } = section;

        return {
          title,
          speed,
          pauseDuration,
          repeatCount,
          enableTranslation,
          translationLanguage,
          translationPosition,
          enableKeywords,
          keywordPosition,
          keywordRepeatCount,
          keywordSpeed,
          repeatSpeeds,
          repeatPauses,
        };
      }),
    };

    return templateConfig;
  }
}

export default new TemplateService();
