/**
 * 时间线生成工具
 * 根据内容配置和播放设置生成时间线
 */

/**
 * 从节点配置中提取序列数据
 * @param {Object} configJson - 内容配置JSON
 * @returns {Array} 序列数组
 */
export function extractSequencesFromNodes(configJson) {
  if (!configJson || !configJson.nodes) {
    console.error("无效的配置JSON:", configJson);
    return [];
  }

  const sequences = [];
  const nodes = configJson.nodes;
  const resources = configJson.resources || {};

  // 打印节点类型统计
  const nodeTypes = {};
  Object.values(nodes).forEach((node) => {
    nodeTypes[node.type] = (nodeTypes[node.type] || 0) + 1;
  });
  console.log("节点类型统计:", nodeTypes);

  // 查找所有文本序列节点和文本内容节点
  Object.values(nodes).forEach((node) => {
    console.log("检查节点:", node.id, node.type);

    // 处理文本序列节点
    if (node.type === "textSequence" || node.type === "videoConfig") {
      try {
        console.log("处理文本序列节点:", node.id);
        console.log("节点参数:", node.params);

        // 检查节点是否有预处理的序列
        if (
          node.params &&
          node.params.sequence &&
          node.params.sequence.length > 0
        ) {
          console.log("使用节点中的预处理序列");

          // 直接使用节点中的预处理序列
          const items = node.params.sequence.map((item) => {
            return {
              id: item.id,
              content: item.content,
              language: item.language || "ja",
              type: item.type || "normal", // 添加类型属性，默认为普通文本
              audioUrl: item.audioUrl,
              duration: item.duration || 2, // 默认时长2秒
            };
          });

          if (items.length > 0) {
            sequences.push({
              id: node.id,
              name: node.customName || `序列 ${sequences.length + 1}`,
              items: items,
            });
            return; // 已找到序列，不需要继续处理
          }
        }

        // 如果节点没有预处理的序列，则从源节点获取内容
        console.log("从源节点获取内容");

        // 获取源节点
        const sourceNodes = node.sourceIds
          ? node.sourceIds.map((id) => nodes[id]).filter(Boolean)
          : [];

        console.log(
          "源节点:",
          sourceNodes.map((n) => n.id)
        );

        // 收集所有分句
        const items = [];

        // 处理源节点
        sourceNodes.forEach((sourceNode) => {
          console.log("处理源节点:", sourceNode.id, sourceNode.type);

          // 处理文本内容节点
          if (sourceNode.type === "textContent") {
            // 检查节点是否有分段
            if (
              sourceNode.params &&
              sourceNode.params.segments &&
              sourceNode.params.segments.length > 0
            ) {
              console.log("使用节点中的分段");

              sourceNode.params.segments.forEach((segment) => {
                // 查找音频资源
                let audioUrl = null;
                if (
                  resources.audioItems &&
                  Array.isArray(resources.audioItems)
                ) {
                  console.log(
                    "查找音频资源，内容:",
                    segment.content.substring(0, 20) + "..."
                  );
                  const audioItem = resources.audioItems.find(
                    (item) => item.text === segment.content
                  );
                  if (audioItem) {
                    audioUrl = audioItem.url || audioItem.audioUrl;
                    console.log("找到音频URL:", audioUrl);
                  } else {
                    console.log("未找到匹配的音频资源");
                  }
                } else {
                  console.log("资源中没有音频项目");
                }

                items.push({
                  id: segment.id,
                  content: segment.content,
                  language: segment.language || "ja",
                  type: segment.type || "normal", // 添加类型属性，默认为普通文本
                  audioUrl: audioUrl,
                  duration: 2, // 默认时长2秒
                });
              });
            } else if (sourceNode.params && sourceNode.params.text) {
              console.log("使用节点中的文本");

              // 如果没有分段，则使用整个文本
              const text = sourceNode.params.text;
              const segmentId = `seg_${Math.random()
                .toString(36)
                .substring(2, 15)}`;

              // 查找音频资源
              let audioUrl = null;
              if (resources.audioItems && Array.isArray(resources.audioItems)) {
                console.log(
                  "查找音频资源，内容:",
                  text.substring(0, 20) + "..."
                );
                const audioItem = resources.audioItems.find(
                  (item) => item.text === text
                );
                if (audioItem) {
                  audioUrl = audioItem.url || audioItem.audioUrl;
                  console.log("找到音频URL:", audioUrl);
                } else {
                  console.log("未找到匹配的音频资源");
                }
              } else {
                console.log("资源中没有音频项目");
              }

              items.push({
                id: segmentId,
                content: text,
                language: sourceNode.params.language || "ja",
                type: sourceNode.params.type || "normal", // 添加类型属性，默认为普通文本
                audioUrl: audioUrl,
                duration: 2, // 默认时长2秒
              });
            }
          }
        });

        // 如果找到了内容，创建序列
        if (items.length > 0) {
          // 生成序列名称
          let sequenceName = node.customName;
          if (!sequenceName) {
            if (sourceNodes.length === 1) {
              // 单个源节点
              const sourceNode = sourceNodes[0];
              sequenceName = `${sourceNode.customName || sourceNode.type} 内容`;
            } else {
              // 多个源节点
              sequenceName = `合并内容 (${sourceNodes.length} 个源节点)`;
            }
          }

          sequences.push({
            id: node.id,
            name: sequenceName,
            items: items,
          });
        }
      } catch (error) {
        console.error("处理序列节点失败:", error);
      }
    }

    // 为每个文本内容节点也创建一个序列，以便基于源节点的环节可以直接使用
    else if (node.type === "textContent") {
      try {
        console.log("处理文本内容节点:", node.id);

        // 检查节点是否有分段
        if (
          node.params &&
          node.params.segments &&
          node.params.segments.length > 0
        ) {
          console.log(`文本内容节点有 ${node.params.segments.length} 个分段`);

          // 创建序列项目
          const items = node.params.segments.map((segment) => {
            // 查找音频资源
            let audioUrl = null;
            if (resources.audioItems && Array.isArray(resources.audioItems)) {
              const audioItem = resources.audioItems.find(
                (item) => item.text === segment.content
              );
              if (audioItem) {
                audioUrl = audioItem.url || audioItem.audioUrl;
              }
            }

            return {
              id: segment.id,
              content: segment.content,
              language: segment.language || "ja",
              type: segment.type || "normal", // 添加类型属性，默认为普通文本
              audioUrl: audioUrl,
              duration: 2, // 默认时长2秒
            };
          });

          // 创建序列
          sequences.push({
            id: node.id,
            name: node.customName || `文本内容 ${sequences.length + 1}`,
            items: items,
          });

          console.log(
            `为文本内容节点 ${node.id} 创建了序列，项目数: ${items.length}`
          );
        } else if (node.params && node.params.text) {
          console.log("文本内容节点只有文本，没有分段");

          // 如果没有分段，则使用整个文本
          const text = node.params.text;
          const segmentId = `seg_${Math.random()
            .toString(36)
            .substring(2, 15)}`;

          // 查找音频资源
          let audioUrl = null;
          if (resources.audioItems && Array.isArray(resources.audioItems)) {
            const audioItem = resources.audioItems.find(
              (item) => item.text === text
            );
            if (audioItem) {
              audioUrl = audioItem.url || audioItem.audioUrl;
            }
          }

          // 创建序列项目
          const items = [
            {
              id: segmentId,
              content: text,
              language: node.params.language || "ja",
              type: node.params.type || "normal", // 添加类型属性，默认为普通文本
              audioUrl: audioUrl,
              duration: 2, // 默认时长2秒
            },
          ];

          // 创建序列
          sequences.push({
            id: node.id,
            name: node.customName || `文本内容 ${sequences.length + 1}`,
            items: items,
          });

          console.log(`为文本内容节点 ${node.id} 创建了序列，项目数: 1`);
        }
      } catch (error) {
        console.error("处理文本内容节点失败:", error);
      }
    }
  });

  // 如果没有找到序列节点，记录错误信息
  if (sequences.length === 0) {
    console.error("未找到文本序列节点，请检查内容配置");
  }

  console.log("提取的序列数据:", sequences);
  return sequences;
}

/**
 * 获取视频配置信息
 * @param {Object} configJson - 内容配置JSON
 * @returns {Object|null} 视频配置信息
 */
function getVideoConfig(configJson) {
  if (!configJson || !configJson.nodes) {
    return null;
  }

  // 查找视频配置节点
  const videoConfigNode = Object.values(configJson.nodes).find(
    (node) => node.type === "videoConfig"
  );

  if (!videoConfigNode || !videoConfigNode.params) {
    return null;
  }

  return videoConfigNode.params;
}

/**
 * 生成时间线
 * @param {Object} configJson - 内容配置JSON
 * @param {Object} settings - 播放设置
 * @returns {Array} 时间线数组
 */
export function generateTimeline(configJson, settings) {
  if (!configJson || !settings || !settings.sections) {
    console.error("无效的配置或设置:", { configJson, settings });
    return [];
  }

  // 从节点中提取序列数据
  const sequences = extractSequencesFromNodes(configJson);
  if (sequences.length === 0) {
    console.error("未找到有效的序列数据");
    return [];
  }

  const timeline = [];
  let currentTime = 0;

  // 获取视频配置信息
  const videoConfig = getVideoConfig(configJson);

  // 如果有封面图片，添加到时间线的开头
  if (videoConfig && videoConfig.cover && videoConfig.cover.imageUrl) {
    console.log("添加封面图片到时间线开头:", videoConfig.cover.imageUrl);

    // 获取封面显示时长
    const coverDuration = videoConfig.cover.duration || 3;

    // 添加封面项到时间线（使用新的数据结构）
    timeline.push({
      id: "cover",
      type: "cover",
      content: "", // 封面不显示文本内容
      imageUrl: videoConfig.cover.imageUrl, // 使用imageUrl而不是coverImageUrl
      startTime: 0,
      duration: coverDuration,
      contentType: "image", // 添加contentType字段
    });

    // 更新当前时间
    currentTime += coverDuration;
  }

  // 保存configJson引用，供后续使用
  const configJsonRef = configJson;

  // 遍历所有环节（移除enabled检查）
  settings.sections.forEach((section) => {
    console.log(
      `处理环节: ${section.title || section.name}, 类型: ${
        section.type
      }, 处理模式: ${section.processingMode || "sequence"}, 停顿时长: ${
        section.pauseDuration || 3000
      }毫秒, 语速: ${section.speed || 1.0}x`
    );

    // 确保处理模式存在
    const processingMode = section.processingMode || "sequence";

    // 获取环节配置
    const pauseDuration = section.pauseDuration || 3000;

    // 获取源内容项
    const sourceItems = getSourceItems(
      configJsonRef,
      sequences,
      section,
      processingMode
    );

    if (!sourceItems || sourceItems.length === 0) {
      console.warn(
        `环节 ${section.title || section.name} 没有有效的源内容项，跳过处理`
      );
      return;
    }

    console.log(`获取到源内容项，数量: ${sourceItems.length}`);

    // 处理环节 - 所有环节都作为序列环节处理
    processSequenceSection(
      timeline,
      sourceItems,
      section,
      processingMode,
      currentTime,
      configJsonRef
    );

    // 更新当前时间
    const lastItem = timeline[timeline.length - 1];
    if (lastItem) {
      currentTime =
        lastItem.startTime + lastItem.duration + pauseDuration / 1000;
    }
  });

  return timeline;
}

/**
 * 获取源内容项
 * @param {Object} configJson - 内容配置JSON
 * @param {Array} sequences - 序列数组
 * @param {Object} section - 环节设置
 * @param {string} processingMode - 处理模式
 * @returns {Array} 源内容项数组
 */
function getSourceItems(configJson, sequences, section, processingMode) {
  // 根据处理模式获取内容项
  if (processingMode === "sequence") {
    // 基于序列模式 - 首先查找文本序列节点的自定义序列

    // 记录可用序列信息，便于调试
    console.log(
      `可用序列列表:`,
      sequences.map(
        (seq, idx) => `[${idx}] ${seq.name} (${seq.items?.length || 0}项)`
      )
    );

    // 查找文本序列节点
    const textSequenceNodes = Object.values(configJson.nodes || {}).filter(
      (node) => node.type === "textSequence"
    );

    // 如果找到文本序列节点，并且节点有自定义序列，则使用自定义序列
    for (const node of textSequenceNodes) {
      if (
        node.params &&
        node.params.sequence &&
        node.params.sequence.length > 0
      ) {
        console.log(
          `找到文本序列节点 ${node.id} 的自定义序列，项目数: ${node.params.sequence.length}`
        );

        // 转换为序列项目格式
        const items = node.params.sequence.map((item) => ({
          id: item.id,
          content: item.content,
          language: item.language || "auto",
          type: item.type || "normal", // 添加类型属性，默认为普通文本
          audioUrl: findAudioUrl(configJson, item.content),
          duration: findAudioDuration(configJson, item.content) || 2, // 尝试查找音频时长，默认2秒
        }));

        return items;
      }
    }

    // 如果没有找到自定义序列，使用合并序列
    console.log(`未找到自定义序列，使用合并序列`);

    // 使用最后一个序列（合并序列）
    if (sequences.length > 0) {
      const mergedSequence = sequences[sequences.length - 1];
      if (
        mergedSequence &&
        mergedSequence.items &&
        mergedSequence.items.length > 0
      ) {
        console.log(`使用合并序列，项目数: ${mergedSequence.items.length}`);
        return mergedSequence.items;
      }
    }

    console.warn(`未找到任何可用序列`);
    return [];
  } else if (processingMode === "source") {
    // 基于源节点模式 - 使用源节点ID或源节点IDs
    let sourceNodeIds = [];

    // 支持新版本的sourceNodeIds数组
    if (section.sourceNodeIds && section.sourceNodeIds.length > 0) {
      sourceNodeIds = section.sourceNodeIds;
      console.log(
        `基于源节点模式，使用多个源节点ID: ${sourceNodeIds.join(", ")}`
      );
    }
    // 兼容旧版本的单个sourceNodeId
    else if (section.sourceNodeId) {
      sourceNodeIds = [section.sourceNodeId];
      console.log(`基于源节点模式，使用单个源节点ID: ${section.sourceNodeId}`);
    }

    // 如果没有源节点ID，返回错误
    if (sourceNodeIds.length === 0) {
      console.warn(`环节 ${section.name || section.title} 未指定源节点ID`);
      return [];
    }

    // 合并所有源节点的内容项
    let allSourceItems = [];

    // 处理每个源节点
    for (const sourceNodeId of sourceNodeIds) {
      // 查找源节点对应的序列
      const sourceSequence = sequences.find((seq) => seq.id === sourceNodeId);

      if (
        sourceSequence &&
        sourceSequence.items &&
        sourceSequence.items.length > 0
      ) {
        console.log(
          `找到源节点 ${sourceNodeId} 序列，项目数: ${sourceSequence.items.length}`
        );
        allSourceItems = allSourceItems.concat(sourceSequence.items);
        continue; // 已找到序列，继续处理下一个源节点
      }

      // 如果没有找到序列，尝试直接从源节点获取内容
      console.warn(
        `未找到源节点 ${sourceNodeId} 对应的序列，尝试直接从源节点获取内容`
      );

      // 查找源节点
      const sourceNode = Object.values(configJson.nodes || {}).find(
        (n) => n.id === sourceNodeId
      );

      if (!sourceNode) {
        console.warn(`未找到ID为 ${sourceNodeId} 的源节点`);
        continue; // 跳过这个源节点，继续处理下一个
      }

      // 尝试从文本内容节点获取分句
      if (
        sourceNode.type === "textContent" &&
        sourceNode.params &&
        sourceNode.params.segments &&
        sourceNode.params.segments.length > 0
      ) {
        console.log(
          `从文本内容节点获取分句数据，分句数: ${sourceNode.params.segments.length}`
        );

        // 转换为序列项目格式
        const nodeItems = sourceNode.params.segments.map((segment) => {
          // 查找音频URL和时长
          const audioUrl = findAudioUrl(configJson, segment.content);
          const audioDuration = findAudioDuration(configJson, segment.content);

          console.log(
            `分句 "${segment.content.substring(0, 20)}..." 音频时长: ${
              audioDuration || "未找到，使用默认值2秒"
            }, 类型: ${segment.type || "normal"}`
          );

          return {
            id: segment.id,
            content: segment.content,
            language: segment.language || "auto",
            type: segment.type || "normal", // 添加类型属性，默认为普通文本
            audioUrl: audioUrl,
            duration: audioDuration || 2, // 使用实际时长或默认2秒
          };
        });

        // 添加到合并结果中
        allSourceItems = allSourceItems.concat(nodeItems);
      } else if (
        sourceNode.type === "textContent" &&
        sourceNode.params &&
        sourceNode.params.text
      ) {
        // 如果没有分句但有文本，使用整个文本
        console.log(`文本内容节点没有分句，使用整个文本`);

        // 查找音频URL和时长
        const text = sourceNode.params.text;
        const audioUrl = findAudioUrl(configJson, text);
        const audioDuration = findAudioDuration(configJson, text);

        console.log(
          `文本 "${text.substring(0, 20)}..." 音频时长: ${
            audioDuration || "未找到，使用默认值2秒"
          }`
        );

        // 添加到合并结果中
        allSourceItems.push({
          id: `seg_${Date.now()}_${sourceNodeId}`,
          content: text,
          language: sourceNode.params.language || "auto",
          type: sourceNode.params.type || "normal", // 添加类型属性，默认为普通文本
          audioUrl: audioUrl,
          duration: audioDuration || 2, // 使用实际时长或默认2秒
        });
      } else {
        console.warn(`源节点 ${sourceNodeId} 不包含有效内容`);
      }
    } // 结束for循环

    if (allSourceItems.length === 0) {
      console.warn(`所有源节点都不包含有效内容`);
    } else {
      console.log(`合并后的内容项数量: ${allSourceItems.length}`);
    }

    return allSourceItems;
  }

  console.warn(`未知的处理模式: ${processingMode}`);
  return [];
}

/**
 * 查找音频URL
 * @param {Object} configJson - 内容配置JSON
 * @param {string} text - 文本内容
 * @returns {string|null} 音频URL或null
 */
function findAudioUrl(configJson, text) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 只查找精确匹配的音频项
  const audioItem = audioItems.find((item) => item.text === text);

  if (audioItem && (audioItem.url || audioItem.audioUrl)) {
    console.log(
      `找到音频URL: ${audioItem.url || audioItem.audioUrl}, 文本: ${
        text ? text.substring(0, 20) : "null"
      }...`
    );
    return audioItem.url || audioItem.audioUrl;
  }

  // 尝试通过ID查找
  if (text && text.id) {
    const idMatch = audioItems.find(
      (item) => item.id === text.id || item.sequenceId === text.id
    );
    if (idMatch && (idMatch.url || idMatch.audioUrl)) {
      console.log(
        `通过ID找到音频URL: ${idMatch.url || idMatch.audioUrl}, ID: ${text.id}`
      );
      return idMatch.url || idMatch.audioUrl;
    }
  }

  console.log(
    `未找到音频URL, 文本: ${text ? text.substring(0, 20) : "null"}...`
  );
  return null;
}

/**
 * 查找音频时长
 * @param {Object} configJson - 内容配置JSON
 * @param {string} text - 文本内容
 * @returns {number|null} 音频时长（秒）或null
 */
function findAudioDuration(configJson, text) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 只查找精确匹配的音频项
  const audioItem = audioItems.find((item) => item.text === text);

  if (audioItem && audioItem.duration) {
    console.log(
      `找到音频时长: ${audioItem.duration}秒, 文本: ${
        text ? text.substring(0, 20) : "null"
      }...`
    );
    return audioItem.duration;
  }

  // 尝试通过ID查找
  if (text && text.id) {
    const idMatch = audioItems.find(
      (item) => item.id === text.id || item.sequenceId === text.id
    );
    if (idMatch && idMatch.duration) {
      console.log(`通过ID找到音频时长: ${idMatch.duration}秒, ID: ${text.id}`);
      return idMatch.duration;
    }
  }

  console.log(
    `未找到音频时长, 使用默认值, 文本: ${
      text ? text.substring(0, 20) : "null"
    }...`
  );
  return null;
}

/**
 * 处理序列环节 - 统一的序列模式
 * @param {Array} timeline - 时间线数组
 * @param {Array} sourceItems - 源内容项数组
 * @param {Object} section - 环节设置
 * @param {string} processingMode - 处理模式
 * @param {number} startTime - 开始时间
 * @param {Object} configJson - 内容配置JSON
 */
function processSequenceSection(
  timeline,
  sourceItems,
  section,
  processingMode,
  startTime,
  configJson
) {
  console.log(
    `处理序列环节: ${section.name || section.title}, 项目数: ${
      sourceItems.length
    }, 重复次数: ${section.repeatCount || 1}, 停顿时长: ${
      section.pauseDuration || 3000
    }毫秒, 语速: ${section.speed || 1.0}x`
  );

  // 获取重复次数
  const repeatCount = section.repeatCount || 1;

  // 获取默认语速和停顿时长
  const defaultSpeed = section.speed || 1.0;
  const defaultPauseDuration = section.pauseDuration || 3000;

  // 重复速度和停顿时长数组
  let repeatSpeeds, repeatPauses;

  if (Array.isArray(section.repeatSpeeds) && section.repeatSpeeds.length > 0) {
    // 使用设置的绝对值
    repeatSpeeds = [...section.repeatSpeeds];

    // 确保数组长度与重复次数一致
    while (repeatSpeeds.length < repeatCount) {
      // 如果数组不够长，使用最后一个值填充
      repeatSpeeds.push(repeatSpeeds[repeatSpeeds.length - 1]);
    }

    // 如果数组过长，截断
    if (repeatSpeeds.length > repeatCount) {
      repeatSpeeds.length = repeatCount;
    }
  } else {
    // 使用全局设置
    repeatSpeeds = Array(repeatCount).fill(defaultSpeed);
  }

  if (Array.isArray(section.repeatPauses) && section.repeatPauses.length > 0) {
    // 使用设置的绝对值
    repeatPauses = [...section.repeatPauses];

    // 确保数组长度与重复次数一致
    while (repeatPauses.length < repeatCount) {
      // 如果数组不够长，使用最后一个值填充
      repeatPauses.push(repeatPauses[repeatPauses.length - 1]);
    }

    // 如果数组过长，截断
    if (repeatPauses.length > repeatCount) {
      repeatPauses.length = repeatCount;
    }
  } else {
    // 使用全局设置
    repeatPauses = Array(repeatCount).fill(defaultPauseDuration);
  }

  console.log("重复速度:", repeatSpeeds);
  console.log("重复停顿时长:", repeatPauses);

  // 是否启用翻译
  const enableTranslation = section.enableTranslation === true;

  // 翻译语言
  const translationLanguage = section.translationLanguage || "";

  // 翻译插入位置
  const translationPosition = section.translationPosition || 2;

  // 确保翻译插入位置有效（不超过重复次数且至少为1）
  const validTranslationPosition = Math.min(
    Math.max(1, translationPosition),
    repeatCount
  );

  // 是否插入关键词
  const insertKeywords = section.enableKeywords === true;

  // 关键词重复次数
  const keywordRepeatCount = section.keywordRepeatCount || 2;

  // 关键词插入位置
  const keywordPosition = section.keywordPosition || 2;

  // 关键词语速 - 受全局语速影响
  const baseKeywordSpeed = section.keywordSpeed || 1.0;
  const keywordSpeed = baseKeywordSpeed * (defaultSpeed || 1.0);

  // 确保关键词插入位置有效（不超过重复次数且至少为1）
  const validKeywordPosition = Math.min(
    Math.max(1, keywordPosition),
    repeatCount
  );

  let currentTime = startTime;

  // 处理重复环节
  sourceItems.forEach((item) => {
    // 检查文本类型
    const isTransitionText = item.type === "transition";

    // 如果是转场文本，只播放一次，否则重复指定次数
    const actualRepeatCount = isTransitionText ? 1 : repeatCount;

    for (let i = 0; i < actualRepeatCount; i++) {
      // 获取标注信息（如果有）
      let annotation = null;
      if (
        configJson &&
        configJson.resources &&
        configJson.resources.annotations
      ) {
        annotation = configJson.resources.annotations[item.id];
      }

      // 获取当前重复的速度和停顿时长
      const currentSpeed = repeatSpeeds[i] || defaultSpeed;
      const currentPauseDuration = repeatPauses[i] || defaultPauseDuration;

      // 添加到时间线
      timeline.push({
        id: item.id, // 保持原始ID不变
        originalId: item.id, // 明确存储原始ID
        content: item.content,
        audioUrl: item.audioUrl,
        startTime: currentTime,
        duration: (item.duration || 2) * (1 / currentSpeed),
        speed: currentSpeed,
        sectionId: section.id,
        sectionName: section.name || section.title,
        repeatIndex: i,
        isRepeat: true, // 明确标记这是重复项
        processingMode: processingMode,
        language: item.language, // 保留源项目的语言信息，不使用默认值
        annotation: annotation, // 添加标注信息
        isTransitionText: isTransitionText, // 标记是否为转场文本
        isKeyword: item.isKeyword || false, // 标记是否为关键词（关键词是分句的属性，不是类型）
      });

      // 更新当前时间
      const itemDuration = (item.duration || 2) * (1 / currentSpeed);
      currentTime += itemDuration + currentPauseDuration / 1000;

      // 如果达到指定的翻译插入位置，并且启用了翻译，添加翻译内容
      // 对于转场文本，不插入翻译
      if (
        !isTransitionText &&
        i === validTranslationPosition - 1 &&
        enableTranslation &&
        translationLanguage
      ) {
        console.log(
          `为内容 "${item.content.substring(
            0,
            20
          )}..." 添加翻译，语言: ${translationLanguage}`
        );

        // 查找翻译内容
        let translationText = null;

        // 从resources中查找翻译
        if (
          configJson.resources &&
          configJson.resources.translations &&
          configJson.resources.translations[translationLanguage]
        ) {
          const translations =
            configJson.resources.translations[translationLanguage];

          // 通过ID查找
          if (translations[item.id]) {
            translationText = translations[item.id];
          }
        }

        if (translationText) {
          console.log(`找到翻译内容: ${translationText.substring(0, 20)}...`);

          // 构造翻译音频ID
          const translationAudioId = `${item.id}_${translationLanguage}`;

          // 查找翻译音频
          const translationAudioUrl = findTranslationAudioUrl(
            configJson,
            item.id,
            translationLanguage
          );
          const translationAudioDuration = findTranslationAudioDuration(
            configJson,
            item.id,
            translationLanguage
          );

          console.log(
            `翻译音频ID: ${translationAudioId}, URL: ${
              translationAudioUrl || "未找到"
            }, 时长: ${translationAudioDuration || "未找到"}`
          );

          // 获取翻译的标注信息（如果有）
          let translationAnnotation = null;
          if (
            configJson &&
            configJson.resources &&
            configJson.resources.annotations
          ) {
            translationAnnotation =
              configJson.resources.annotations[
                `${item.id}_${translationLanguage}`
              ];
          }

          // 获取当前重复的速度和停顿时长（使用翻译位置的设置）
          const translationSpeed =
            repeatSpeeds[validTranslationPosition - 1] || defaultSpeed;
          const translationPauseDuration =
            repeatPauses[validTranslationPosition - 1] || defaultPauseDuration;

          // 添加翻译到时间线
          timeline.push({
            id: item.id, // 保持原始ID不变
            originalId: item.id, // 明确存储原始ID
            content: translationText,
            audioUrl: translationAudioUrl,
            startTime: currentTime,
            duration: translationAudioDuration || 2 * (1 / translationSpeed), // 使用实际时长或默认2秒
            speed: translationSpeed,
            sectionId: section.id,
            sectionName: section.name || section.title,
            repeatIndex: validTranslationPosition - 1, // 设置重复索引，用于停顿时长计算
            isTranslation: true,
            language: translationLanguage,
            processingMode: processingMode,
            annotation: translationAnnotation, // 添加翻译的标注信息
          });

          // 更新当前时间，使用实际时长或默认值
          const translationDuration = translationAudioDuration || 2;
          currentTime +=
            translationDuration * (1 / translationSpeed) +
            translationPauseDuration / 1000;
        } else {
          console.warn(
            `未找到内容 "${item.content.substring(0, 20)}..." 的翻译`
          );
        }
      }

      // 如果达到指定的关键词插入位置，并且启用了关键词，添加关键词内容
      // 对于转场文本，不插入关键词
      if (
        !isTransitionText &&
        i === validKeywordPosition - 1 &&
        insertKeywords &&
        (item.type === "normal" || item.type === undefined) // 对普通文本和未指定类型的文本插入关键词
      ) {
        // 查找原始分句，获取关键词列表
        const originalSegment = findSegmentById(configJson, item.id);

        // 如果有关键词，添加到时间线
        if (
          originalSegment &&
          originalSegment.keywords &&
          originalSegment.keywords.length > 0
        ) {
          console.log(
            `为内容 "${item.content.substring(0, 20)}..." 添加${
              originalSegment.keywords.length
            }个关键词`
          );

          // 处理每个关键词
          for (const keyword of originalSegment.keywords) {
            const keywordText = keyword.text;
            const keywordId = keyword.id;
            const keywordLanguage = keyword.language || item.language;

            console.log(`处理关键词: ${keywordText}, ID: ${keywordId}`);

            // 查找关键词音频
            const keywordAudioUrl = findKeywordAudioUrl(
              configJson,
              keywordId,
              keywordLanguage
            );
            const keywordAudioDuration = findKeywordAudioDuration(
              configJson,
              keywordId,
              keywordLanguage
            );

            // 获取关键词的标注信息（如果有）
            let keywordAnnotation = null;
            if (
              configJson &&
              configJson.resources &&
              configJson.resources.annotations
            ) {
              keywordAnnotation = configJson.resources.annotations[keywordId];
              console.log(
                `关键词 "${keywordText}" (${keywordLanguage}) 的标注信息:`,
                keywordAnnotation
              );
            }

            // 重复播放关键词
            for (let k = 0; k < keywordRepeatCount; k++) {
              // 添加关键词到时间线
              timeline.push({
                id: `${item.id}_keyword_${keywordId}_${k}`,
                originalId: item.id,
                keywordId: keywordId,
                content: keywordText,
                audioUrl: keywordAudioUrl || "", // 使用关键词的音频URL，如果没有则为空
                startTime: currentTime,
                duration: (keywordAudioDuration || 1) * (1 / keywordSpeed),
                speed: keywordSpeed,
                sectionId: section.id,
                sectionName: section.name || section.title,
                repeatIndex: validKeywordPosition - 1, // 设置重复索引，用于停顿时长计算
                isKeyword: true, // 标记为关键词
                keywordIndex: k,
                language: keywordLanguage,
                processingMode: processingMode,
                annotation: keywordAnnotation, // 使用关键词的标注信息
              });

              // 更新当前时间
              // 获取当前重复的停顿时长（使用关键词位置的设置）
              const keywordPauseDuration =
                repeatPauses[validKeywordPosition - 1] || defaultPauseDuration;
              currentTime +=
                (keywordAudioDuration || 1) * (1 / keywordSpeed) +
                keywordPauseDuration / 2 / 1000; // 关键词之间的停顿时间为当前重复停顿时间的一半
            }
          }
        } else {
          console.log(`内容 "${item.content.substring(0, 20)}..." 没有关键词`);
        }
      }
    }
  });
}

// 旧的处理函数已删除，统一使用processSequenceSection

/**
 * 查找关键词音频URL
 * @param {Object} configJson - 内容配置JSON
 * @param {string} keywordId - 关键词ID
 * @param {string} language - 语言
 * @returns {string|null} 关键词音频URL或null
 */
function findKeywordAudioUrl(configJson, keywordId, language) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 查找关键词音频项
  const idMatch = audioItems.find(
    (item) =>
      item.id === keywordId ||
      (item.keywordId === keywordId && item.language === language)
  );

  if (idMatch && (idMatch.url || idMatch.audioUrl)) {
    console.log(
      `通过ID找到关键词音频URL: ${
        idMatch.url || idMatch.audioUrl
      }, ID: ${keywordId}`
    );
    return idMatch.url || idMatch.audioUrl;
  }

  console.log(`未找到关键词音频URL, ID: ${keywordId}, 语言: ${language}`);
  return null;
}

/**
 * 查找关键词音频时长
 * @param {Object} configJson - 内容配置JSON
 * @param {string} keywordId - 关键词ID
 * @param {string} language - 语言
 * @returns {number|null} 关键词音频时长（秒）或null
 */
function findKeywordAudioDuration(configJson, keywordId, language) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 查找关键词音频项
  const idMatch = audioItems.find(
    (item) =>
      item.id === keywordId ||
      (item.keywordId === keywordId && item.language === language)
  );

  if (idMatch && idMatch.duration) {
    console.log(
      `通过ID找到关键词音频时长: ${idMatch.duration}秒, ID: ${keywordId}`
    );
    return idMatch.duration;
  }

  console.log(`未找到关键词音频时长, ID: ${keywordId}, 语言: ${language}`);
  return null;
}

/**
 * 查找翻译音频URL
 * @param {Object} configJson - 内容配置JSON
 * @param {string} sourceId - 源内容ID
 * @param {string} language - 翻译语言
 * @returns {string|null} 翻译音频URL或null
 */
function findTranslationAudioUrl(configJson, sourceId, language) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 查找翻译音频项
  // 1. 通过ID匹配: sourceId_language
  const translationId = `${sourceId}_${language}`;
  const idMatch = audioItems.find(
    (item) =>
      item.id === translationId ||
      (item.sourceId === sourceId && item.targetLanguage === language) ||
      (item.audioType === "translation" &&
        item.sourceId === sourceId &&
        item.language === language)
  );

  if (idMatch && (idMatch.url || idMatch.audioUrl)) {
    console.log(
      `通过ID找到翻译音频URL: ${
        idMatch.url || idMatch.audioUrl
      }, ID: ${translationId}`
    );
    return idMatch.url || idMatch.audioUrl;
  }

  console.log(`未找到翻译音频URL, 源ID: ${sourceId}, 语言: ${language}`);
  return null;
}

/**
 * 查找翻译音频时长
 * @param {Object} configJson - 内容配置JSON
 * @param {string} sourceId - 源内容ID
 * @param {string} language - 翻译语言
 * @returns {number|null} 翻译音频时长（秒）或null
 */
function findTranslationAudioDuration(configJson, sourceId, language) {
  if (
    !configJson ||
    !configJson.resources ||
    !configJson.resources.audioItems
  ) {
    return null;
  }

  const audioItems = configJson.resources.audioItems;
  if (!Array.isArray(audioItems)) {
    return null;
  }

  // 查找翻译音频项
  // 1. 通过ID匹配: sourceId_language
  const translationId = `${sourceId}_${language}`;
  const idMatch = audioItems.find(
    (item) =>
      item.id === translationId ||
      (item.sourceId === sourceId && item.targetLanguage === language) ||
      (item.audioType === "translation" &&
        item.sourceId === sourceId &&
        item.language === language)
  );

  if (idMatch && idMatch.duration) {
    console.log(
      `通过ID找到翻译音频时长: ${idMatch.duration}秒, ID: ${translationId}`
    );
    return idMatch.duration;
  }

  console.log(`未找到翻译音频时长, 源ID: ${sourceId}, 语言: ${language}`);
  return null;
}

/**
 * 根据ID查找分句
 * @param {Object} configJson - 内容配置JSON
 * @param {string} segmentId - 分句ID
 * @returns {Object|null} 分句对象或null
 */
function findSegmentById(configJson, segmentId) {
  if (!configJson || !configJson.nodes) {
    return null;
  }

  console.log(`查找分句ID: ${segmentId}`);

  // 遍历所有节点
  for (const nodeId in configJson.nodes) {
    const node = configJson.nodes[nodeId];

    // 检查是否是文本内容节点
    // 首先检查 node.params.segments (新版本)
    if (node.type === "textContent" && node.params && node.params.segments) {
      // 查找匹配ID的分句
      const segment = node.params.segments.find((seg) => seg.id === segmentId);
      if (segment) {
        console.log(`在node.params.segments中找到分句:`, segment);
        return segment;
      }
    }

    // 然后检查 node.data.segments (旧版本)
    if (node.type === "textContent" && node.data && node.data.segments) {
      // 查找匹配ID的分句
      const segment = node.data.segments.find((seg) => seg.id === segmentId);
      if (segment) {
        console.log(`在node.data.segments中找到分句:`, segment);
        return segment;
      }
    }
  }

  console.log(`未找到分句ID: ${segmentId}`);
  return null;
}

/**
 * 计算总时长
 * @param {Array} timeline - 时间线数组
 * @returns {number} 总时长（秒）
 */
export function calculateTotalDuration(timeline) {
  if (!timeline || timeline.length === 0) return 0;

  const lastItem = timeline[timeline.length - 1];
  return lastItem.startTime + lastItem.duration;
}
