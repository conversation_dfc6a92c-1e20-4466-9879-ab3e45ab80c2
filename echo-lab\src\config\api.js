/**
 * API配置
 * 定义所有API端点和相关配置
 */

// API基础URL
export const API_BASE_URL = "";

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    BASE: "/api/auth",
    SEND_CODE: "/api/auth/send-code",
    VERIFY_CODE: "/api/auth/verify-code",
    CURRENT_USER: "/api/auth/me",
    UPDATE_USER: "/api/auth/me",
    LOGOUT: "/api/auth/logout",
  },

  // 内容管理
  CONTENTS: {
    BASE: "/api/contents",
    PUBLIC: "/api/public/contents",
    HOT: "/api/public/contents/hot",
    LATEST: "/api/public/contents/latest",
    BY_TAG: "/api/public/contents/tag",
    SEARCH: "/api/public/contents/search",
  },

  // 文本处理
  TEXT_PROCESSING: {
    ANNOTATE: "/api/annotate",
    TRANSLATE: "/api/translate",
  },

  // 音频服务
  AUDIO: {
    TTS_BATCH: "/api/tts/batch",
    TTS_INFO: "/api/tts/info",
  },

  // 特殊词汇
  SPECIAL_WORDS: {
    BASE: "/api/special-words",
  },

  // 图片服务
  IMAGES: {
    BASE: "/api/images",
    UPLOAD: "/api/images/upload",
  },

  // 会话管理
  SESSIONS: {
    BASE: "/api/sessions",
  },

  // 功能权限
  PERMISSIONS: {
    FEATURES: "/api/feature-permissions/features",
    CHECK: "/api/feature-permissions/check",
    ME: "/api/permissions/me",
    USAGE: "/api/permissions/usage",
    ADMIN_BASE: "/api/admin/feature-permissions",
  },

  // 用户等级
  USER_LEVELS: {
    BASE: "/api/user-levels",
    ME: "/api/user-levels/me",
    USERS: "/api/user-levels/users",
    SUBSCRIPTIONS: "/api/user-levels/subscriptions",
  },

  // 用户反馈
  FEEDBACK: {
    BASE: "/api/feedback",
    USER: "/api/feedback/user",
    ADMIN: "/api/feedback/admin",
  },

  // 播放策略模板
  TEMPLATES: {
    BASE: "/api/templates",
    USE: (id) => `/api/templates/${id}/use`,
    DUPLICATE: (id) => `/api/templates/${id}/duplicate`,
  },

  // 收藏管理
  FAVORITES: {
    BASE: "/api/favorites",
    CHECK: (id) => `/api/favorites/check/${id}`,
  },

  // 管理员功能
  ADMIN: {
    BASE: "/api/admin",
    USERS: "/api/admin/users",
    STATISTICS: "/api/admin/statistics",
    LEVELS: "/api/admin/user-levels",
    SUBSCRIPTIONS: "/api/admin/subscriptions",
  },
};

// 兼容性：保留旧的常量（逐步废弃）
export const CONTENTS_API = API_ENDPOINTS.CONTENTS.BASE;
export const PUBLIC_CONTENTS_API = API_ENDPOINTS.CONTENTS.PUBLIC;
export const ANNOTATE_API = API_ENDPOINTS.TEXT_PROCESSING.ANNOTATE;
export const TRANSLATE_API = API_ENDPOINTS.TEXT_PROCESSING.TRANSLATE;
export const TTS_API = "/api/tts";
export const TTS_INFO_API = API_ENDPOINTS.AUDIO.TTS_INFO;
export const SPECIAL_WORDS_API = API_ENDPOINTS.SPECIAL_WORDS.BASE;
export const IMAGES_API = API_ENDPOINTS.IMAGES.BASE;
export const AUTH_API = API_ENDPOINTS.AUTH.BASE;
export const SESSIONS_API = API_ENDPOINTS.SESSIONS.BASE;
export const ADMIN_API = API_ENDPOINTS.ADMIN.BASE;
export const FEATURE_PERMISSIONS_API = "/api/feature-permissions";
export const ADMIN_FEATURE_PERMISSIONS_API =
  API_ENDPOINTS.PERMISSIONS.ADMIN_BASE;
export const FEEDBACK_API = API_ENDPOINTS.FEEDBACK.BASE;
export const TEMPLATES_API = API_ENDPOINTS.TEMPLATES.BASE;

export default {
  API_BASE_URL,
  API_ENDPOINTS,
  // 兼容性导出
  CONTENTS_API,
  PUBLIC_CONTENTS_API,
  ANNOTATE_API,
  TRANSLATE_API,
  TTS_API,
  TTS_INFO_API,
  SPECIAL_WORDS_API,
  IMAGES_API,
  AUTH_API,
  SESSIONS_API,
  ADMIN_API,
  FEATURE_PERMISSIONS_API,
  ADMIN_FEATURE_PERMISSIONS_API,
  FEEDBACK_API,
  TEMPLATES_API,
};
