/**
 * 音频缓存工具
 * 使用localStorage缓存音频数据，减少网络请求
 */

// 缓存前缀，用于区分不同类型的缓存
const CACHE_PREFIX = 'echo_lab_audio_';

// 缓存过期时间（毫秒），默认7天
const CACHE_EXPIRATION = 7 * 24 * 60 * 60 * 1000;

/**
 * 获取缓存键
 * @param {string} url - 音频URL
 * @returns {string} 缓存键
 */
function getCacheKey(url) {
  return `${CACHE_PREFIX}${url}`;
}

/**
 * 获取缓存项的元数据键
 * @param {string} url - 音频URL
 * @returns {string} 元数据缓存键
 */
function getMetaKey(url) {
  return `${CACHE_PREFIX}meta_${url}`;
}

/**
 * 保存音频数据到缓存
 * @param {string} url - 音频URL
 * @param {ArrayBuffer} data - 音频数据
 * @returns {boolean} 是否成功保存
 */
export function cacheAudioData(url, data) {
  try {
    // 将ArrayBuffer转换为Base64字符串
    const base64Data = arrayBufferToBase64(data);
    
    // 保存音频数据
    localStorage.setItem(getCacheKey(url), base64Data);
    
    // 保存元数据（时间戳）
    const metadata = {
      timestamp: Date.now(),
      size: data.byteLength
    };
    localStorage.setItem(getMetaKey(url), JSON.stringify(metadata));
    
    console.log(`音频缓存成功: ${url}, 大小: ${(data.byteLength / 1024).toFixed(2)} KB`);
    return true;
  } catch (error) {
    console.error(`音频缓存失败: ${url}`, error);
    
    // 如果是存储空间不足，清理部分缓存
    if (error.name === 'QuotaExceededError') {
      cleanOldCache();
    }
    
    return false;
  }
}

/**
 * 从缓存获取音频数据
 * @param {string} url - 音频URL
 * @returns {ArrayBuffer|null} 音频数据或null（如果缓存不存在）
 */
export function getAudioFromCache(url) {
  try {
    // 获取缓存数据
    const cachedData = localStorage.getItem(getCacheKey(url));
    if (!cachedData) {
      return null;
    }
    
    // 获取元数据
    const metaString = localStorage.getItem(getMetaKey(url));
    if (!metaString) {
      // 如果没有元数据，删除缓存数据并返回null
      localStorage.removeItem(getCacheKey(url));
      return null;
    }
    
    // 解析元数据
    const metadata = JSON.parse(metaString);
    
    // 检查缓存是否过期
    if (Date.now() - metadata.timestamp > CACHE_EXPIRATION) {
      // 删除过期缓存
      localStorage.removeItem(getCacheKey(url));
      localStorage.removeItem(getMetaKey(url));
      return null;
    }
    
    // 将Base64字符串转换回ArrayBuffer
    const arrayBuffer = base64ToArrayBuffer(cachedData);
    
    console.log(`从缓存获取音频: ${url}, 大小: ${(arrayBuffer.byteLength / 1024).toFixed(2)} KB`);
    return arrayBuffer;
  } catch (error) {
    console.error(`从缓存获取音频失败: ${url}`, error);
    return null;
  }
}

/**
 * 清理过期缓存
 * @returns {number} 清理的缓存数量
 */
export function cleanExpiredCache() {
  let cleanedCount = 0;
  
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出音频缓存的元数据键
    const metaKeys = keys.filter(key => key.startsWith(`${CACHE_PREFIX}meta_`));
    
    // 当前时间
    const now = Date.now();
    
    // 检查每个缓存项
    metaKeys.forEach(metaKey => {
      try {
        // 解析元数据
        const metadata = JSON.parse(localStorage.getItem(metaKey));
        
        // 检查是否过期
        if (now - metadata.timestamp > CACHE_EXPIRATION) {
          // 获取URL
          const url = metaKey.replace(`${CACHE_PREFIX}meta_`, '');
          
          // 删除缓存数据和元数据
          localStorage.removeItem(getCacheKey(url));
          localStorage.removeItem(metaKey);
          
          cleanedCount++;
        }
      } catch (error) {
        console.error(`清理缓存项失败: ${metaKey}`, error);
      }
    });
    
    console.log(`清理了 ${cleanedCount} 个过期缓存项`);
    return cleanedCount;
  } catch (error) {
    console.error('清理过期缓存失败', error);
    return cleanedCount;
  }
}

/**
 * 清理最旧的缓存项，直到释放指定大小的空间
 * @param {number} targetSize - 目标释放空间大小（字节）
 * @returns {number} 清理的缓存数量
 */
export function cleanOldCache(targetSize = 10 * 1024 * 1024) {
  let cleanedCount = 0;
  let freedSpace = 0;
  
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出音频缓存的元数据键
    const metaKeys = keys.filter(key => key.startsWith(`${CACHE_PREFIX}meta_`));
    
    // 获取所有缓存项的元数据
    const cacheItems = metaKeys.map(metaKey => {
      try {
        const metadata = JSON.parse(localStorage.getItem(metaKey));
        const url = metaKey.replace(`${CACHE_PREFIX}meta_`, '');
        return {
          url,
          metaKey,
          dataKey: getCacheKey(url),
          timestamp: metadata.timestamp,
          size: metadata.size || 0
        };
      } catch (error) {
        return null;
      }
    }).filter(Boolean);
    
    // 按时间戳排序（最旧的在前）
    cacheItems.sort((a, b) => a.timestamp - b.timestamp);
    
    // 清理最旧的缓存项，直到释放足够空间
    for (const item of cacheItems) {
      if (freedSpace >= targetSize) {
        break;
      }
      
      // 删除缓存数据和元数据
      localStorage.removeItem(item.dataKey);
      localStorage.removeItem(item.metaKey);
      
      freedSpace += item.size;
      cleanedCount++;
    }
    
    console.log(`清理了 ${cleanedCount} 个最旧的缓存项，释放了 ${(freedSpace / 1024 / 1024).toFixed(2)} MB 空间`);
    return cleanedCount;
  } catch (error) {
    console.error('清理最旧缓存失败', error);
    return cleanedCount;
  }
}

/**
 * 清理所有音频缓存
 * @returns {number} 清理的缓存数量
 */
export function clearAllAudioCache() {
  let cleanedCount = 0;
  
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出音频缓存的键
    const cacheKeys = keys.filter(key => key.startsWith(CACHE_PREFIX));
    
    // 删除所有缓存项
    cacheKeys.forEach(key => {
      localStorage.removeItem(key);
      cleanedCount++;
    });
    
    console.log(`清理了所有 ${cleanedCount} 个音频缓存项`);
    return cleanedCount;
  } catch (error) {
    console.error('清理所有音频缓存失败', error);
    return cleanedCount;
  }
}

/**
 * 获取缓存统计信息
 * @returns {Object} 缓存统计信息
 */
export function getAudioCacheStats() {
  try {
    // 获取所有localStorage键
    const keys = Object.keys(localStorage);
    
    // 筛选出音频缓存的元数据键
    const metaKeys = keys.filter(key => key.startsWith(`${CACHE_PREFIX}meta_`));
    
    // 计算总大小和数量
    let totalSize = 0;
    let itemCount = metaKeys.length;
    let oldestTimestamp = Date.now();
    let newestTimestamp = 0;
    
    metaKeys.forEach(metaKey => {
      try {
        const metadata = JSON.parse(localStorage.getItem(metaKey));
        totalSize += metadata.size || 0;
        
        if (metadata.timestamp < oldestTimestamp) {
          oldestTimestamp = metadata.timestamp;
        }
        
        if (metadata.timestamp > newestTimestamp) {
          newestTimestamp = metadata.timestamp;
        }
      } catch (error) {
        console.error(`获取缓存项元数据失败: ${metaKey}`, error);
      }
    });
    
    return {
      count: itemCount,
      totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
      oldestDate: new Date(oldestTimestamp).toISOString(),
      newestDate: new Date(newestTimestamp).toISOString()
    };
  } catch (error) {
    console.error('获取缓存统计信息失败', error);
    return {
      count: 0,
      totalSize: 0,
      totalSizeMB: 0,
      oldestDate: null,
      newestDate: null
    };
  }
}

/**
 * 将ArrayBuffer转换为Base64字符串
 * @param {ArrayBuffer} buffer - 要转换的ArrayBuffer
 * @returns {string} Base64字符串
 */
function arrayBufferToBase64(buffer) {
  let binary = '';
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  
  return window.btoa(binary);
}

/**
 * 将Base64字符串转换为ArrayBuffer
 * @param {string} base64 - 要转换的Base64字符串
 * @returns {ArrayBuffer} ArrayBuffer
 */
function base64ToArrayBuffer(base64) {
  const binaryString = window.atob(base64);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return bytes.buffer;
}

// 初始化时清理过期缓存
cleanExpiredCache();
