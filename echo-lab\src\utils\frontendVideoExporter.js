/**
 * 前端视频导出统一接口
 * 集成WebCodecs和现有导出逻辑，完全替代后端处理
 */

import { webCodecsExporter } from "./webCodecsVideoExporter.js";
import { VIDEO_PHASES } from "@/config/video.js";

/**
 * 前端视频导出器
 * 使用WebCodecs API进行完全的前端视频合成
 */
export class FrontendVideoExporter {
  constructor() {
    this.isSupported = webCodecsExporter.isSupported;
  }

  /**
   * 导出视频（完全前端处理）
   * @param {Array} timeline - 时间线数据
   * @param {AudioBuffer} audioBuffer - 音频缓冲区
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async exportVideo(timeline, audioBuffer, renderContent, options = {}) {
    if (!this.isSupported) {
      throw new Error("当前浏览器不支持前端视频合成，请使用Chrome 94+");
    }

    const {
      videoQuality = "medium",
      fileName = "echo_lab_video",
      generateSubtitles = false,
      subtitleLanguages = [],
      configJson = null,
      containerWidth = null,
      containerHeight = null,
      onProgress = null,
      signal = null,
    } = options;

    // 质量预设映射 - 与AVC Level兼容
    const qualityPresets = {
      low: { width: 640, height: 480 }, // H.264 Level 3.0
      medium: { width: 1280, height: 720 }, // H.264 Level 3.1
      high: { width: 1920, height: 1080 }, // H.264 Level 4.0
    };

    const preset = qualityPresets[videoQuality] || qualityPresets.medium;

    try {
      // 使用WebCodecs导出器
      const result = await webCodecsExporter.exportVideo(
        timeline,
        audioBuffer,
        renderContent,
        {
          width: preset.width,
          height: preset.height,
          frameRate: 30,
          quality: videoQuality,
          fileName,
          generateSubtitles,
          subtitleLanguages,
          configJson,
          // 传递容器尺寸信息
          containerWidth,
          containerHeight,
          onProgress: (progress) => {
            // 转换进度格式以兼容现有UI
            if (onProgress) {
              onProgress({
                phase: this._mapPhase(progress.phase),
                progress: progress.progress,
              });
            }
          },
          signal,
        }
      );

      return result;
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("前端视频导出被取消");
        throw error;
      }

      console.error("前端视频导出失败:", error);
      throw new Error(`视频导出失败: ${error.message}`);
    }
  }

  /**
   * 映射进度阶段到现有的VIDEO_PHASES
   */
  _mapPhase(phase) {
    const phaseMap = {
      "generating-frames": VIDEO_PHASES.GENERATING_FRAMES,
      "initializing-encoder": VIDEO_PHASES.PREPARING_UPLOAD,
      "encoding-video": VIDEO_PHASES.PROCESSING_VIDEO,
      "encoding-audio": VIDEO_PHASES.PROCESSING_VIDEO,
      finalizing: VIDEO_PHASES.DOWNLOADING,
      "generating-subtitles": VIDEO_PHASES.DOWNLOADING,
      complete: VIDEO_PHASES.COMPLETE,
    };

    return phaseMap[phase] || phase;
  }

  /**
   * 下载导出的视频和字幕
   */
  async downloadResult(result) {
    const { videoBlob, fileName, subtitleFiles = [] } = result;

    if (subtitleFiles.length > 0) {
      // 如果有字幕文件，打包下载
      await webCodecsExporter.downloadVideoWithSubtitles(
        videoBlob,
        fileName,
        subtitleFiles
      );
    } else {
      // 只下载视频文件
      webCodecsExporter.downloadVideo(videoBlob, fileName);
    }
  }

  /**
   * 检查浏览器支持
   */
  checkSupport() {
    return {
      isSupported: this.isSupported,
      message: this.isSupported
        ? "支持前端视频合成"
        : "当前浏览器不支持WebCodecs API，请使用Chrome 94+",
    };
  }
}

// 创建单例实例
export const frontendVideoExporter = new FrontendVideoExporter();

/**
 * 兼容现有接口的导出函数
 * 可以直接替换现有的后端导出逻辑
 */
export async function exportVideoFrontend(
  timeline,
  audioBuffer,
  renderContent,
  options = {}
) {
  return await frontendVideoExporter.exportVideo(
    timeline,
    audioBuffer,
    renderContent,
    options
  );
}

/**
 * 检查是否应该使用前端导出
 * 根据浏览器支持情况决定
 */
export function shouldUseFrontendExport() {
  return frontendVideoExporter.isSupported;
}

/**
 * 获取导出方式信息
 */
export function getExportInfo() {
  if (frontendVideoExporter.isSupported) {
    return {
      method: "frontend",
      description: "前端视频合成（WebCodecs）",
      advantages: [
        "无需上传大量数据",
        "节省流量费用",
        "处理速度更快",
        "完全本地处理",
      ],
    };
  } else {
    return {
      method: "unsupported",
      description: "不支持前端合成",
      requirements: "Chrome 94+ 浏览器",
    };
  }
}
