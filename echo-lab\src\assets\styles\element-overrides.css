/**
 * Element Plus 样式覆盖
 * 用于覆盖 Element Plus 默认样式
 */

/* Tab 样式覆盖 */
.el-tabs__item {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.el-tabs__item.is-active {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.el-tabs__item:focus {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}

.el-tabs__active-bar {
  height: 2px !important;
}

/*
 * 注意：全局样式覆盖应该谨慎使用，只在确实需要时才添加
 * 大多数情况下，应该优先使用组件的scoped样式
 */
