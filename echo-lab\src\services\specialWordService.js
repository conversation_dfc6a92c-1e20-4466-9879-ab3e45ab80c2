/**
 * 特殊词汇服务
 * 提供特殊词汇的管理功能，包括本地存储和API调用
 */
import httpClient from "@/utils/httpClient";
import { API_ENDPOINTS } from "@/config/api";

// 本地存储键名
const STORAGE_KEY = "echolab_special_words";

// 全局特殊词汇缓存 - 存储所有特殊词汇数据
let allSpecialWordsCache = null;

// 特殊词汇缓存 - 用于按服务和语言过滤的数据
let specialWordsCache = {
  timestamp: 0,
  data: {},
};

/**
 * 获取特殊词汇列表
 * @param {string} serviceId 可选的TTS服务ID
 * @param {string} languageCode 可选的语言代码
 * @returns {Promise<Object>} 特殊词汇列表，按服务和语言分组
 */
export async function getSpecialWords(serviceId = null, languageCode = null) {
  try {
    // 如果已有缓存且不需要过滤，直接返回缓存
    if (allSpecialWordsCache && !serviceId && !languageCode) {
      console.log("[特殊词汇] 使用全局缓存的特殊词汇数据");
      return allSpecialWordsCache;
    }

    // 构建查询参数
    const params = {};
    if (serviceId) {
      params.service_id = serviceId;
    }
    if (languageCode) {
      params.language_code = languageCode;
    }

    // 调用API获取特殊词汇列表
    console.log("[特殊词汇] 从API获取特殊词汇列表");
    const response = await httpClient.get(
      API_ENDPOINTS.SPECIAL_WORDS.BASE,
      params
    );
    const data = response.data || {};

    // 如果是获取所有特殊词汇，更新全局缓存
    if (!serviceId && !languageCode) {
      console.log("[特殊词汇] 更新全局特殊词汇缓存");
      allSpecialWordsCache = data;
    }

    return data;
  } catch (error) {
    console.error("获取特殊词汇列表失败:", error);
    // 如果有缓存，在出错时返回缓存
    if (allSpecialWordsCache && !serviceId && !languageCode) {
      console.log("[特殊词汇] API请求失败，使用缓存数据");
      return allSpecialWordsCache;
    }
    return {};
  }
}

/**
 * 添加特殊词汇
 * @param {string} word 特殊词汇
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 * @returns {Promise<Object>} 添加结果
 */
export async function addSpecialWord(word, serviceId, languageCode) {
  try {
    const response = await api.post("/api/special-words", {
      word,
      service_id: serviceId,
      language_code: languageCode,
    });
    return response.data;
  } catch (error) {
    console.error("添加特殊词汇失败:", error);
    throw error;
  }
}

/**
 * 删除特殊词汇
 * @param {number} id 特殊词汇ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteSpecialWord(id) {
  try {
    const response = await api.delete(`/api/special-words/${id}`);
    return response.data;
  } catch (error) {
    console.error("删除特殊词汇失败:", error);
    throw error;
  }
}

/**
 * 批量添加特殊词汇
 * @param {Array<Object>} words 特殊词汇数组，每个元素包含word、service_id和language_code
 * @returns {Promise<Object>} 添加结果
 */
export async function batchAddSpecialWords(words) {
  try {
    const response = await api.post("/api/special-words/batch", { words });
    return response.data;
  } catch (error) {
    console.error("批量添加特殊词汇失败:", error);
    throw error;
  }
}

/**
 * 保存本地特殊词汇
 * @param {Object} words 特殊词汇对象，按服务和语言分组
 */
export function saveLocalSpecialWords(words) {
  try {
    console.log(`[特殊词汇] 保存本地特殊词汇:`, words);

    // 确保words是有效的对象
    if (typeof words !== "object" || words === null) {
      console.error(`[特殊词汇] 无效的特殊词汇对象:`, words);
      return false;
    }

    // 保存到本地存储
    localStorage.setItem(STORAGE_KEY, JSON.stringify(words));
    console.log(`[特殊词汇] 本地特殊词汇保存成功`);

    // 清除所有缓存，确保下次获取时重新加载
    console.log(`[特殊词汇] 清除所有缓存`);
    allSpecialWordsCache = null;
    specialWordsCache.timestamp = 0;
    specialWordsCache.data = {};

    return true;
  } catch (error) {
    console.error("保存本地特殊词汇失败:", error);
    return false;
  }
}

/**
 * 获取本地特殊词汇
 * @returns {Object} 特殊词汇对象，按服务和语言分组
 */
export function getLocalSpecialWords() {
  try {
    const data = localStorage.getItem(STORAGE_KEY);
    const words = data ? JSON.parse(data) : {};

    console.log(`[特殊词汇] 从本地存储获取特殊词汇:`, words);

    // 确保返回的是有效的对象
    return typeof words === "object" && words !== null ? words : {};
  } catch (error) {
    console.error("获取本地特殊词汇失败:", error);
    return {};
  }
}

/**
 * 添加本地特殊词汇
 * @param {string} word 特殊词汇
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 */
export function addLocalSpecialWord(word, serviceId, languageCode) {
  try {
    console.log(
      `[特殊词汇] 添加本地特殊词汇: "${word}", 服务ID: ${serviceId}, 语言: ${languageCode}`
    );

    const words = getLocalSpecialWords();

    // 确保服务和语言的分组存在
    if (!words[serviceId]) {
      words[serviceId] = {};
    }
    if (!words[serviceId][languageCode]) {
      words[serviceId][languageCode] = [];
    }

    // 检查是否已存在
    if (!words[serviceId][languageCode].includes(word)) {
      console.log(`[特殊词汇] 特殊词汇 "${word}" 不存在，添加到本地存储`);
      words[serviceId][languageCode].push(word);
      saveLocalSpecialWords(words);

      // 清除所有缓存
      console.log(`[特殊词汇] 清除所有缓存`);
      allSpecialWordsCache = null;
      specialWordsCache.timestamp = 0;
      specialWordsCache.data = {};

      return true;
    } else {
      console.log(`[特殊词汇] 特殊词汇 "${word}" 已存在，不需要添加`);
      return false;
    }
  } catch (error) {
    console.error("添加本地特殊词汇失败:", error);
    return false;
  }
}

/**
 * 删除本地特殊词汇
 * @param {string} word 特殊词汇
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 */
export function deleteLocalSpecialWord(word, serviceId, languageCode) {
  try {
    console.log(
      `[特殊词汇] 删除本地特殊词汇: "${word}", 服务ID: ${serviceId}, 语言: ${languageCode}`
    );

    const words = getLocalSpecialWords();

    // 检查分组是否存在
    if (words[serviceId] && words[serviceId][languageCode]) {
      // 处理字符串数组
      if (Array.isArray(words[serviceId][languageCode])) {
        // 查找索引
        const index = words[serviceId][languageCode].indexOf(word);

        // 如果找到了词汇，删除它
        if (index !== -1) {
          console.log(
            `[特殊词汇] 在本地存储中找到特殊词汇 "${word}" 的索引: ${index}`
          );
          words[serviceId][languageCode].splice(index, 1);
          saveLocalSpecialWords(words);
          console.log(`[特殊词汇] 已从本地存储中删除特殊词汇 "${word}"`);

          // 清除所有缓存
          console.log(`[特殊词汇] 清除所有缓存`);
          allSpecialWordsCache = null;
          specialWordsCache.timestamp = 0;
          specialWordsCache.data = {};

          return true;
        } else {
          // 检查是否有对象格式的词汇
          for (let i = 0; i < words[serviceId][languageCode].length; i++) {
            const item = words[serviceId][languageCode][i];
            if (typeof item === "object" && item.word === word) {
              console.log(
                `[特殊词汇] 在本地存储中找到对象格式的特殊词汇 "${word}" 的索引: ${i}`
              );
              words[serviceId][languageCode].splice(i, 1);
              saveLocalSpecialWords(words);
              console.log(`[特殊词汇] 已从本地存储中删除特殊词汇 "${word}"`);

              // 清除所有缓存
              console.log(`[特殊词汇] 清除所有缓存`);
              allSpecialWordsCache = null;
              specialWordsCache.timestamp = 0;
              specialWordsCache.data = {};

              return true;
            }
          }
        }
      }
    }

    console.log(`[特殊词汇] 未在本地存储中找到特殊词汇 "${word}"`);
    return false;
  } catch (error) {
    console.error("删除本地特殊词汇失败:", error);
    return false;
  }
}

/**
 * 清空本地特殊词汇
 */
export function clearLocalSpecialWords() {
  try {
    localStorage.removeItem(STORAGE_KEY);

    // 清除所有缓存
    console.log(`[特殊词汇] 清除所有缓存`);
    allSpecialWordsCache = null;
    specialWordsCache.timestamp = 0;
    specialWordsCache.data = {};
  } catch (error) {
    console.error("清空本地特殊词汇失败:", error);
  }
}

/**
 * 获取特殊词汇列表
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 * @returns {Promise<Array>} 特殊词汇列表
 */
export async function getSpecialWordsList(serviceId, languageCode) {
  try {
    console.log(
      `[特殊词汇] 开始获取特殊词汇列表 - 服务ID: ${serviceId}, 语言: ${languageCode}`
    );

    // 检查缓存是否有效
    const cacheKey = `${serviceId}_${languageCode}`;

    if (specialWordsCache.data[cacheKey]) {
      console.log(`[特殊词汇] 使用缓存的特殊词汇列表`);
      return specialWordsCache.data[cacheKey];
    }

    // 尝试从全局缓存中提取数据
    if (allSpecialWordsCache) {
      console.log(`[特殊词汇] 从全局缓存中提取特定服务和语言的特殊词汇`);

      // 从全局缓存中提取特定服务和语言的特殊词汇
      const serviceWords = allSpecialWordsCache[serviceId] || {};
      const apiWordsList = serviceWords[languageCode] || [];

      // 获取本地特殊词汇
      const localWords = getLocalSpecialWords();
      const localServiceWords = localWords[serviceId] || {};
      const localWordsList = localServiceWords[languageCode] || [];

      // 合并API和本地特殊词汇
      const wordMap = new Map();

      // 添加API词汇
      apiWordsList.forEach((item) => {
        wordMap.set(item.word, {
          ...item,
          isLocal: false,
        });
      });

      // 添加本地词汇
      localWordsList.forEach((word) => {
        const key = typeof word === "string" ? word : word.word;
        const existing = wordMap.get(key);
        if (!existing || !existing.isSystem) {
          wordMap.set(key, {
            word: key,
            isSystem: false,
            isLocal: true,
            ...(typeof word === "object" ? word : {}),
          });
        }
      });

      // 转换回数组
      const mergedWordsList = Array.from(wordMap.values());

      // 缓存结果
      if (!specialWordsCache.data) {
        specialWordsCache.data = {};
      }
      specialWordsCache.data[cacheKey] = mergedWordsList;

      return mergedWordsList;
    }

    // 如果没有全局缓存，则从API获取
    console.log(`[特殊词汇] 没有全局缓存，从API获取特殊词汇列表`);

    // 获取所有特殊词汇（这会更新全局缓存）
    await getSpecialWords();

    // 递归调用自身，这次会使用全局缓存
    return getSpecialWordsList(serviceId, languageCode);
  } catch (error) {
    console.error("获取特殊词汇列表失败:", error);

    // 出错时尝试返回本地特殊词汇
    try {
      const localWords = getLocalSpecialWords();
      const localServiceWords = localWords[serviceId] || {};
      const localWordsList = localServiceWords[languageCode] || [];

      console.log(
        `[特殊词汇] 出错，使用本地特殊词汇 ${localWordsList.length} 个`
      );

      // 确保本地词汇格式一致
      return localWordsList.map((word) => ({
        word: typeof word === "string" ? word : word.word,
        isSystem: false,
        isLocal: true,
        ...(typeof word === "object" ? word : {}),
      }));
    } catch (localError) {
      console.error("获取本地特殊词汇也失败:", localError);
      return [];
    }
  }
}

/**
 * 处理特殊词汇
 * 将特殊词汇替换为标注中的假名
 * @param {string} text 原始文本
 * @param {Object|string} annotation 标注对象或文本
 * @param {string} serviceId TTS服务ID
 * @param {string} languageCode 语言代码
 * @returns {Object} 包含处理后的文本和替换信息
 */
export async function processSpecialWords(
  text,
  annotation,
  serviceId,
  languageCode
) {
  try {
    console.log(
      `[特殊词汇处理] 开始处理文本: "${text.substring(0, 30)}${
        text.length > 30 ? "..." : ""
      }"`
    );
    console.log(`[特殊词汇处理] 服务ID: ${serviceId}, 语言: ${languageCode}`);
    console.log(`[特殊词汇处理] 标注数据:`, annotation);

    // 如果没有服务ID，使用默认服务ID
    const effectiveServiceId = serviceId || "google";
    console.log(`[特殊词汇处理] 使用服务ID: ${effectiveServiceId}`);

    // 如果没有文本，直接返回原文
    if (!text) {
      console.log(`[特殊词汇处理] 文本为空，返回原文`);
      return {
        text: text,
        isProcessed: false,
        replacements: [],
      };
    }

    // 只处理日语文本
    if (languageCode !== "ja") {
      console.log(`[特殊词汇处理] 非日语文本(${languageCode})，不处理`);
      return {
        text: text,
        isProcessed: false,
        replacements: [],
      };
    }

    // 获取特殊词汇列表（使用全局缓存）
    const specialWordsList = await getSpecialWordsList(
      effectiveServiceId,
      languageCode
    );

    // 提取特殊词汇的单词列表
    const allWords = specialWordsList.map((item) => item.word);
    console.log(
      `[特殊词汇处理] 获取到 ${allWords.length} 个特殊词汇:`,
      allWords
    );

    // 如果没有任何词汇可处理，直接返回原文
    if (allWords.length === 0) {
      console.log(`[特殊词汇处理] 没有特殊词汇，返回原文`);
      return {
        text: text,
        isProcessed: false,
        replacements: [],
      };
    }

    // 按词汇长度降序排序，优先处理长词汇
    const sortedWords = [...allWords].sort((a, b) => b.length - a.length);
    console.log(`[特殊词汇处理] 排序后的特殊词汇:`, sortedWords);

    // 处理特殊词汇
    let processedText = text;
    let isProcessed = false;
    const replacements = [];

    // 检查是否有字符级标注数据
    const hasCharacters =
      annotation &&
      typeof annotation === "object" &&
      annotation.characters &&
      annotation.characters.length > 0;

    console.log(`[特殊词汇处理] 是否有字符级标注数据: ${hasCharacters}`);
    if (hasCharacters) {
      console.log(`[特殊词汇处理] 字符级标注数据:`, annotation.characters);
    }

    for (const word of sortedWords) {
      // 检查文本中是否包含特殊词汇
      if (processedText.includes(word)) {
        console.log(`[特殊词汇处理] 文本中包含特殊词汇: "${word}"`);
        let reading = null;

        // 使用字符级标注数据查找读音
        if (hasCharacters) {
          reading = findReadingFromCharacters(
            word,
            text,
            annotation.characters
          );
          console.log(
            `[特殊词汇处理] 从字符级标注中找到读音: ${reading || "未找到"}`
          );
        } else {
          console.log(`[特殊词汇处理] 没有字符级标注数据，无法获取读音`);
        }

        if (reading) {
          // 记录替换前的文本
          const beforeReplace = processedText;

          // 替换特殊词汇为假名
          processedText = processedText.replace(new RegExp(word, "g"), reading);
          console.log(`[特殊词汇处理] 替换特殊词汇: "${word}" -> "${reading}"`);

          // 如果替换成功
          if (beforeReplace !== processedText) {
            isProcessed = true;
            console.log(`[特殊词汇处理] 替换成功`);

            // 记录替换信息
            replacements.push({
              original: word,
              replacement: reading,
              position: beforeReplace.indexOf(word),
            });
          } else {
            console.log(`[特殊词汇处理] 替换失败，文本未变化`);
          }
        } else {
          console.log(`[特殊词汇处理] 未找到有效读音，不替换`);
        }
      } else {
        console.log(`[特殊词汇处理] 文本中不包含特殊词汇: "${word}"`);
      }
    }

    console.log(
      `[特殊词汇处理] 处理结果: isProcessed=${isProcessed}, 替换数量=${replacements.length}`
    );
    console.log(
      `[特殊词汇处理] 处理后文本: "${processedText.substring(0, 30)}${
        processedText.length > 30 ? "..." : ""
      }"`
    );

    return {
      text: processedText,
      isProcessed: isProcessed,
      replacements: replacements,
    };
  } catch (error) {
    console.error("处理特殊词汇失败:", error);
    return {
      text: text,
      isProcessed: false,
      replacements: [],
    };
  }
}

/**
 * 从字符级标注数据中查找特殊词汇的读音
 * @param {string} word 特殊词汇
 * @param {string} text 原始文本
 * @param {Array} characters 字符级标注数据
 * @returns {string|null} 找到的读音，如果没找到则返回null
 */
function findReadingFromCharacters(word, text, characters) {
  try {
    console.log(`[读音提取] 开始为特殊词汇 "${word}" 提取读音`);
    console.log(
      `[读音提取] 原文: "${text.substring(0, 30)}${
        text.length > 30 ? "..." : ""
      }"`
    );

    // 在原文中查找特殊词汇的位置
    const wordIndex = text.indexOf(word);
    if (wordIndex === -1) {
      console.log(`[读音提取] 在原文中未找到特殊词汇 "${word}"`);
      return null;
    }

    console.log(
      `[读音提取] 在原文中找到特殊词汇 "${word}" 的位置: ${wordIndex}`
    );

    // 计算字符级标注中对应的索引
    let charIndex = 0;
    let currentTextIndex = 0;

    // 找到特殊词汇在字符级标注中的起始位置
    while (currentTextIndex < wordIndex && charIndex < characters.length) {
      currentTextIndex += characters[charIndex].char.length;
      charIndex++;
    }

    // 如果找不到起始位置，返回null
    if (currentTextIndex !== wordIndex) {
      console.log(`[读音提取] 无法在字符级标注中找到对应的起始位置`);
      console.log(
        `[读音提取] 当前文本索引: ${currentTextIndex}, 目标索引: ${wordIndex}`
      );
      return null;
    }

    console.log(
      `[读音提取] 在字符级标注中找到起始位置，字符索引: ${charIndex}`
    );

    // 收集特殊词汇对应的读音
    let reading = "";
    let wordLength = 0;
    let startCharIndex = charIndex;

    console.log(`[读音提取] 开始收集读音，起始字符索引: ${startCharIndex}`);

    while (wordLength < word.length && charIndex < characters.length) {
      const char = characters[charIndex];
      wordLength += char.char.length;

      // 如果有读音且读音与字符不同，使用读音
      if (char.reading && char.reading !== char.char) {
        reading += char.reading;
        console.log(
          `[读音提取] 字符 "${char.char}" 使用读音 "${char.reading}"`
        );
      } else {
        // 否则使用原字符
        reading += char.char;
        console.log(
          `[读音提取] 字符 "${char.char}" 没有不同的读音，使用原字符`
        );
      }

      charIndex++;
    }

    console.log(
      `[读音提取] 收集的读音: "${reading}", 字符长度: ${wordLength}, 词汇长度: ${word.length}`
    );

    // 如果收集的字符长度与特殊词汇不匹配，返回null
    if (wordLength !== word.length) {
      console.log(`[读音提取] 收集的字符长度与特殊词汇长度不匹配，返回null`);
      return null;
    }

    // 检查是否有任何字符的读音与原字符不同
    let hasChanged = false;
    for (let i = startCharIndex; i < charIndex; i++) {
      if (
        characters[i].reading &&
        characters[i].reading !== characters[i].char
      ) {
        hasChanged = true;
        console.log(
          `[读音提取] 发现字符 "${characters[i].char}" 的读音 "${characters[i].reading}" 与原字符不同`
        );
        break;
      }
    }

    console.log(`[读音提取] 是否有字符读音与原字符不同: ${hasChanged}`);

    // 只有当至少有一个字符的读音与原字符不同时，才返回读音
    if (hasChanged) {
      console.log(`[读音提取] 返回读音: "${reading}"`);
      return reading;
    } else {
      console.log(`[读音提取] 所有字符的读音与原字符相同，返回null`);
      return null;
    }
  } catch (error) {
    console.error("从字符级标注查找读音失败:", error);
    return null;
  }
}

export default {
  getSpecialWords,
  addSpecialWord,
  deleteSpecialWord,
  batchAddSpecialWords,
  getLocalSpecialWords,
  addLocalSpecialWord,
  deleteLocalSpecialWord,
  clearLocalSpecialWords,
  processSpecialWords,
  getSpecialWordsList,
};
