/**
 * 播放策略模板服务
 * 提供模板的业务逻辑处理
 */
const db = require("../models");
const crypto = require("crypto");

// 自定义 nanoid 函数，生成 21 位随机 ID
function nanoid() {
  return crypto
    .randomBytes(16)
    .toString("base64")
    .replace(/[+/=]/g, "")
    .substring(0, 21);
}

// 系统预设模板
const SYSTEM_TEMPLATES = {
  easy: {
    name: "轻松模式",
    description:
      "两个环节，第一个环节重复4遍（第2遍后插入关键词，第3遍后插入翻译），第二个环节重复1遍",
    config: {
      sections: [
        {
          title: "重复练习",
          speed: 1.0,
          pauseDuration: 3000,
          repeatCount: 4,
          enableTranslation: true,
          translationLanguage: "zh-CN",
          translationPosition: 3,
          enableKeywords: true,
          keywordRepeatCount: 2,
          keywordPosition: 2,
          keywordSpeed: 1.0,
          repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
          repeatPauses: [3000, 3000, 3000, 3000],
        },
        {
          title: "通读验证",
          speed: 1.0,
          pauseDuration: 3000,
          repeatCount: 1,
          enableTranslation: false,
          translationLanguage: "",
          translationPosition: 2,
          enableKeywords: false,
          keywordRepeatCount: 2,
          keywordPosition: 2,
          keywordSpeed: 1.0,
          repeatSpeeds: [1.0],
          repeatPauses: [3000],
        },
      ],
    },
  },
  learning: {
    name: "学习模式",
    description:
      "三个环节，第一个环节重复1遍，第二个环节重复4遍（第2遍后插入关键词，第3遍后插入翻译），第三个环节重复1遍",
    config: {
      sections: [
        {
          title: "初步了解",
          speed: 1.0,
          pauseDuration: 3000,
          repeatCount: 1,
          enableTranslation: false,
          translationLanguage: "",
          translationPosition: 2,
          enableKeywords: false,
          keywordRepeatCount: 2,
          keywordPosition: 2,
          keywordSpeed: 1.0,
          repeatSpeeds: [1.0],
          repeatPauses: [3000],
        },
        {
          title: "重点练习",
          speed: 1.0,
          pauseDuration: 3000,
          repeatCount: 4,
          enableTranslation: true,
          translationLanguage: "zh-CN",
          translationPosition: 3,
          enableKeywords: true,
          keywordRepeatCount: 2,
          keywordPosition: 2,
          keywordSpeed: 1.0,
          repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
          repeatPauses: [3000, 3000, 3000, 3000],
        },
        {
          title: "巩固复习",
          speed: 1.0,
          pauseDuration: 3000,
          repeatCount: 1,
          enableTranslation: false,
          translationLanguage: "",
          translationPosition: 2,
          enableKeywords: false,
          keywordRepeatCount: 2,
          keywordPosition: 2,
          keywordSpeed: 1.0,
          repeatSpeeds: [1.0],
          repeatPauses: [3000],
        },
      ],
    },
  },
};

class PlaybackTemplateService {
  /**
   * 获取模板列表
   * @param {Object} options 查询选项
   * @param {string} options.type 模板类型
   * @param {boolean} options.isPublic 是否公开
   * @returns {Object} 模板列表
   */
  async getTemplates(options = {}) {
    const { type, isPublic } = options;

    // 构建查询条件
    const where = {
      status: "active",
    };

    // 只有明确指定类型时才过滤类型
    if (type) {
      where.type = type;
    }

    // 只有明确指定isPublic时才过滤
    if (isPublic !== undefined) {
      where.isPublic = isPublic;
    }

    // 获取所有用户模板（不分页）
    const result = await db.PlaybackTemplate.findAll({
      where,
      order: [
        ["usageCount", "DESC"],
        ["created_at", "DESC"],
      ],
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username", "email"],
          required: false,
        },
      ],
    });

    // 添加系统预设模板（除非明确只查询用户模板）
    let systemTemplates = [];
    if (!type || type === "system") {
      systemTemplates = Object.keys(SYSTEM_TEMPLATES).map((key) => ({
        id: `system_${key}`,
        name: SYSTEM_TEMPLATES[key].name,
        description: SYSTEM_TEMPLATES[key].description,
        config: SYSTEM_TEMPLATES[key].config,
        type: "system",
        userId: null,
        isPublic: true,
        usageCount: 0,
        status: "active",
        created_at: new Date(),
        updated_at: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
        creator: null,
      }));
    }

    return {
      templates: [...systemTemplates, ...result],
    };
  }

  /**
   * 根据ID获取模板
   * @param {string} id 模板ID
   * @param {string} userId 用户ID（用于权限检查）
   * @returns {Object|null} 模板对象
   */
  async getTemplateById(id, userId = null) {
    // 检查是否为系统预设模板
    if (id.startsWith("system_")) {
      const key = id.replace("system_", "");
      if (SYSTEM_TEMPLATES[key]) {
        return {
          id,
          name: SYSTEM_TEMPLATES[key].name,
          description: SYSTEM_TEMPLATES[key].description,
          config: SYSTEM_TEMPLATES[key].config,
          type: "system",
          userId: null,
          isPublic: true,
          usageCount: 0,
          status: "active",
          created_at: new Date(),
          updated_at: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          creator: null,
        };
      }
      return null;
    }

    // 查询数据库中的模板
    const template = await db.PlaybackTemplate.findByPk(id, {
      include: [
        {
          model: db.User,
          as: "creator",
          attributes: ["id", "username", "email"],
          required: false,
        },
      ],
    });

    if (!template) {
      return null;
    }

    // 检查访问权限
    if (
      template.type === "user" &&
      !template.isPublic &&
      template.userId !== userId
    ) {
      return null;
    }

    return template;
  }

  /**
   * 创建模板
   * @param {Object} templateData 模板数据
   * @param {string} userId 创建者ID
   * @returns {Object} 创建的模板
   */
  async createTemplate(templateData, userId) {
    const { name, description, config, isPublic = false } = templateData;

    // 验证配置格式
    this.validateTemplateConfig(config);

    const id = nanoid();

    const template = await db.PlaybackTemplate.create({
      id,
      name,
      description,
      config,
      type: "user",
      userId,
      isPublic,
      usageCount: 0,
      status: "active",
    });

    return template;
  }

  /**
   * 更新模板
   * @param {string} id 模板ID
   * @param {Object} updateData 更新数据
   * @param {string} userId 用户ID
   * @returns {Object} 更新后的模板
   */
  async updateTemplate(id, updateData, userId) {
    const template = await db.PlaybackTemplate.findByPk(id);

    if (!template) {
      throw new Error("模板不存在");
    }

    // 检查权限：只有创建者可以编辑
    if (template.userId !== userId) {
      throw new Error("权限不足");
    }

    // 验证配置格式（如果有更新配置）
    if (updateData.config) {
      this.validateTemplateConfig(updateData.config);
    }

    await template.update(updateData);

    return template;
  }

  /**
   * 删除模板
   * @param {string} id 模板ID
   * @param {string} userId 用户ID
   * @returns {boolean} 是否删除成功
   */
  async deleteTemplate(id, userId) {
    const template = await db.PlaybackTemplate.findByPk(id);

    if (!template) {
      throw new Error("模板不存在");
    }

    // 检查权限：只有创建者可以删除
    if (template.userId !== userId) {
      throw new Error("权限不足");
    }

    await template.destroy();

    return true;
  }

  /**
   * 使用模板（增加使用次数）
   * @param {string} id 模板ID
   * @returns {boolean} 是否成功
   */
  async useTemplate(id) {
    // 系统模板不需要更新使用次数
    if (id.startsWith("system_")) {
      return true;
    }

    const template = await db.PlaybackTemplate.findByPk(id);

    if (template) {
      await template.increment("usageCount");
      return true;
    }

    return false;
  }

  /**
   * 复制模板
   * @param {string} id 原模板ID
   * @param {string} newName 新模板名称
   * @param {string} userId 用户ID
   * @returns {Object} 新模板
   */
  async duplicateTemplate(id, newName, userId) {
    const originalTemplate = await this.getTemplateById(id, userId);

    if (!originalTemplate) {
      throw new Error("原模板不存在或无权限访问");
    }

    const newTemplate = await this.createTemplate(
      {
        name: newName,
        description: originalTemplate.description,
        config: originalTemplate.config,
        isPublic: false,
      },
      userId
    );

    return newTemplate;
  }

  /**
   * 验证模板配置格式
   * @param {Object} config 配置对象
   */
  validateTemplateConfig(config) {
    if (!config || !config.sections || !Array.isArray(config.sections)) {
      throw new Error("配置格式错误：缺少sections数组");
    }

    if (config.sections.length === 0) {
      throw new Error("配置格式错误：至少需要一个环节");
    }

    // 验证每个环节的配置
    config.sections.forEach((section, index) => {
      const requiredFields = [
        "title",
        "speed",
        "pauseDuration",
        "repeatCount",
        "enableTranslation",
        "translationLanguage",
        "translationPosition",
        "enableKeywords",
        "keywordPosition",
        "keywordRepeatCount",
        "keywordSpeed",
        "repeatSpeeds",
        "repeatPauses",
      ];

      requiredFields.forEach((field) => {
        if (section[field] === undefined) {
          throw new Error(`配置格式错误：环节${index + 1}缺少字段${field}`);
        }
      });

      // 验证数组长度
      if (section.repeatSpeeds.length !== section.repeatCount) {
        throw new Error(
          `配置格式错误：环节${
            index + 1
          }的repeatSpeeds数组长度与repeatCount不匹配`
        );
      }

      if (section.repeatPauses.length !== section.repeatCount) {
        throw new Error(
          `配置格式错误：环节${
            index + 1
          }的repeatPauses数组长度与repeatCount不匹配`
        );
      }
    });
  }
}

module.exports = new PlaybackTemplateService();
