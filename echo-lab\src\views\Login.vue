<template>
  <div class="login-container">
    <div class="login-card">
      <h2 class="login-title">Echo Lab</h2>
      <p class="login-subtitle">使用邮箱验证码登录</p>

      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" label-position="top"
        @submit.prevent="handleSubmit">
        <!-- 邮箱输入 -->
        <el-form-item label="邮箱地址" prop="email">
          <el-input v-model="loginForm.email" placeholder="请输入邮箱地址" :disabled="codeSent || verifying">
            <template #prefix>
              <el-icon>
                <Message />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 验证码输入 -->
        <el-form-item v-if="codeSent" label="验证码" prop="code">
          <div class="code-input-group">
            <el-input v-model="loginForm.code" placeholder="请输入验证码" :disabled="verifying" maxlength="6"
              @keyup.enter="verifyCode">
              <template #prefix>
                <el-icon>
                  <Lock />
                </el-icon>
              </template>
            </el-input>
            <el-button type="primary" :disabled="countdown > 0 || verifying" @click="sendVerificationCode">
              {{ countdown > 0 ? `${countdown}秒后重新获取` : "重新获取" }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button v-if="!codeSent" type="primary" :loading="sending" @click="sendVerificationCode"
            class="submit-button">
            获取验证码
          </el-button>
          <el-button v-else type="primary" :loading="verifying" @click="verifyCode" class="submit-button">
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 返回首页 -->
      <div class="back-link">
        <router-link to="/">返回首页</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
// 这些图标在模板中通过组件方式使用，所以不会直接在脚本中引用
// 但仍然需要导入以便Vue能够识别它们
import { Message, Lock } from "@element-plus/icons-vue";
import { sendCode, verifyCode as verifyCodeApi } from "@/services/authService";
import { useUserStore } from "@/stores/userStore";
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';

// 路由
const router = useRouter();
const route = useRoute();

// 用户状态存储
const userStore = useUserStore();

// 初始化SEO
useSEO(PAGE_SEO_CONFIG.login);

// 表单数据
const loginForm = reactive({
  email: "",
  code: "",
});

// 表单验证规则
const loginRules = {
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入有效的邮箱地址", trigger: "blur" },
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码必须是6位数字", trigger: "blur" },
  ],
};

// 表单引用
const loginFormRef = ref(null);

// 状态
const codeSent = ref(false);
const sending = ref(false);
const verifying = ref(false);
const countdown = ref(0);
let countdownTimer = null;

// 发送验证码
async function sendVerificationCode() {
  try {
    // 验证邮箱
    await loginFormRef.value.validateField("email");

    sending.value = true;
    const result = await sendCode(loginForm.email, "login");

    if (result.success) {
      ElMessage.success("验证码已发送，请查收邮件");
      codeSent.value = true;
      startCountdown();
    } else {
      ElMessage.error(result.error || "验证码发送失败");
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error(error.error || "验证码发送失败，请稍后重试");
  } finally {
    sending.value = false;
  }
}

// 验证码登录
async function verifyCode() {
  try {
    // 验证表单
    await loginFormRef.value.validate();

    verifying.value = true;
    const result = await verifyCodeApi(loginForm.email, loginForm.code, "login");

    if (result.success) {
      // 更新用户状态
      userStore.setUser(result.user);

      ElMessage.success("登录成功");

      // 跳转到重定向页面或首页
      const redirectPath = route.query.redirect || "/";
      router.push(redirectPath);
    } else {
      ElMessage.error(result.error || "验证码验证失败");
    }
  } catch (error) {
    console.error("验证码验证失败:", error);
    ElMessage.error(error.error || "验证码验证失败，请稍后重试");
  } finally {
    verifying.value = false;
  }
}

// 开始倒计时
function startCountdown() {
  countdown.value = 60;
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer);
    }
  }, 1000);
}

// 处理表单提交
function handleSubmit() {
  if (codeSent.value) {
    verifyCode();
  } else {
    sendVerificationCode();
  }
}

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 1rem;
}

.login-card {
  width: 100%;
  max-width: 28rem;
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
}

.login-title {
  font-size: 1.75rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 0.5rem;
  color: #303133;
}

.login-subtitle {
  font-size: 1rem;
  text-align: center;
  margin-bottom: 2rem;
  color: #606266;
}

.code-input-group {
  display: flex;
  gap: 0.75rem;
}

.submit-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.back-link {
  margin-top: 1.5rem;
  text-align: center;
}

.back-link a {
  color: #409eff;
  text-decoration: none;
}

.back-link a:hover {
  text-decoration: underline;
}
</style>
