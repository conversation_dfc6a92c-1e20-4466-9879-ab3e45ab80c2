<template>
  <div class="feedback-button-container">
    <el-button type="primary" size="small" class="feedback-button" @click="openFeedbackDialog" :icon="ChatLineRound">
      反馈
    </el-button>

    <!-- 反馈对话框 -->
    <el-dialog v-model="dialogVisible" title="用户反馈" :width="isMobile ? '90%' : '500px'" :close-on-click-modal="false"
      :destroy-on-close="true" class="feedback-dialog">
      <el-form ref="feedbackFormRef" :model="feedbackForm" :rules="feedbackRules" label-position="top">
        <!-- 反馈类型 -->
        <el-form-item label="反馈类型" prop="type">
          <el-select v-model="feedbackForm.type" placeholder="请选择反馈类型" class="w-100">
            <el-option label="功能建议（改进现有功能）" value="suggestion" />
            <el-option label="问题反馈（报告错误）" value="bug" />
            <el-option label="新功能请求（添加全新功能）" value="feature" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <!-- 反馈内容 -->
        <el-form-item label="反馈内容" prop="content">
          <el-input v-model="feedbackForm.content" type="textarea" :rows="isMobile ? 3 : 5"
            placeholder="请详细描述您的反馈内容..." />
        </el-form-item>

        <!-- 联系方式 -->
        <el-form-item label="联系方式（可选）" prop="contact">
          <el-input v-model="feedbackForm.contact" placeholder="您的邮箱或其他联系方式，方便我们回复您" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFeedback" :loading="submitting">
            提交反馈
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { ChatLineRound } from "@element-plus/icons-vue";
import { submitFeedback as submitFeedbackApi } from "@/services/feedbackService";
import { isMobileDevice } from "@/utils/deviceDetector";

// 检测是否为移动设备
const isMobile = isMobileDevice();

// 对话框可见性
const dialogVisible = ref(false);

// 表单引用
const feedbackFormRef = ref(null);

// 提交状态
const submitting = ref(false);

// 表单数据
const feedbackForm = reactive({
  type: "suggestion",
  content: "",
  contact: "",
  pageUrl: window.location.href
});

// 表单验证规则
const feedbackRules = {
  type: [
    { required: true, message: "请选择反馈类型", trigger: "change" }
  ],
  content: [
    { required: true, message: "请输入反馈内容", trigger: "blur" },
    { min: 5, message: "反馈内容至少5个字符", trigger: "blur" }
  ],
  contact: [
    {
      pattern: /^$|^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "请输入有效的邮箱地址",
      trigger: "blur"
    }
  ]
};

// 打开反馈对话框
function openFeedbackDialog() {
  dialogVisible.value = true;
}

// 提交反馈
async function submitFeedback() {
  // 验证表单
  await feedbackFormRef.value.validate(async (valid) => {
    if (!valid) {
      return;
    }

    submitting.value = true;

    try {
      // 提交反馈
      const result = await submitFeedbackApi({
        type: feedbackForm.type,
        content: feedbackForm.content,
        contact: feedbackForm.contact,
        pageUrl: feedbackForm.pageUrl
      });

      if (result.success) {
        ElMessage.success("感谢您的反馈！我们会尽快处理。");
        dialogVisible.value = false;

        // 重置表单
        feedbackForm.type = "suggestion";
        feedbackForm.content = "";
        feedbackForm.contact = "";
      } else {
        ElMessage.error(result.error || "提交反馈失败，请稍后重试");
      }
    } catch (error) {
      console.error("提交反馈失败:", error);
      ElMessage.error("提交反馈失败，请稍后重试");
    } finally {
      submitting.value = false;
    }
  });
}
</script>

<style scoped>
.feedback-button-container {
  position: fixed;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 100;
}

.feedback-button {
  border-radius: 1.5rem;
  padding: 0.5rem 1rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
}

.w-100 {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* 移动端样式优化 */
:deep(.feedback-dialog) {
  max-width: 100%;
}

:deep(.feedback-dialog .el-dialog__body) {
  padding: 1rem;
}

:deep(.feedback-dialog .el-form-item) {
  margin-bottom: 0.75rem;
}

:deep(.feedback-dialog .el-form-item__label) {
  padding-bottom: 0.25rem;
  line-height: 1.2;
  font-size: 0.9rem;
}

/* 移动端样式 - 使用类名而非媒体查询 */
.mobile-device .feedback-button-container {
  right: 1rem;
  bottom: 1rem;
}

.mobile-device .feedback-button {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.mobile-device :deep(.feedback-dialog .el-dialog__header) {
  padding: 0.75rem 1rem;
}

.mobile-device :deep(.feedback-dialog .el-dialog__footer) {
  padding: 0.5rem 1rem 1rem;
}
</style>
