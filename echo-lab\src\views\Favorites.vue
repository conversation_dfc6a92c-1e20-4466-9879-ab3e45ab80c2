<!--
  收藏列表页面
  显示用户收藏的内容列表
-->
<template>
  <div class="favorites-page">
    <SmartPageHeader title="我的收藏">
      <template #actions>
        <el-input v-model="searchQuery" placeholder="搜索收藏内容" prefix-icon="Search" clearable @clear="handleSearchClear"
          class="search-input" />
      </template>
    </SmartPageHeader>

    <div class="page-content">
      <!-- 加载状态 -->
      <div v-if="favoriteStore.loading" class="loading-container">
        <el-skeleton :rows="3" animated />
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="favoriteStore.favorites.length === 0" class="empty-container">
        <el-empty description="暂无收藏内容">
          <template #description>
            <p>您还没有收藏任何内容</p>
          </template>
          <el-button type="primary" @click="goToHome">浏览内容</el-button>
        </el-empty>
      </div>

      <!-- 内容列表 -->
      <div v-else class="favorites-container">
        <div class="favorites-grid">
          <div v-for="content in filteredFavorites" :key="content.id" class="favorite-item">
            <PublicContentCard :content="content">
              <template #actions>
                <FavoriteButton :content-id="content.id" :content-data="content" @unfavorite="handleUnfavorite" />
              </template>
            </PublicContentCard>
          </div>
        </div>

        <!-- 无搜索结果 -->
        <div v-if="searchQuery && filteredFavorites.length === 0" class="no-results">
          <el-empty description="没有找到匹配的内容" :image-size="100">
            <el-button @click="searchQuery = ''">清除搜索</el-button>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useFavoriteStore } from "@/stores/favoriteStore";
import { useUserStore } from "@/stores/userStore";
import SmartPageHeader from "@/components/common/SmartPageHeader.vue";
import PublicContentCard from "@/components/content/PublicContentCard.vue";
import FavoriteButton from "@/components/common/FavoriteButton.vue";
import { Search } from "@element-plus/icons-vue";

const router = useRouter();
const favoriteStore = useFavoriteStore();
const userStore = useUserStore();

// 搜索状态
const searchQuery = ref("");

// 过滤后的收藏列表
const filteredFavorites = computed(() => {
  if (!searchQuery.value) {
    return favoriteStore.favorites;
  }

  const query = searchQuery.value.toLowerCase();
  return favoriteStore.favorites.filter(
    (content) =>
      content.name.toLowerCase().includes(query) ||
      (content.description && content.description.toLowerCase().includes(query)) ||
      (content.tags && content.tags.toLowerCase().includes(query))
  );
});

// 初始化
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning("请先登录后查看收藏内容");
    router.push("/login");
    return;
  }

  // 获取收藏列表
  await favoriteStore.fetchFavorites();
});

// 处理取消收藏
const handleUnfavorite = (contentId) => {
  // 已经在 FavoriteButton 组件中处理了状态更新
  // 这里可以添加额外的逻辑
};

// 清除搜索
const handleSearchClear = () => {
  searchQuery.value = "";
};

// 跳转到首页
const goToHome = () => {
  router.push("/");
};
</script>

<style scoped>
.favorites-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-content {
  padding: 1.5rem;
}

.search-input {
  width: 250px;
}

.loading-container,
.empty-container {
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.favorites-container {
  margin-top: 1rem;
}

.favorites-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.favorite-item {
  transition: transform 0.2s;
}

.no-results {
  margin-top: 2rem;
  padding: 2rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  text-align: center;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .page-content {
    padding: 1rem;
  }

  .search-input {
    width: 200px;
  }

  .favorites-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }
}
</style>
