<!--
  视频配置节点组件
  配置视频输出参数 - 精简版
-->
<template>
  <div>
    <BaseNode :nodeId="nodeId">
      <!-- 添加一个滚动容器 -->
      <div class="scroll-container">
        <div class="video-config-node-content">
          <!-- 源节点选择 - 与其他节点保持一致 -->
          <div class="source-section">
            <div class="section-title">源节点：</div>
            <div v-if="hasSourceNode" class="source-info">
              <el-tag v-for="sourceNode in sourceNodes" :key="sourceNode.id" size="small" class="source-tag"
                :closable="true" @close="removeSourceNode(sourceNode.id)">
                {{ getSourceNodeLabel(sourceNode) }}
              </el-tag>
            </div>
            <div v-else class="source-empty">
              <el-alert type="warning" :closable="false" show-icon>
                请连接一个文本序列节点作为输入源
              </el-alert>
            </div>
          </div>

          <!-- 设置区域 -->
          <div class="settings-section">


            <!-- 封面设置 -->
            <div class="settings-group">
              <div class="collapsible-header" @click="toggleCoverCollapse">
                <div class="group-title">封面设置</div>
                <el-icon class="collapse-icon" :class="{ 'is-active': !coverCollapsed }">
                  <ArrowDown />
                </el-icon>
              </div>


              <div v-show="!coverCollapsed" class="cover-settings">
                <!-- 封面基本信息 -->
                <div class="cover-basic-info">
                  <div class="cover-preview" v-if="params.cover?.imageUrl">
                    <div class="cover-preview-container">
                      <!-- 直接显示图片，不再区分原始图片和合成图片 -->
                      <img :src="params.cover.imageUrl" alt="封面图片预览" class="cover-preview-image" />
                    </div>

                    <!-- 移除展示时长显示 -->
                  </div>

                  <div v-else class="cover-empty">
                    <div class="add-cover-center">
                      <el-button type="primary" size="small" @click="openCoverEditor">
                        <el-icon style="margin-right: 0.25rem;">
                          <Upload />
                        </el-icon>添加封面
                      </el-button>
                      <div class="cover-tip">添加16:9比例的封面图片</div>
                    </div>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="cover-actions" v-if="params.cover?.imageUrl">
                  <el-button type="primary" size="small" @click="openCoverEditor">编辑封面</el-button>
                  <el-button type="danger" size="small" @click="removeCoverImage">移除封面</el-button>
                </div>

                <!-- 封面展示时长 -->
                <div class="duration-setting" v-if="params.cover?.imageUrl">
                  <div class="setting-label">展示时长 (秒):</div>
                  <el-input-number v-model="params.cover.duration" :min="1" :max="10" :step="0.5"
                    @change="handleSettingsChange" :controls="true" style="width: 8rem;" />
                </div>
              </div>
            </div>

            <!-- 版权信息设置 -->
            <div class="settings-group">
              <div class="collapsible-header" @click="toggleCopyrightCollapse">
                <div class="group-title">版权信息</div>
                <el-icon class="collapse-icon" :class="{ 'is-active': !copyrightCollapsed }">
                  <ArrowDown />
                </el-icon>
              </div>
              <div v-show="!copyrightCollapsed" class="copyright-settings">
                <!-- 版权文本 -->
                <div class="setting-row">
                  <div class="setting-label">版权文本:</div>
                  <el-input v-model="params.copyright.text" placeholder="输入版权信息，如：© 2023 All Rights Reserved"
                    @change="handleSettingsChange" />
                </div>

                <!-- 版权位置 -->
                <div class="setting-row">
                  <div class="setting-label">显示位置:</div>
                  <div class="position-buttons">
                    <el-radio-group v-model="params.copyright.position" @change="handleSettingsChange" size="small">
                      <el-radio-button value="topLeft">左上</el-radio-button>
                      <el-radio-button value="topRight">右上</el-radio-button>
                      <el-radio-button value="bottomLeft">左下</el-radio-button>
                      <el-radio-button value="bottomRight">右下</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BaseNode>

    <!-- 封面编辑器对话框 -->
    <standard-dialog v-model="coverEditorVisible" title="封面编辑器" width="60%" :show-confirm="true" confirm-text="保存设置"
      cancel-text="关闭" :confirm-closes-dialog="false" @confirm="saveCoverSettings" @cancel="cancelCoverEdit">
      <div class="cover-editor">
        <!-- 左侧：封面预览 -->
        <div class="cover-editor-preview">
          <div class="cover-editor-container" ref="coverEditorContainer">
            <div v-if="tempCoverData && tempCoverData.imageUrl" class="cover-image-container">
              <!-- 使用 cropper-image -->
              <div class="cropper-wrapper aspect-16-9">
                <img ref="cropperImgRef" :src="tempCoverData.imageUrl" class="cropper-img" />
                <!-- 文本元素层 - 绝对定位在裁剪区上方 -->
                <div class="text-elements-layer">
                  <div v-for="text in tempCoverData.texts" :key="text.id" class="cover-text-element editor-text" :style="{
                    color: text.color,
                    fontSize: `${text.size}rem`,
                    left: `${text.x}%`,
                    top: `${text.y}%`,
                    padding: '5px 10px',
                    textShadow: '0 0 3px rgba(0, 0, 0, 0.8)',
                    pointerEvents: 'auto',
                    userSelect: 'none',
                    position: 'absolute',
                    transform: 'translate(-50%, -50%)',
                    fontWeight: 'bold',
                    zIndex: 20
                  }" @mousedown.stop="startDrag($event, text.id)">
                    {{ text.content }}
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="cover-upload-placeholder">
              <el-upload class="upload-area" action="#" :auto-upload="false" :show-file-list="false" accept="image/*"
                :on-change="handleCoverImageUpload">
                <div class="upload-content">
                  <el-icon class="upload-icon">
                    <Upload />
                  </el-icon>
                  <div class="upload-text">点击或拖拽图片到此处上传</div>
                  <div class="upload-tip">建议使用16:9比例的图片</div>
                </div>
              </el-upload>
            </div>
          </div>

          <!-- 图片操作按钮 -->
          <div class="cover-image-actions" v-if="tempCoverData && tempCoverData.imageUrl">
            <el-button type="primary" @click="openImageUpload">更换图片</el-button>
            <el-button type="danger" @click="removeTempCoverImage">移除图片</el-button>
          </div>
        </div>

        <!-- 右侧：编辑面板 -->
        <div class="cover-editor-panel">
          <!-- 文字元素管理 -->
          <div class="editor-section">
            <div class="editor-section-header">
              <h3 class="editor-section-title">文字元素</h3>
              <el-button type="primary" size="small" @click="addCoverText">添加文字</el-button>
            </div>

            <!-- 文字元素列表 -->
            <div v-if="tempCoverData && tempCoverData.texts && tempCoverData.texts.length > 0"
              class="text-elements-list">
              <div v-for="(text, index) in tempCoverData.texts" :key="text.id" class="text-element-card">
                <div class="text-element-header" @click="toggleTextCollapse(text.id)">
                  <div class="text-element-title">
                    文字 #{{ index + 1 }}: {{ text.content.substring(0, 10) }}{{ text.content.length > 10 ? '...' : '' }}
                  </div>
                  <div class="text-element-actions">
                    <el-icon class="collapse-icon" :class="{ 'is-active': !isTextCollapsed(text.id) }">
                      <ArrowDown />
                    </el-icon>
                    <el-button type="danger" size="small" @click.stop="removeCoverText(text.id)">删除</el-button>
                  </div>
                </div>

                <div v-show="!isTextCollapsed(text.id)" class="text-element-content">
                  <el-form label-position="top">
                    <el-form-item>
                      <template #label>
                        <span class="compact-label">文字内容</span>
                      </template>
                      <el-input v-model="text.content" placeholder="输入文字内容" />
                    </el-form-item>

                    <el-form-item>
                      <template #label>
                        <span class="compact-label">文字大小</span>
                      </template>
                      <div class="size-control">
                        <div class="slider-with-value">
                          <el-slider v-model="text.size" :min="0.5" :max="4" :step="0.1"
                            :format-tooltip="value => `${Math.round(value * 16)}`" class="text-size-slider" />
                          <div class="size-display">{{ Math.round(text.size * 16) }}</div>
                        </div>
                      </div>
                    </el-form-item>

                    <el-form-item>
                      <template #label>
                        <span class="compact-label">文字颜色</span>
                      </template>
                      <el-color-picker v-model="text.color" show-alpha />
                    </el-form-item>

                    <el-form-item>
                      <template #label>
                        <span class="compact-label">文字位置</span>
                      </template>
                      <div class="position-controls">
                        <div class="position-inputs-horizontal">
                          <div class="position-input-group">
                            <span class="position-label">X:</span>
                            <el-input-number v-model="text.x" :min="0" :max="100" :step="1" size="small"
                              controls-position="right" @change="handlePositionChange" style="width: 80px;" />
                            <span class="position-unit">%</span>
                          </div>
                          <div class="position-input-group">
                            <span class="position-label">Y:</span>
                            <el-input-number v-model="text.y" :min="0" :max="100" :step="1" size="small"
                              controls-position="right" @change="handlePositionChange" style="width: 80px;" />
                            <span class="position-unit">%</span>
                          </div>
                        </div>
                        <div class="position-hint">可直接在左侧预览区域拖动文字调整位置</div>
                      </div>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>

            <div v-else class="no-text-elements">
              <el-empty description="暂无文字元素" :image-size="60">
                <el-button type="primary" @click="addCoverText">添加文字</el-button>
              </el-empty>
            </div>
          </div>
        </div>
      </div>
    </standard-dialog>

    <!-- 图片上传隐藏元素 -->
    <input type="file" ref="fileInput" style="display: none;" accept="image/*" @change="handleFileInputChange" />
  </div>
</template>

<script setup>
import { computed, ref, reactive, onUnmounted, nextTick, watch } from 'vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import BaseNode from './BaseNode.vue';
import nodeFactory from '@/core/factories/NodeFactory';
import { ArrowDown, Upload } from '@element-plus/icons-vue';
import imageService from '@/services/imageService';
import StandardDialog from '@/components/common/StandardDialog.vue';
// 引入 cropperjs 1.x
import Cropper from 'cropperjs';
import 'cropperjs/dist/cropper.css';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  }
});

const nodeStore = useNodeStore();

// 节点数据
const node = computed(() => nodeStore.getNode(props.nodeId));

// 节点参数
const params = computed({
  get: () => {
    // 获取节点参数，如果不存在则使用默认值
    const nodeParams = node.value?.params || {};

    // 确保 cover 属性存在
    if (!nodeParams.cover) {
      nodeParams.cover = {
        imageUrl: "",
        duration: 1 // 修改默认值为1秒
      };
    }

    // 确保 copyright 属性存在
    if (!nodeParams.copyright) {
      nodeParams.copyright = {
        text: "https://echolab.club",
        position: "topLeft" // 修改默认值为左上
      };
    }

    return nodeParams;
  },
  set: (value) => nodeStore.updateNodeParams(props.nodeId, value)
});

// 源节点
const sourceNodes = computed(() => {
  if (!node.value || !node.value.sourceIds || node.value.sourceIds.length === 0) {
    return [];
  }

  return node.value.sourceIds.map(id => nodeStore.getNode(id)).filter(Boolean);
});

// 是否有源节点
const hasSourceNode = computed(() => sourceNodes.value.length > 0);

// 折叠状态
const coverCollapsed = ref(false);
const copyrightCollapsed = ref(false);
const collapsedTextIds = ref(new Set());

// 封面编辑器状态
const coverEditorVisible = ref(false);
const fileInput = ref(null);
const coverEditorContainer = ref(null);
const isProcessing = ref(false);

// 临时编辑状态，用于存储编辑中的封面数据
const tempCoverData = ref(null);

// 裁剪器相关引用
const cropperImgRef = ref(null);
let cropperInstance = null;

// 裁剪数据
const cropData = reactive({
  coordinates: null, // 实时裁剪坐标
  canvas: null // 裁剪后的画布数据
});



// 文本拖动状态
const dragState = reactive({
  isDragging: false,
  currentTextId: null,
  startX: 0,
  startY: 0,
  elementX: 0,
  elementY: 0
});

// 切换折叠状态
function toggleCoverCollapse() {
  coverCollapsed.value = !coverCollapsed.value;
}

function toggleCopyrightCollapse() {
  copyrightCollapsed.value = !copyrightCollapsed.value;
}

// 切换文本元素折叠状态
function toggleTextCollapse(textId) {
  if (collapsedTextIds.value.has(textId)) {
    collapsedTextIds.value.delete(textId);
  } else {
    collapsedTextIds.value.add(textId);
  }
}

// 检查文本元素是否折叠
function isTextCollapsed(textId) {
  return collapsedTextIds.value.has(textId);
}

// 添加新的文本元素
function addCoverText() {
  if (!tempCoverData.value) {
    tempCoverData.value = {
      imageUrl: '',
      duration: 1, // 修改默认值为1秒
      texts: [] // 编辑时临时使用的字段
    };
  }

  if (!tempCoverData.value.texts) {
    tempCoverData.value.texts = [];
  }

  // 折叠所有现有文本元素
  tempCoverData.value.texts.forEach(text => {
    collapsedTextIds.value.add(text.id);
  });

  // 生成唯一ID
  const textId = Date.now().toString();

  // 添加新文本元素
  tempCoverData.value.texts.push({
    id: textId,
    content: "新文本",
    size: 1.5,
    color: "#FFFFFF",
    x: 50, // 默认位置在中间，单位为百分比
    y: 50
  });

  // 新添加的文本元素默认展开
  collapsedTextIds.value.delete(textId);
}

// 删除文本元素
function removeCoverText(textId) {
  if (!tempCoverData.value || !tempCoverData.value.texts) {
    return;
  }

  tempCoverData.value.texts = tempCoverData.value.texts.filter(text => text.id !== textId);
}



// 获取源节点标签
function getSourceNodeLabel(sourceNode) {
  if (!sourceNode) return '未知节点';

  // 如果有自定义名称，优先显示
  if (sourceNode.customName) {
    return sourceNode.customName;
  }

  // 获取节点类型标签
  const typeLabel = nodeFactory.getNodeTypeConfig(sourceNode.type)?.label || sourceNode.type;

  // 使用节点编号
  const nodeNumber = sourceNode.number || 1;

  return `${typeLabel} #${nodeNumber}`;
}

// 处理封面图片上传
async function handleCoverImageUpload(file) {
  if (!file) return;

  // 检查文件类型
  if (!file.raw.type.startsWith('image/')) {
    ElMessage.error('请上传图片文件');
    return;
  }

  try {
    // 创建临时URL用于预览
    const reader = new FileReader();
    reader.onload = (e) => {
      // 确保临时数据存在
      if (!tempCoverData.value) {
        tempCoverData.value = {
          imageUrl: '',
          duration: 1, // 修改默认值为1秒
          texts: [] // 编辑时临时使用的字段
        };
      }

      // 更新临时数据，使用临时URL
      tempCoverData.value.imageUrl = e.target.result;
      tempCoverData.value.tempFile = file.raw; // 保存文件对象，稍后上传

      // 确保文本数组存在

      if (!tempCoverData.value.texts) {
        tempCoverData.value.texts = [];
      }

      // 图片已经加载完成，cropperjs 会自动处理图片加载
      // 显示加载成功消息
      ElMessage.success('图片加载成功，可以开始编辑');
    };

    reader.readAsDataURL(file.raw);
  } catch (error) {
    ElMessage.error('图片加载失败: ' + (error.message || '未知错误'));
  }
}

function initCropper() {
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
  if (cropperImgRef.value) {
    cropperInstance = new Cropper(cropperImgRef.value, {
      aspectRatio: 16 / 9,
      viewMode: 1,
      autoCropArea: 1,
      background: false,
      movable: true,
      zoomable: true,
      scalable: false,
      rotatable: false,
      responsive: true,
      cropBoxResizable: true,
      cropBoxMovable: true,
      dragMode: 'move',
    });
  }
}

// 监听图片变化，自动初始化 cropperjs
watch(
  () => tempCoverData.value && tempCoverData.value.imageUrl,
  (newUrl) => {
    if (!newUrl) return;
    nextTick(() => {
      if (cropperImgRef.value) {
        cropperImgRef.value.onload = null;
        cropperImgRef.value.onload = () => {
          initCropper();
        };
        if (cropperImgRef.value.complete) {
          initCropper();
        }
      }
    });
  }
);

// openCoverEditor 只需设置数据和显示，无需再绑定 onload
function openCoverEditor() {
  if (!params.value.cover) {
    params.value.cover = {
      imageUrl: "",
      duration: 1
    };
  }
  tempCoverData.value = JSON.parse(JSON.stringify(params.value.cover));
  coverEditorVisible.value = true;
}

function cancelCoverEdit() {
  coverEditorVisible.value = false;
  tempCoverData.value = null;
  cropData.coordinates = null;
  cropData.canvas = null;
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
}

// 打开图片上传
function openImageUpload() {
  if (fileInput.value) {
    fileInput.value.click();
  }
}

// 处理文件输入变化
function handleFileInputChange(event) {
  const file = event.target.files[0];
  if (file) {
    handleCoverImageUpload({ raw: file });
  }
  // 重置文件输入，以便能够再次选择同一文件
  if (fileInput.value) {
    fileInput.value.value = '';
  }
}



// 生成合成图片
async function generateCompositeCover() {
  if (!tempCoverData.value || !tempCoverData.value.imageUrl) {
    ElMessage.warning('请先上传封面图片');
    return null;
  }
  try {
    if (!cropperInstance) {
      ElMessage.warning('裁剪器未初始化');
      return null;
    }

    // 1. 创建一个临时Canvas，大小与原始图片相同
    const originalImage = cropperInstance.getImageData();
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalImage.naturalWidth;
    tempCanvas.height = originalImage.naturalHeight;

    // 2. 在临时Canvas上绘制原始图片
    const tempCtx = tempCanvas.getContext('2d');

    // 创建一个Image对象来加载原始图片
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 处理跨域问题

    // 使用Promise等待图片加载完成
    try {
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = (e) => {
          console.error('图片加载失败:', e);
          reject(new Error('图片加载失败，可能是跨域问题'));
        };

        // 添加超时处理
        const timeout = setTimeout(() => {
          reject(new Error('图片加载超时'));
        }, 10000); // 10秒超时

        img.onload = () => {
          clearTimeout(timeout);
          resolve();
        };

        // 确保URL有效
        if (!tempCoverData.value || !tempCoverData.value.imageUrl) {
          reject(new Error('图片URL无效'));
          return;
        }

        img.src = tempCoverData.value.imageUrl;
      });
    } catch (error) {
      console.error('图片加载错误:', error);
      ElMessage.error('图片加载失败: ' + (error.message || '未知错误'));
      return null;
    }

    // 绘制原始图片到临时Canvas
    try {
      tempCtx.drawImage(img, 0, 0, originalImage.naturalWidth, originalImage.naturalHeight);
    } catch (error) {
      console.error('绘制图片到Canvas失败:', error);
      ElMessage.error('处理图片失败: ' + (error.message || '未知错误'));
      return null;
    }

    // 3. 在临时Canvas上绘制文本
    if (tempCoverData.value.texts && tempCoverData.value.texts.length > 0) {
      // 计算图片缩放比例（原始尺寸与显示尺寸的比例）
      const scaleX = originalImage.naturalWidth / originalImage.width;

      tempCoverData.value.texts.forEach(text => {
        tempCtx.save();

        // 设置文本样式
        tempCtx.fillStyle = text.color;
        // 根据原始图片尺寸调整字体大小
        const fontSizeInPixels = Math.round(text.size * 16 * scaleX);
        tempCtx.font = `bold ${fontSizeInPixels}px Arial, sans-serif`;
        tempCtx.textAlign = 'center';
        tempCtx.textBaseline = 'middle';
        tempCtx.shadowColor = 'rgba(0, 0, 0, 0.7)';
        tempCtx.shadowBlur = 4;
        tempCtx.shadowOffsetX = 1;
        tempCtx.shadowOffsetY = 1;

        // 计算文本在原始图片上的位置
        // 文本位置是相对于容器的百分比
        const textX = (text.x / 100) * originalImage.naturalWidth;
        const textY = (text.y / 100) * originalImage.naturalHeight;

        // 绘制文本
        tempCtx.fillText(text.content, textX, textY);

        tempCtx.restore();
      });
    }

    // 4. 将临时Canvas的内容替换为裁剪器的图片源
    const tempDataUrl = tempCanvas.toDataURL('image/jpeg', 0.95);

    // 5. 重新初始化裁剪器，使用合成后的图片
    // 保存当前裁剪框数据
    const currentCropBoxData = cropperInstance.getCropBoxData();

    // 销毁当前裁剪器
    cropperInstance.destroy();

    // 设置新图片源
    cropperImgRef.value.src = tempDataUrl;

    // 等待图片加载完成
    await new Promise(resolve => {
      cropperImgRef.value.onload = resolve;
    });

    // 重新初始化裁剪器
    initCropper();

    // 等待裁剪器初始化完成
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      // 恢复裁剪框位置和大小
      if (cropperInstance) {
        cropperInstance.setCropBoxData(currentCropBoxData);
      } else {
        console.warn('裁剪器未初始化，无法恢复裁剪框');
      }
    } catch (error) {
      console.warn('恢复裁剪框失败:', error);
      // 继续执行，不中断流程
    }

    // 再次等待以确保裁剪器稳定
    await new Promise(resolve => setTimeout(resolve, 200));

    // 6. 获取裁剪后的图片
    if (!cropperInstance) {
      ElMessage.warning('裁剪器未初始化，无法获取裁剪图片');
      return null;
    }

    let croppedCanvas;
    try {
      croppedCanvas = cropperInstance.getCroppedCanvas({ width: 1280, height: 720 });
    } catch (error) {
      console.error('获取裁剪Canvas失败:', error);
      ElMessage.warning('获取裁剪图片失败: ' + (error.message || '未知错误'));
      return null;
    }

    if (!croppedCanvas) {
      ElMessage.warning('获取裁剪图片失败');
      return null;
    }

    // 7. 导出最终图片
    const finalBlob = await new Promise(resolve => {
      croppedCanvas.toBlob(resolve, 'image/jpeg', 0.95);
    });

    if (!finalBlob) {
      ElMessage.warning('生成图片失败');
      return null;
    }

    return finalBlob;
  } catch (error) {
    console.error('生成合成封面时出错:', error);
    ElMessage.error('生成封面失败: ' + (error.message || '未知错误'));
    return null;
  }
}

// 保存封面设置
async function saveCoverSettings() {
  if (isProcessing.value) return; // 防止重复点击

  try {
    // 设置处理状态为true，激活loading效果
    isProcessing.value = true;

    // 使用Element Plus的全屏加载
    const loading = ElLoading.service({
      lock: true,
      text: '正在处理封面图片...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      // 检查裁剪器是否初始化
      if (!cropperInstance) {
        loading.close();
        ElMessage.error('裁剪器未初始化，请重新打开编辑器');
        isProcessing.value = false;
        return;
      }

      // 生成合成图片
      const compositeBlob = await generateCompositeCover();

      if (!compositeBlob) {
        loading.close();
        ElMessage.error('无法生成封面图片，请重试');
        isProcessing.value = false;
        return;
      }

      // 更新加载提示
      loading.setText('正在上传封面...');

      try {
        // 创建文件对象
        const file = new File([compositeBlob], 'composite-cover.jpg', { type: 'image/jpeg' });

        // 上传合成图片
        const result = await imageService.uploadImage(file, 'cover', {
          nodeId: props.nodeId,
          purpose: 'video_cover_composite'
        });

        if (result.success) {
          // 保存临时数据中的持续时间
          const tempDuration = tempCoverData.value.duration || params.value.cover.duration;

          // 重置封面数据，只保留最终合成图片和必要字段
          params.value.cover = {
            imageUrl: result.data.ossUrl,
            duration: tempDuration
          };

          // 更新节点参数
          handleSettingsChange();

          // 关闭加载
          loading.close();
          ElMessage.success('封面设置已保存');

          // 重置裁剪数据
          cropData.coordinates = null;
          cropData.canvas = null;

          // 成功后关闭对话框
          coverEditorVisible.value = false;
          // 清空临时数据
          tempCoverData.value = null;
        } else {
          loading.close();
          ElMessage.error('合成图片上传失败: ' + (result.message || '未知错误'));
        }
      } catch (uploadError) {
        loading.close();
        console.error('上传封面图片时出错:', uploadError);
        ElMessage.error('上传封面失败: ' + (uploadError.message || '未知错误'));
      }
    } catch (processError) {
      loading.close();
      console.error('处理封面图片时出错:', processError);
      ElMessage.error('处理封面失败: ' + (processError.message || '未知错误'));
    }
  } catch (error) {
    console.error('保存封面设置时出错:', error);
    ElMessage.error('保存封面设置失败: ' + (error.message || '未知错误'));
  } finally {
    // 无论成功还是失败，都将处理状态设置为false
    isProcessing.value = false;
  }
}

// 移除封面图片（节点上的）
function removeCoverImage() {
  // 确保 params.value.cover 存在
  if (!params.value.cover) {
    params.value.cover = {
      imageUrl: "",
      duration: 1.5
    };
    handleSettingsChange();
    return;
  }

  // 完全重置封面数据，保留默认展示时长
  const duration = params.value.cover.duration || 1; // 修改默认值为1秒
  // 只保留必要的字段，完全清除其他字段
  params.value.cover = {
    imageUrl: '',
    duration: duration
  };
  handleSettingsChange();
}

// 移除临时封面图片（编辑器中的）
function removeTempCoverImage() {
  if (tempCoverData.value) {
    tempCoverData.value.imageUrl = '';
  }
}

// 处理设置变化
function handleSettingsChange() {
  nodeStore.updateNodeParams(props.nodeId, params.value);
}

// 移除源节点
function removeSourceNode(sourceId) {
  nodeStore.disconnectNodes(sourceId, props.nodeId);
}

// 开始拖动文本元素
function startDrag(event, textId) {
  event.preventDefault();
  event.stopPropagation();

  // 确保临时数据和texts数组存在
  if (!tempCoverData.value || !tempCoverData.value.texts) {
    return;
  }

  // 获取文本元素
  const text = tempCoverData.value.texts.find(t => t.id === textId);
  if (!text) {
    console.warn('未找到文本元素:', textId);
    return;
  }

  // 确保文本元素有x和y坐标
  if (text.x === undefined) text.x = 50;
  if (text.y === undefined) text.y = 50;

  // 设置拖动状态
  dragState.isDragging = true;
  dragState.currentTextId = textId;
  dragState.startX = event.clientX;
  dragState.startY = event.clientY;
  dragState.elementX = text.x;
  dragState.elementY = text.y;

  // 添加事件监听
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
}

// 拖动过程
function onDrag(event) {
  if (!dragState.isDragging || !dragState.currentTextId) return;

  try {
    // 获取文本元素层容器
    const container = document.querySelector('.text-elements-layer');
    if (!container) {
      console.warn('未找到文本元素层容器');
      return;
    }

    // 计算容器的尺寸
    const rect = container.getBoundingClientRect();
    if (rect.width === 0 || rect.height === 0) {
      console.warn('容器尺寸无效');
      return;
    }

    // 计算鼠标移动的距离（相对于容器的百分比）
    const deltaXPercent = ((event.clientX - dragState.startX) / rect.width) * 100;
    const deltaYPercent = ((event.clientY - dragState.startY) / rect.height) * 100;

    // 计算新位置（限制在0-100范围内）
    let newX = Math.max(0, Math.min(100, dragState.elementX + deltaXPercent));
    let newY = Math.max(0, Math.min(100, dragState.elementY + deltaYPercent));

    // 直接更新文本元素位置
    if (tempCoverData.value && tempCoverData.value.texts) {
      const text = tempCoverData.value.texts.find(t => t.id === dragState.currentTextId);
      if (text) {
        text.x = newX;
        text.y = newY;

        // 重置拖动起始点，使拖动更流畅
        dragState.startX = event.clientX;
        dragState.startY = event.clientY;
        dragState.elementX = newX;
        dragState.elementY = newY;
      }
    }
  } catch (error) {
    console.error('拖动文本元素时出错:', error);
    stopDrag(); // 出错时停止拖动
  }
}

// 停止拖动
function stopDrag() {
  if (!dragState.isDragging) return;

  dragState.isDragging = false;

  // 移除事件监听
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

// 处理位置输入框变化
function handlePositionChange() {
  // 确保值在0-100范围内
  if (tempCoverData.value && tempCoverData.value.texts) {
    tempCoverData.value.texts.forEach(text => {
      text.x = Math.max(0, Math.min(100, text.x));
      text.y = Math.max(0, Math.min(100, text.y));
    });
  }
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  if (cropperInstance) {
    cropperInstance.destroy();
    cropperInstance = null;
  }
});
</script>

<style scoped>
.scroll-container {
  max-height: 35rem;
  /* 设置最大高度 */
  overflow-y: auto;
  /* 启用垂直滚动 */
  overflow-x: hidden;
}

.video-config-node-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem;
  height: auto;
  /* 自适应高度 */
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.25rem;
}

.source-info {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.source-tag {
  margin-right: 0;
}



.image-selector {
  margin-top: 0.5rem;
}

.image-preview {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}



.settings-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow-y: visible;
  /* 确保内容可以溢出并滚动 */
}

.settings-group {
  border: 0.0625rem solid #e4e7ed;
  border-radius: 0.25rem;
  padding: 0.625rem;
  background-color: #f9f9f9;
  margin-bottom: 0.5rem;
  /* 添加底部间距 */
}

.group-title {
  font-weight: bold;
  color: #303133;
  font-size: 0.875rem;
}

.collapsible-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding-bottom: 0.375rem;
  margin-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #ebeef5;
  position: relative;
}

.header-actions {
  position: absolute;
  right: 1.5rem;
  top: 0;
  z-index: 10;
}

.collapsible-header:hover {
  background-color: #f5f7fa;
}

.collapse-icon {
  transition: transform 0.3s;
  font-size: 0.875rem;
  color: #909399;
}

.collapse-icon.is-active {
  transform: rotate(180deg);
}

/* 封面设置样式 */
.cover-settings {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.125rem 0;
  max-height: 12rem;
  /* 限制最大高度 */
  overflow-y: auto;
  /* 启用垂直滚动 */
}

.duration-setting {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.setting-label {
  font-size: 0.875rem;
  color: #606266;
  min-width: 5rem;
}

.cover-text-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.text-style-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding-left: 0.5rem;
}

.setting-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.size-value {
  font-size: 0.875rem;
  color: #606266;
  min-width: 3rem;
}

.size-display {
  font-size: 0.875rem;
  color: #606266;
  margin-left: 0.5rem;
  min-width: 3rem;
  text-align: right;
}

.slider-with-value {
  display: flex;
  align-items: center;
  width: 100%;
}

.image-container {
  position: relative;
  max-width: 100%;
  max-height: 8rem;
  overflow: hidden;
}

.cover-text-element {
  position: absolute;
  transform: translate(-50%, -50%);
  color: #FFFFFF;
  z-index: 1;
  cursor: move;
  user-select: none;
  min-width: 2rem;
  text-align: center;
  /* 移除文本限制，允许文本完整显示 */
  white-space: nowrap;
  overflow: visible;
  /* 移除宽度限制 */
  text-shadow: 0 0 0.1875rem rgba(0, 0, 0, 0.9);
  /* 文字阴影，增强可读性 */
  font-weight: bold;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

/* 版权信息样式 */
.copyright-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.position-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.position-buttons .el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.position-buttons .el-radio-button {
  margin-right: 0;
}

/* 封面基本信息样式 */
.cover-basic-info {
  margin-bottom: 0.5rem;
  /* 减小底部间距 */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 居中显示 */
}

.cover-preview {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.cover-preview-container {
  position: relative;
  width: 120px;
  /* 减小宽度 */
  height: 67.5px;
  /* 保持16:9比例 */
  border-radius: 0.25rem;
  border: 0.0625rem solid #dcdfe6;
  /* 1px -> 0.0625rem */
  overflow: hidden;
  background-color: #f0f0f0;
}

.cover-preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 预览中的裁剪区域 */
.preview-crop-area {
  position: absolute;
  border: 1px dashed #409eff;
  background-color: rgba(64, 158, 255, 0.05);
  box-sizing: border-box;
  overflow: hidden;
}

/* 预览中的文本元素 */
.preview-text-element {
  position: absolute;
  transform: translate(-50%, -50%);
  padding: 0.125rem 0.25rem;
  color: #FFFFFF;
  /* 移除文本限制，允许文本完整显示 */
  white-space: nowrap;
  overflow: visible;
  text-align: center;
  font-size: 0.75rem;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

.cover-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cover-info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: #606266;
  min-width: 5rem;
}

.info-value {
  font-size: 0.875rem;
  color: #303133;
  font-weight: bold;
}

.cover-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  justify-content: center;
  /* 居中显示按钮 */
}

.cover-empty {
  padding: 1rem;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 10rem;
}

.add-cover-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 0;
}

.cover-tip {
  margin-top: 0.5rem;
  color: #909399;
  font-size: 0.875rem;
}

/* 封面编辑器样式 */
.cover-editor {
  display: flex;
  gap: 1rem;
  height: 50vh;
  /* 大幅减小整体高度 */
  min-height: 25rem;
  /* 400px -> 25rem */
  max-height: 55vh;
  /* 减小最大高度 */
  overflow: hidden;
}

.cover-editor-preview {
  flex: 3;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow: hidden;
  /* 确保预览区域有足够的宽度 */
}

.cover-editor-container {
  position: relative;
  border: 0.0625rem solid #dcdfe6;
  /* 1px -> 0.0625rem */
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: #333;
  /* 更深的背景色，让图片更突出 */
  height: auto;
  /* 自适应高度 */
  display: flex;
  /* 使用 flex 布局 */
  flex-direction: column;
  /* 垂直方向排列子元素 */
}

.cover-image-container {
  position: relative;
  width: 100%;
  height: 20rem;
  /* 增加高度到20rem */
  overflow: hidden;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 裁剪器包装容器样式 */
.cropper-wrapper.aspect-16-9 {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #222;
  overflow: hidden;
  border-radius: 0.25rem;
}

.cropper-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  background: #222;
}

/* 裁剪器样式优化 */
:deep(.vue-advanced-cropper__foreground) {
  border: 2px solid rgba(255, 255, 255, 0.8);
}

:deep(.vue-advanced-cropper__grid) {
  border-color: rgba(255, 255, 255, 0.3);
}

:deep(.vue-advanced-cropper__stencil) {
  border: 2px solid #409EFF;
}

:deep(.vue-advanced-cropper__handlers) {
  background-color: #409EFF;
  border: 2px solid #fff;
}

:deep(.vue-advanced-cropper__line) {
  background-color: rgba(255, 255, 255, 0.5);
}

/* 裁剪器样式 */
.cropper {
  height: 100%;
  width: 100%;
  background: #DDD;
}





/* 文本元素层 */
.text-elements-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3000;
  /* 提高z-index，确保在vue-advanced-cropper的所有元素上方 */
  pointer-events: none;
  /* 允许点击穿透到裁剪器 */
}

/* 文本元素样式 */
.cover-text-element {
  position: absolute;
  transform: translate(-50%, -50%);
  cursor: move;
  user-select: none;
  white-space: nowrap;
  text-shadow: 0 0 0.1875rem rgba(0, 0, 0, 0.8);
  /* 3px -> 0.1875rem */
  pointer-events: auto;
  /* 恢复文本元素的点击事件 */
  font-weight: bold;
  z-index: 3100;
  /* 确保在裁剪框上方 */
  /* 移除内边距和背景色 */
}

.cover-upload-placeholder {
  width: 100%;
  height: 20rem;
  /* 增加高度到20rem */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #DDD;
  border-radius: 0.25rem;
  overflow: hidden;
  cursor: pointer;
  /* 添加指针样式，提示可点击 */
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-content {
  text-align: center;
  padding: 1rem;
}

.upload-icon {
  font-size: 2.5rem;
  color: #409EFF;
  margin-bottom: 1rem;
}

.upload-text {
  font-size: 1rem;
  color: #606266;
  margin-bottom: 0.5rem;
}

.upload-tip {
  font-size: 0.875rem;
  color: #909399;
}

.upload-area {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-content {
  text-align: center;
  padding: 1.5rem;
}

.upload-icon {
  font-size: 2.5rem;
  color: #c0c4cc;
  margin-bottom: 0.75rem;
}

.upload-text {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.5rem;
}

.upload-tip {
  font-size: 0.75rem;
  color: #909399;
}

.cover-image-actions {
  display: flex;
  gap: 0.5rem;
  padding: 0.25rem 0;
}

.cover-editor-panel {
  flex: 2;
  /* 增加编辑面板的比例 */
  overflow-y: auto;
  padding: 0.75rem;
  background-color: #f9f9f9;
  border-left: 1px solid #e4e7ed;
  max-width: 40%;
  /* 增加编辑面板的最大宽度 */
  /* 限制编辑面板的最大宽度 */
}

.editor-section {
  margin-bottom: 1.5rem;
  background-color: #fff;
  border-radius: 0.25rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.editor-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.editor-section-title {
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
  color: #303133;
}

.text-elements-list {
  padding: 0.5rem;
}

.text-element-card {
  border: 1px solid #e4e7ed;
  border-radius: 0.25rem;
  margin-bottom: 0.75rem;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.text-element-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  background-color: #f5f7fa;
  cursor: pointer;
  border-bottom: 1px solid #e4e7ed;
}

.text-element-header:hover {
  background-color: #ecf5ff;
}

.text-element-title {
  font-weight: 500;
  color: #303133;
  font-size: 0.875rem;
}

.text-element-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.text-element-content {
  padding: 0.75rem;
  background-color: #fff;
}

.size-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%
}

.slider-with-value {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.5rem;
}

.text-size-slider {
  flex: 1;
}

.size-display {
  min-width: 2rem;
  text-align: center;
  font-size: 0.875rem;
  color: #606266;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 0.25rem;
  padding: 0.125rem 0.375rem;
}

.size-slider-container {
  flex: 1;
}

.size-input {
  width: 5rem;
}

.size-unit {
  color: #606266;
  font-size: 0.875rem;
  margin-left: 0.25rem;
}

.position-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.position-inputs {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.position-inputs-vertical {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.position-input-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.position-inputs-horizontal {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-direction: row;
}

.position-label {
  font-size: 0.875rem;
  color: #606266;
  width: 1.5rem;
  text-align: right;
}

.position-unit {
  font-size: 0.875rem;
  color: #606266;
}

.position-values {
  display: flex;
  gap: 1rem;
}

.position-value {
  font-size: 0.875rem;
  color: #303133;
}

.position-hint {
  font-size: 0.75rem;
  color: #909399;
  font-style: italic;
  margin-top: 0.25rem;
}

.compact-label {
  font-size: 0.875rem;
  color: #606266;
  font-weight: normal;
}

.no-text-elements {
  padding: 1rem 0;
  text-align: center;
}

/* 文本元素样式 */
.editor-text {
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
  /* 移除文本限制，允许文本完整显示 */
  white-space: nowrap;
  overflow: visible;
  /* 移除宽度限制 */
}

/* 16:9参考线 */
.aspect-ratio-guide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  z-index: 2;
  /* 确保在文本元素上方 */
}

.aspect-ratio-guide::before {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  border: 2px dashed rgba(64, 158, 255, 0.7);
  position: absolute;
  box-sizing: border-box;
}

.aspect-ratio-text {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: #409eff;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  z-index: 3;
  /* 确保在参考线上方 */
}

/* 优化 cropperjs 裁剪框样式：半透明红色、圆角、无阴影，手柄和辅助线协调 */
:deep(.cropper-crop-box) {
  border: 0.15625rem solid rgba(255, 0, 0, 1) !important;
  border-radius: 0.5rem !important;
  box-shadow: none !important;
}

:deep(.cropper-view-box) {
  border: 0.15625rem solid rgba(255, 0, 0, 1) !important;
  border-radius: 0.5rem !important;
  box-shadow: none !important;
}

:deep(.cropper-line),
:deep(.cropper-point) {
  background: rgba(255, 69, 58, 0.7) !important;
}

:deep(.cropper-center) {
  background: rgba(255, 69, 58, 0.5) !important;
}

:deep(.cropper-dashed) {
  border-color: rgba(255, 69, 58, 0.25) !important;
}
</style>
