<template>
  <div class="playback-context-panel">
    <div class="timeline-items" ref="containerRef">
      <div v-for="(item, index) in filteredTimeline" :key="index" class="timeline-item"
        :class="{ 'active': index === getCurrentFilteredIndex }" :data-index="index" @click="selectItem(index)">
        <div class="item-time">{{ formatTime(item.actualStartTime !== undefined ? item.actualStartTime : item.startTime)
          }}</div>
        <div class="item-content">{{ item.content }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed } from 'vue';

const props = defineProps({
  timeline: {
    type: Array,
    required: true
  },
  currentIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['select-item']);
const containerRef = ref(null);

// 只过滤掉封面项，保留转场文本
const filteredTimeline = computed(() => {
  return props.timeline.filter(item => !item.isCover);
});

// 时间格式化
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 点击选中某项
const selectItem = (filteredIndex) => {
  // 获取过滤后的项目
  const filteredItem = filteredTimeline.value[filteredIndex];

  // 在原始时间线中查找对应的索引
  const originalIndex = props.timeline.findIndex(item => {
    // 使用actualStartTime（如果存在）或fallback到startTime
    const itemStartTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;
    const filteredItemStartTime = filteredItem.actualStartTime !== undefined ? filteredItem.actualStartTime : filteredItem.startTime;

    return itemStartTime === filteredItemStartTime && item.content === filteredItem.content;
  });

  // 如果找到了对应的索引，发送事件
  if (originalIndex !== -1) {
    emit('select-item', originalIndex);
  }
};

// 获取当前项在过滤后时间线中的索引
const getCurrentFilteredIndex = computed(() => {
  // 如果当前索引对应的是封面项，则返回-1
  if (props.timeline[props.currentIndex]?.isCover) {
    return -1;
  }

  // 获取当前项
  const currentItem = props.timeline[props.currentIndex];
  if (!currentItem) return -1;

  // 在过滤后的时间线中查找对应的索引
  return filteredTimeline.value.findIndex(item => {
    // 使用actualStartTime（如果存在）或fallback到startTime
    const itemStartTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;
    const currentItemStartTime = currentItem.actualStartTime !== undefined ? currentItem.actualStartTime : currentItem.startTime;

    return itemStartTime === currentItemStartTime && item.content === currentItem.content;
  });
});

const scrollToCurrentItem = () => {
  const container = containerRef.value;
  if (!container) return;

  // 获取当前项在过滤后时间线中的索引
  const filteredIndex = getCurrentFilteredIndex.value;
  if (filteredIndex === -1) return;

  const currentItem = container.querySelector(`.timeline-item[data-index="${filteredIndex}"]`);
  if (!currentItem) return;

  const containerRect = container.getBoundingClientRect();
  const itemRect = currentItem.getBoundingClientRect();

  const scrollTop = container.scrollTop + (itemRect.top - containerRect.top) - (container.clientHeight / 2) + (currentItem.clientHeight / 2);

  requestAnimationFrame(() => {
    container.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
  });
};
// 监听索引变化触发滚动
watch(() => props.currentIndex, async () => {
  await nextTick();
  scrollToCurrentItem();
}, { immediate: true });

// 初次挂载时滚动一次
onMounted(() => {
  nextTick(() => scrollToCurrentItem());
});
</script>


<style scoped>
.playback-context-panel {
  height: 100%;
  background-color: #fff;
  overflow: hidden;
}

.timeline-items {
  overflow-y: auto;
  padding: 10px;
  height: 100%;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.timeline-item {
  display: flex;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #f9f9f9;
  border-left: 3px solid transparent;
}

.timeline-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409EFF;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.item-time {
  min-width: 50px;
  color: #606266;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.item-content {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
  color: #303133;
}
</style>
