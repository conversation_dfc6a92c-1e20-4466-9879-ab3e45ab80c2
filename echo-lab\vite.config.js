import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import { fileURLToPath } from "url";
import Components from "unplugin-vue-components/vite";
import AutoImport from "unplugin-auto-import/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import { VitePWA } from "vite-plugin-pwa";
import { visualizer } from "rollup-plugin-visualizer";

const isProd = process.env.NODE_ENV === "production";
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig(() => {
  return {
    plugins: [
      vue({
        // 显式启用模板编译（确保 h 函数可用）
        template: {
          compilerOptions: {
            whitespace: "condense",
          },
        },
      }),
      // 自动导入Element Plus组件
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass", // 使用sass样式
          }),
        ],
        dts: "src/components.d.ts",
      }),
      // 自动导入Element Plus API
      AutoImport({
        resolvers: [ElementPlusResolver()],
        imports: ["vue", "vue-router"],
        dts: "src/auto-imports.d.ts",
      }),
      // PWA 配置
      VitePWA({
        registerType: "autoUpdate",
        includeAssets: ["favicon.ico", "icons/*.png"],
        includeManifestIcons: true,
        manifest: {
          id: "/",
          name: "Echo Lab - 日语听力练习",
          short_name: "Echo Lab",
          description: "专业的日语听力练习工具",
          theme_color: "#ff0000",
          background_color: "#ffffff",
          icons: [
            {
              src: "icons/logo-128.png",
              sizes: "128x128",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-192.png",
              sizes: "192x192",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-256.png",
              sizes: "256x256",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-512.png",
              sizes: "512x512",
              type: "image/png",
              purpose: "any",
            },
            {
              src: "icons/logo-192.png",
              sizes: "192x192",
              type: "image/png",
              purpose: "maskable",
            },
            {
              src: "icons/logo-512.png",
              sizes: "512x512",
              type: "image/png",
              purpose: "maskable",
            },
          ],
          display: "standalone",
          start_url: "/",
          scope: "/",
          // 确保manifest被正确注入
          includeAssetsManifest: true,
        },
        workbox: {
          // 增加文件大小限制到5MB
          maximumFileSizeToCacheInBytes: 5 * 1024 * 1024,
          // 缓存所有静态资源，包括JS、CSS、HTML、图标和媒体文件
          globPatterns: isProd
            ? [
                "**/*.{js,css,html,ico,png,jpg,jpeg,gif,svg,webp,woff,woff2,ttf,eot}",
              ]
            : [
                // 开发模式下只缓存基本文件
                "*.html",
                "icons/*.png",
              ],
          runtimeCaching: [
            {
              // 缓存所有API请求
              urlPattern: /^(https?:\/\/[^\/]+)?\/api\/.*/i,
              handler: "NetworkFirst", // 网络优先，确保获取最新数据
              options: {
                cacheName: "api-cache",
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24, // 1天
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
              },
            },
            {
              // 缓存所有资源文件（包括OSS资源和媒体文件）
              urlPattern:
                /^(https?:\/\/[^\/]+)?(\/oss-resources\/|.*\.(mp3|wav|ogg|m4a|jpg|jpeg|png|gif|webp|svg))$/i,
              handler: "CacheFirst", // 缓存优先策略
              options: {
                cacheName: "resources-cache",
                fetchOptions: {
                  credentials: "omit", // 不发送凭证
                },
                expiration: {
                  maxEntries: 1000, // 缓存条目数量
                  maxAgeSeconds: 60 * 60 * 24 * 30, // 30天
                },
                cacheableResponse: {
                  statuses: [0, 200],
                },
                rangeRequests: true, // 支持范围请求（对音频视频很重要）
              },
            },
          ],
        },
        // 开发模式下启用 PWA
        devOptions: {
          enabled: true, // 开发和生产环境都启用
          type: "module",
        },
        // 确保正确处理HTML
        injectRegister: "auto",
        injectManifest: {
          injectionPoint: undefined,
        },
      }),
      // Bundle分析器 - 只在需要时启用
      process.env.ANALYZE &&
        visualizer({
          filename: "dist/stats.html",
          open: true,
          gzipSize: true,
          brotliSize: true,
          template: "treemap", // 'treemap', 'sunburst', 'network'
        }),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    server: {
      port: 3001,
      host: "0.0.0.0",
      open: true,
      hmr: {
        overlay: false, // 禁用错误覆盖层
      },
      proxy: {
        "/api": {
          target: "http://localhost:3000",
          changeOrigin: true,
        },
        "/oss-resources": {
          target: "https://echolab.oss-cn-hongkong.aliyuncs.com/",
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/oss-resources/, ""),
        },
      },
    },
    define: {
      // Vue 特性标志
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: !isProd,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: !isProd,
      // 修复 isFunction 初始化问题
      "process.env.NODE_ENV": JSON.stringify(
        process.env.NODE_ENV || "development"
      ),
    },
    esbuild: {
      target: "es2015",
      jsx: "transform",
      keepNames: true,
    },
    build: {
      minify: isProd ? "terser" : false,
      sourcemap: false,
      terserOptions: isProd
        ? {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
            format: {
              comments: false,
            },
            mangle: true,
          }
        : undefined,
      rollupOptions: {
        output: {
          // 保持依赖关系正确的代码分割
          manualChunks: {
            "vue-core": ["vue"],
            vendor: [
              "element-plus",
              "@element-plus/icons-vue",
              "lodash-es",
              "axios",
              "crypto-js",
            ],
          },
        },
      },
      target: "es2015",
    },
    optimizeDeps: {
      include: [
        // 核心依赖预构建
        "vue",
        "vue-router",
        "pinia",
        "element-plus",
        "@element-plus/icons-vue",
        "lodash-es",
        "axios",
        "crypto-js",
        "uuid",
      ],
      exclude: [
        // 排除大型库，让它们按需加载
        "mp4-muxer",
        "html2canvas",
        "dom-to-image-more",
        "cropperjs",
        "vue-advanced-cropper",
        "jszip",
      ],
    },
  };
});
