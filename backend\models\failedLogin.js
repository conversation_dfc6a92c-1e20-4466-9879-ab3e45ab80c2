/**
 * 失败登录模型
 * 用于记录登录失败信息，防止暴力破解
 */
module.exports = (sequelize, DataTypes) => {
  const FailedLogin = sequelize.define(
    "FailedLogin",
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ip: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: "IP地址",
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "尝试登录的邮箱",
      },
      userAgent: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "用户代理",
      },
      reason: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: "失败原因",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        comment: "创建时间",
      },
    },
    {
      tableName: "failed_logins",
      timestamps: true,
      updatedAt: false, // 不需要更新时间
      indexes: [
        {
          name: "idx_failed_logins_ip",
          fields: ["ip"],
        },
        {
          name: "idx_failed_logins_email",
          fields: ["email"],
        },
        {
          name: "idx_failed_logins_created_at",
          fields: ["createdAt"],
        },
      ],
    }
  );

  // 添加自动清理过期记录的方法
  FailedLogin.cleanupExpiredRecords = async function (days = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    try {
      const deletedCount = await this.destroy({
        where: {
          createdAt: {
            [sequelize.Sequelize.Op.lt]: cutoffDate,
          },
        },
      });

      console.log(`[FailedLogin] 已清理 ${deletedCount} 条过期的失败登录记录`);
      return deletedCount;
    } catch (error) {
      console.error("[FailedLogin] 清理过期记录失败:", error);
      throw error;
    }
  };

  return FailedLogin;
};
