<template>
  <div class="template-management-view">
    <SmartPageHeader title="播放策略模板">
      <template #actions>
        <p class="header-description">
          创建和管理您的播放策略模板，提升学习效率
        </p>
      </template>
    </SmartPageHeader>

    <!-- 主要内容 -->
    <div class="page-content">
      <TemplateManager />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import SmartPageHeader from '@/components/common/SmartPageHeader.vue';
import TemplateManager from '@/components/template/TemplateManager.vue';
import { useTemplateStore } from '@/stores/templateStore';

// 页面标题
document.title = '模板管理 - Echo Lab';

// Stores
const templateStore = useTemplateStore();

// 页面挂载时加载模板数据
onMounted(() => {
  templateStore.loadTemplates();
});
</script>

<style scoped>
.template-management-view {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-description {
  font-size: 0.9rem;
  color: #606266;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 1rem;
  }

  .header-description {
    font-size: 0.8rem;
  }
}
</style>
