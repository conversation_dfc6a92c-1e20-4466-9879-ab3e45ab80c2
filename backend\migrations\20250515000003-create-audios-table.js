'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    
    // 如果表已存在，则跳过创建
    if (!tables.includes('audios')) {
      // 创建音频表
      await queryInterface.createTable('audios', {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true
        },
        text: {
          type: Sequelize.TEXT,
          allowNull: false
        },
        language: {
          type: Sequelize.STRING(10),
          allowNull: false
        },
        speed: {
          type: Sequelize.FLOAT,
          defaultValue: 1.0
        },
        speaker: {
          type: Sequelize.STRING(50),
          allowNull: false
        },
        oss_url: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        oss_key: {
          type: Sequelize.STRING(255),
          allowNull: false
        },
        duration: {
          type: Sequelize.FLOAT,
          allowNull: false
        },
        md5_hash: {
          type: Sequelize.STRING(32),
          allowNull: false
        },
        created_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updated_at: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
        }
      });

      // 添加索引
      await queryInterface.addIndex('audios', ['md5_hash'], {
        name: 'idx_md5_hash'
      });
      await queryInterface.addIndex('audios', ['language'], {
        name: 'idx_language'
      });
      await queryInterface.addIndex('audios', ['created_at'], {
        name: 'idx_created_at'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('audios');
  }
};
