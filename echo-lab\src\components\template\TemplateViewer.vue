<template>
  <el-drawer v-model="drawerVisible" :title="null" direction="rtl" size="70%" class="template-viewer-drawer"
    @close="handleClose">
    <div class="template-viewer">
      <!-- 头部标题 -->
      <div class="viewer-header">
        <h1 class="viewer-title">
          {{ isEditing ? '编辑模板' : (template ? '模板详情' : '新建模板') }}
        </h1>
        <div class="header-actions" v-if="!readonly && template">
          <el-tag :class="getTypeClass(template.type)" size="small">
            {{ getTypeText(template.type) }}
          </el-tag>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="viewer-content">
        <!-- 只读模式：显示模板信息 -->
        <template v-if="readonly && template">
          <!-- 模板基本信息 -->
          <div class="info-card">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">模板名称</div>
                <div class="info-value">{{ template.name }}</div>
              </div>
              <div class="info-item" v-if="template.description">
                <div class="info-label">模板描述</div>
                <div class="info-value">{{ template.description }}</div>
              </div>
              <div class="info-item" v-if="template.creator">
                <div class="info-label">创建者</div>
                <div class="info-value">{{ template.creator.username }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">创建时间</div>
                <div class="info-value">{{ formatDate(template.createdAt) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">环节数量</div>
                <div class="info-value">{{ template.config?.sections?.length || 0 }}个</div>
              </div>
              <div class="info-item">
                <div class="info-label">使用次数</div>
                <div class="info-value">{{ template.usageCount || 0 }}次</div>
              </div>
            </div>
          </div>
        </template>

        <!-- 编辑模式：表单 -->
        <template v-if="!readonly">
          <el-form ref="formRef" :model="templateForm" :rules="formRules" label-width="80px" class="template-form">
            <!-- 基本信息 -->
            <div class="form-card">
              <h3 class="card-title">基本信息</h3>

              <el-form-item label="名称" prop="name">
                <el-input v-model="templateForm.name" placeholder="请输入模板名称" maxlength="100" show-word-limit />
              </el-form-item>

              <el-form-item label="描述" prop="description">
                <el-input v-model="templateForm.description" type="textarea" :rows="2" placeholder="请描述这个模板的特点和适用场景"
                  maxlength="500" show-word-limit />
              </el-form-item>

              <el-form-item label="公开" v-if="isLoggedIn">
                <el-switch v-model="templateForm.isPublic" active-text="公开" inactive-text="私有" />
                <div class="form-help">公开后其他用户可以使用这个模板</div>
              </el-form-item>
            </div>
          </el-form>
        </template>

        <!-- 环节配置（只读和编辑都显示） -->
        <div class="sections-card">
          <div class="card-header">
            <h3 class="card-title">环节配置</h3>
            <div class="header-actions">
              <el-button v-if="sections.length > 1" size="small" text @click="toggleAllSections"
                :title="allCollapsed ? '全部展开' : '全部折叠'">
                <el-icon>
                  <ArrowDown v-if="allCollapsed" />
                  <ArrowUp v-else />
                </el-icon>
                {{ allCollapsed ? '全部展开' : '全部折叠' }}
              </el-button>

              <el-button v-if="!readonly" type="primary" size="small" @click="addSection">
                <el-icon>
                  <Plus />
                </el-icon>
                添加环节
              </el-button>
            </div>
          </div>

          <div class="sections-container">
            <!-- 编辑模式：支持拖拽 -->
            <draggable v-if="!readonly" v-model="templateForm.config.sections" item-key="title" handle=".drag-handle"
              class="sections-list" ghost-class="section-ghost" chosen-class="section-chosen" drag-class="section-drag">
              <template #item="{ element: section, index }">
                <div class="section-item" :class="{ collapsed: collapsedSections.has(index) }">
                  <!-- 环节头部 -->
                  <div class="section-header">
                    <div class="section-controls">
                      <div class="drag-handle" title="拖拽排序">
                        <el-icon>
                          <Operation />
                        </el-icon>
                      </div>
                      <div class="section-number">{{ index + 1 }}</div>
                    </div>

                    <div class="section-info">
                      <h4 class="section-title">{{ section.title || `环节 ${index + 1}` }}</h4>
                      <div class="section-summary">{{ getSectionSummary(section) }}</div>
                    </div>

                    <div class="section-actions">
                      <el-button size="small" text @click="toggleSection(index)"
                        :title="collapsedSections.has(index) ? '展开' : '折叠'">
                        <el-icon>
                          <ArrowUp v-if="!collapsedSections.has(index)" />
                          <ArrowDown v-else />
                        </el-icon>
                      </el-button>

                      <el-button v-if="sections.length > 1" size="small" type="danger" text
                        @click="removeSection(index)" title="删除">
                        删除
                      </el-button>
                    </div>
                  </div>

                  <!-- 环节内容 -->
                  <div v-show="!collapsedSections.has(index)" class="section-content">
                    <SectionConfigForm v-model="templateForm.config.sections[index]" :section-index="index" />
                  </div>
                </div>
              </template>
            </draggable>

            <!-- 只读模式 -->
            <div v-else class="sections-list">
              <div v-for="(section, index) in sections" :key="index" class="section-item"
                :class="{ collapsed: collapsedSections.has(index) }">
                <!-- 环节头部 -->
                <div class="section-header">
                  <div class="section-number">{{ index + 1 }}</div>
                  <div class="section-info">
                    <h4 class="section-title">{{ section.title || `环节 ${index + 1}` }}</h4>
                    <div class="section-summary">{{ getSectionSummary(section) }}</div>
                  </div>
                  <div class="section-actions">
                    <el-button size="small" text @click="toggleSection(index)"
                      :title="collapsedSections.has(index) ? '展开' : '折叠'">
                      <el-icon>
                        <ArrowUp v-if="!collapsedSections.has(index)" />
                        <ArrowDown v-else />
                      </el-icon>
                    </el-button>
                  </div>
                </div>

                <!-- 环节内容 -->
                <div v-show="!collapsedSections.has(index)" class="section-content">
                  <SectionConfigDisplay :section="section" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">
          {{ readonly ? '关闭' : '取消' }}
        </el-button>

        <template v-if="readonly && template && canDuplicate">
          <el-button type="primary" @click="duplicateTemplate">
            复制模板
          </el-button>
        </template>

        <template v-if="!readonly">
          <el-button type="primary" @click="saveTemplate" :loading="saving">
            {{ isEditing ? '更新模板' : '创建模板' }}
          </el-button>
        </template>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { Plus, ArrowUp, ArrowDown, Operation } from '@element-plus/icons-vue';
import { useTemplateStore } from '@/stores/templateStore';
import { useUserStore } from '@/stores/userStore';
import SectionConfigForm from './SectionConfigForm.vue';
import SectionConfigDisplay from './SectionConfigDisplay.vue';
import templateService from '@/services/templateService';
import draggable from 'vuedraggable';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  template: {
    type: Object,
    default: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'saved']);

// Stores
const templateStore = useTemplateStore();
const userStore = useUserStore();

// Refs
const formRef = ref(null);

// State
const saving = ref(false);
const collapsedSections = ref(new Set()); // 折叠的环节索引
const templateForm = ref({
  name: '',
  description: '',
  isPublic: false,
  config: {
    sections: []
  }
});

// Computed
const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEditing = computed(() => !!props.template && !props.readonly);
const isLoggedIn = computed(() => userStore.isLoggedIn);

const sections = computed(() => {
  if (props.readonly && props.template) {
    return props.template.config?.sections || [];
  }
  return templateForm.value.config.sections;
});

const canDuplicate = computed(() => {
  if (!props.template || !userStore.isLoggedIn) return false;
  if (props.template.type === 'system') return true;
  return props.template.userId !== userStore.user?.id;
});

const allCollapsed = computed(() => {
  return sections.value.length > 0 && collapsedSections.value.size === sections.value.length;
});

// Form rules
const formRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 1, max: 100, message: '模板名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
};

// Methods
const initForm = () => {
  if (props.template && !props.readonly) {
    // 编辑模式
    const config = JSON.parse(JSON.stringify(props.template.config || { sections: [] }));
    // 确保每个section都有title
    config.sections = config.sections.map((section, index) => ({
      ...section,
      title: section.title || `环节 ${index + 1}`
    }));

    templateForm.value = {
      name: props.template.name || '',
      description: props.template.description || '',
      isPublic: props.template.isPublic || false,
      config
    };
  } else if (!props.readonly) {
    // 新建模式
    const defaultConfig = templateService.createDefaultConfig(1);
    // 确保默认section有title
    defaultConfig.sections = defaultConfig.sections.map((section, index) => ({
      ...section,
      title: section.title || `环节 ${index + 1}`
    }));

    templateForm.value = {
      name: '',
      description: '',
      isPublic: false,
      config: defaultConfig
    };
  }

  // 重置折叠状态
  collapsedSections.value.clear();
};

const addSection = () => {
  const sectionCount = templateForm.value.config.sections.length;
  const defaultSection = {
    title: `环节 ${sectionCount + 1}`, // 添加可编辑的标题
    speed: 1.0,
    pauseDuration: 3000,
    repeatCount: 4,
    enableTranslation: false,
    translationLanguage: '',
    translationPosition: 2,
    enableKeywords: false,
    keywordRepeatCount: 2,
    keywordPosition: 2,
    keywordSpeed: 1.0,
    repeatSpeeds: [1.0, 1.0, 1.0, 1.0],
    repeatPauses: [3000, 3000, 3000, 3000]
  };

  templateForm.value.config.sections.push(defaultSection);
};

const removeSection = (index) => {
  if (templateForm.value.config.sections.length > 1) {
    templateForm.value.config.sections.splice(index, 1);
    // 移除对应的折叠状态
    collapsedSections.value.delete(index);
    // 更新后续索引的折叠状态
    const newCollapsed = new Set();
    collapsedSections.value.forEach(i => {
      if (i < index) {
        newCollapsed.add(i);
      } else if (i > index) {
        newCollapsed.add(i - 1);
      }
    });
    collapsedSections.value = newCollapsed;
  }
};

const toggleSection = (index) => {
  if (collapsedSections.value.has(index)) {
    collapsedSections.value.delete(index);
  } else {
    collapsedSections.value.add(index);
  }
};

const toggleAllSections = () => {
  if (allCollapsed.value) {
    // 全部展开
    collapsedSections.value.clear();
  } else {
    // 全部折叠
    collapsedSections.value.clear();
    for (let i = 0; i < sections.value.length; i++) {
      collapsedSections.value.add(i);
    }
  }
};

const moveSectionUp = (index) => {
  if (index > 0) {
    const sections = templateForm.value.config.sections;
    [sections[index - 1], sections[index]] = [sections[index], sections[index - 1]];

    // 更新折叠状态
    const wasCurrentCollapsed = collapsedSections.value.has(index);
    const wasPrevCollapsed = collapsedSections.value.has(index - 1);

    collapsedSections.value.delete(index);
    collapsedSections.value.delete(index - 1);

    if (wasCurrentCollapsed) collapsedSections.value.add(index - 1);
    if (wasPrevCollapsed) collapsedSections.value.add(index);
  }
};

const moveSectionDown = (index) => {
  const sections = templateForm.value.config.sections;
  if (index < sections.length - 1) {
    [sections[index], sections[index + 1]] = [sections[index + 1], sections[index]];

    // 更新折叠状态
    const wasCurrentCollapsed = collapsedSections.value.has(index);
    const wasNextCollapsed = collapsedSections.value.has(index + 1);

    collapsedSections.value.delete(index);
    collapsedSections.value.delete(index + 1);

    if (wasCurrentCollapsed) collapsedSections.value.add(index + 1);
    if (wasNextCollapsed) collapsedSections.value.add(index);
  }
};

const getSectionSummary = (section) => {
  const parts = [];
  parts.push(`${section.speed}x速度`);
  parts.push(`${section.repeatCount}次重复`);
  if (section.enableTranslation) parts.push('含翻译');
  if (section.enableKeywords) parts.push('含关键词');
  return parts.join(' · ');
};

const getTypeClass = (type) => {
  return {
    'system': 'type-system',
    'user': 'type-user',
    'public': 'type-public'
  }[type] || 'type-user';
};

const getTypeText = (type) => {
  return {
    'system': '系统模板',
    'user': '用户模板',
    'public': '公开模板'
  }[type] || '用户模板';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN');
};

const validateForm = async () => {
  try {
    await formRef.value.validate();
    const validation = templateService.validateTemplateConfig(templateForm.value.config);
    if (!validation.valid) {
      ElMessage.error(`配置验证失败: ${validation.errors[0]}`);
      return false;
    }
    return true;
  } catch (error) {
    return false;
  }
};

const saveTemplate = async () => {
  const isValid = await validateForm();
  if (!isValid) return;

  saving.value = true;

  try {
    if (isEditing.value) {
      await templateStore.updateTemplate(props.template.id, templateForm.value);
    } else {
      await templateStore.createTemplate(templateForm.value);
    }

    emit('saved');
    drawerVisible.value = false;
  } catch (error) {
    console.error('保存模板失败:', error);
  } finally {
    saving.value = false;
  }
};

const duplicateTemplate = async () => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板的名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${props.template.name} - 副本`,
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '模板名称不能为空';
          }
          if (value.trim().length > 100) {
            return '模板名称不能超过100个字符';
          }
          return true;
        }
      }
    );

    await templateStore.duplicateTemplate(props.template.id, newName.trim());
    drawerVisible.value = false;
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制模板失败:', error);
    }
  }
};

const handleClose = () => {
  drawerVisible.value = false;
};

// Watch
watch(() => props.modelValue, (newValue) => {
  if (newValue && !props.readonly) {
    initForm();
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }
});

watch(() => props.template, () => {
  if (props.modelValue && !props.readonly) {
    initForm();
  }
});
</script>

<style scoped>
/* 抽屉样式 */
:deep(.template-viewer-drawer .el-drawer__header) {
  display: none;
}

:deep(.template-viewer-drawer .el-drawer__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.template-viewer {
  flex: 1;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 头部样式 */
.viewer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 10;
}

.viewer-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

/* 内容区域 */
.viewer-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* 类型标签样式 */
.type-system {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%) !important;
  color: white !important;
  border: none !important;
}

.type-user {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%) !important;
  color: white !important;
  border: none !important;
}

.type-public {
  background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%) !important;
  color: white !important;
  border: none !important;
}

/* 信息卡片样式 */
.info-card {
  margin: 1rem;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.info-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  font-size: 0.875rem;
  color: #495057;
  font-weight: 600;
}

/* 表单卡片样式 */
.form-card {
  margin: 1rem;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.template-form {
  margin: 0;
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.form-help {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

/* 环节配置样式 */
.sections-card {
  margin: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.sections-container {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  overflow-y: auto;
}

.sections-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-item {
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  overflow: hidden;
  transition: all 0.3s ease;
}

.section-item.collapsed {
  background: #f1f3f4;
}

.section-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.section-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.drag-handle {
  cursor: grab;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drag-handle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.drag-handle:active {
  cursor: grabbing;
}

.section-number {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.section-info {
  flex: 1;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  color: white;
}

.section-summary {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.section-actions .el-button {
  color: rgba(255, 255, 255, 0.9);
  border: none;
  background: transparent;
}

.section-actions .el-button:hover {
  color: white;
  background: rgba(255, 255, 255, 0.2);
}

.section-content {
  padding: 1rem;
  background: white;
  transition: all 0.3s ease;
}

/* 拖拽状态样式 */
.section-ghost {
  opacity: 0.5;
  background: #e3f2fd;
  border: 2px dashed #2196f3;
}

.section-chosen {
  transform: rotate(2deg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.section-drag {
  transform: rotate(5deg);
  opacity: 0.8;
}

/* 底部操作栏 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-header {
    padding: 1rem;
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: space-between;
  }

  .section-header {
    padding: 0.5rem 0.75rem;
    gap: 0.5rem;
  }

  .section-controls {
    gap: 0.25rem;
  }

  .section-number {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .section-title {
    font-size: 0.875rem;
  }

  .section-summary {
    font-size: 0.75rem;
  }

  .section-actions {
    gap: 0.125rem;
  }

  .section-actions .el-button {
    padding: 0.25rem;
    min-width: auto;
  }

  .section-content {
    padding: 0.75rem;
  }

  .drag-handle {
    padding: 0.125rem;
  }

  .drawer-footer {
    flex-direction: column;
    gap: 0.75rem;
  }
}
</style>
