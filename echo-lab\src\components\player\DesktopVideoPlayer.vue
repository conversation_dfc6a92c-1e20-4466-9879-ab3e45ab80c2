<script setup>
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue';
import { VideoPlay, VideoPause, Mute, Headset, ArrowLeft, ArrowRight, RefreshRight, VideoCamera } from '@element-plus/icons-vue';
import VideoPlayerBase from './VideoPlayerBase.vue';
import VideoExportDialog from './VideoExportDialog.vue';
import { useUserStore } from '@/stores/userStore';
import { checkFeaturePermission } from '@/services/featurePermissionService';

// 接收与基础播放器相同的属性
const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },
  audioBuffer: {
    type: Object,
    default: null
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  configJson: {
    type: Object,
    default: null
  },
  contentData: {
    type: Object,
    default: null
  },
  audioSynthesisProgress: {
    type: Object,
    default: () => ({
      total: 0,
      completed: 0,
      percentage: 0,
      isProcessing: false
    })
  }
});

// 转发相同的事件
const emit = defineEmits(['timeupdate', 'loading-change', 'open-settings', 'index-change', 'toggle-playlist']);

// 引用基础播放器
const playerBase = ref(null);

// 控制状态
const controlState = reactive({
  showVolumeSlider: false,
  isVolumeSliderActive: false,
  showSpeedMenu: false,
  showAutoShutdownPanel: false,
  volumeSliderTimeout: null,
  showTextStyleDialog: false,
  // 添加临时定时关闭状态
  tempAutoShutdown: {
    enabled: false,
    minutes: 15
  },
  // 视频导出对话框
  showVideoExportDialog: false
});

// 播放速度选项
const playbackSpeeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];

// 计算属性 - 从基础播放器获取状态
const uiState = computed(() => playerBase.value?.uiState || {});
const baseControlState = computed(() => playerBase.value?.controlState || { showControls: false });
const textStyle = computed(() => playerBase.value?.textStyle || {});
const fontSizeRange = computed(() => playerBase.value?.fontSizeRange || { min: 0.8, max: 2.0, step: 0.05 });
const colorOptions = computed(() => playerBase.value?.colorOptions || []);
// 使用ref而不是computed，这样可以直接修改值
const progressValue = ref(0);
// 不再需要封面图片相关计算属性，已移至VideoPlayerBase
const autoShutdownState = computed(() => playerBase.value?.autoShutdownState || { enabled: false, minutes: 15, remainingSeconds: 0 });

// 定时关闭开关的计算属性
const autoShutdownEnabled = computed({
  get: () => autoShutdownState.value.enabled,
  set: (value) => {
    if (value !== autoShutdownState.value.enabled) {
      playerBase.value?.toggleAutoShutdown?.();
    }
  }
});

// 是否可以跳转到上一个句子
const canJumpToPrevious = computed(() => playerBase.value?.canJumpToPreviousSentence || false);

// 是否可以跳转到下一个句子
const canJumpToNext = computed(() => playerBase.value?.canJumpToNextSentence || false);

// 内容元素点击处理已移至VideoPlayerBase

// 边界反馈状态
const boundaryFeedback = reactive({
  prev: false,
  next: false
});

// 处理边界反馈
const handleBoundaryReached = (boundary) => {
  if (boundary === 'start') {
    // 触发前一个按钮的反馈动画
    boundaryFeedback.prev = true;
    setTimeout(() => {
      boundaryFeedback.prev = false;
    }, 300); // 动画持续时间
  } else if (boundary === 'end') {
    // 触发后一个按钮的反馈动画
    boundaryFeedback.next = true;
    setTimeout(() => {
      boundaryFeedback.next = false;
    }, 300); // 动画持续时间
  }
};

// 根据进度百分比获取进度文本
const getProgressText = (percentage) => {
  if (percentage < 90) {
    return `正在处理: ${percentage}%`;
  } else if (percentage < 95) {
    return '正在合成: 90%';
  } else if (percentage < 100) {
    return '即将完成: 95%';
  } else {
    return '处理完成: 100%';
  }
};

// 显示音量滑块
const showVolumeControl = () => {
  // 清除之前的定时器
  if (controlState.volumeSliderTimeout) {
    clearTimeout(controlState.volumeSliderTimeout);
    controlState.volumeSliderTimeout = null;
  }

  // 显示音量滑块
  controlState.showVolumeSlider = true;

  // 设置自动隐藏定时器
  startVolumeSliderHideTimer();
};

// 隐藏音量滑块
const hideVolumeControl = () => {
  // 如果音量滑块不处于活动状态，设置定时器隐藏
  if (!controlState.isVolumeSliderActive) {
    startVolumeSliderHideTimer();
  }
};

// 开始音量滑块隐藏定时器
const startVolumeSliderHideTimer = () => {
  // 清除之前的定时器
  if (controlState.volumeSliderTimeout) {
    clearTimeout(controlState.volumeSliderTimeout);
  }

  // 设置新的定时器，1.5秒后隐藏
  controlState.volumeSliderTimeout = setTimeout(() => {
    if (!controlState.isVolumeSliderActive) {
      controlState.showVolumeSlider = false;
    }
  }, 1500);
};

// 切换静音时的处理
const handleMuteClick = (event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 切换静音状态
  playerBase.value?.toggleMute?.();
};

// 从点击位置设置音量
const setVolumeFromClick = (event) => {
  const trackElement = event.currentTarget;
  const rect = trackElement.getBoundingClientRect();
  const clickY = event.clientY - rect.top;
  const trackHeight = rect.height;

  // 计算音量（从底部向上）
  let newVolume = 1 - (clickY / trackHeight);
  newVolume = Math.max(0, Math.min(1, newVolume));

  // 设置音量
  playerBase.value?.setVolume?.(newVolume);
};

// 开始音量滑动
const startVolumeSlide = (event) => {
  // 设置音量滑块为活动状态
  controlState.isVolumeSliderActive = true;

  // 立即设置音量
  setVolumeFromClick(event);

  // 添加全局鼠标移动和松开事件监听
  document.addEventListener('mousemove', handleGlobalMouseMove);
  document.addEventListener('mouseup', handleGlobalMouseUp);
};

// 全局鼠标移动处理
const handleGlobalMouseMove = (event) => {
  if (controlState.isVolumeSliderActive) {
    // 找到音量滑块元素
    const volumeTrack = document.querySelector('.volume-track');
    if (volumeTrack) {
      const rect = volumeTrack.getBoundingClientRect();
      const clickY = event.clientY - rect.top;
      const trackHeight = rect.height;

      // 计算音量（从底部向上）
      let newVolume = 1 - (clickY / trackHeight);
      newVolume = Math.max(0, Math.min(1, newVolume));

      // 设置音量
      playerBase.value?.setVolume?.(newVolume);
    }
  }
};

// 全局鼠标松开处理
const handleGlobalMouseUp = () => {
  // 移除全局事件监听
  document.removeEventListener('mousemove', handleGlobalMouseMove);
  document.removeEventListener('mouseup', handleGlobalMouseUp);

  // 设置音量滑块为非活动状态
  controlState.isVolumeSliderActive = false;

  // 开始隐藏定时器
  startVolumeSliderHideTimer();
};

// 切换速度菜单显示/隐藏
const toggleSpeedMenu = (event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 切换速度菜单显示状态
  controlState.showSpeedMenu = !controlState.showSpeedMenu;

  // 如果显示速度菜单，添加点击外部关闭的事件监听
  if (controlState.showSpeedMenu) {
    setTimeout(() => {
      document.addEventListener('click', handleOutsideSpeedMenuClick);
    }, 0);
  }
};

// 处理点击外部关闭速度菜单
const handleOutsideSpeedMenuClick = (event) => {
  // 如果点击的不是速度控制区域，则关闭速度菜单
  if (!event.target.closest('.playback-speed')) {
    controlState.showSpeedMenu = false;
    document.removeEventListener('click', handleOutsideSpeedMenuClick);
  }
};

// 打开文本样式设置
const openTextStyleSettings = () => {
  controlState.showTextStyleDialog = true;
};

// 处理文本样式变更
const handleTextStyleChange = (newStyle) => {
  playerBase.value?.handleTextStyleChange?.(newStyle);
};

// 切换定时关闭状态
const toggleAutoShutdown = () => {
  // 直接调用基础播放器的toggleAutoShutdown方法
  playerBase.value?.toggleAutoShutdown?.();
};

// 设置定时关闭时间
const setAutoShutdownTime = (minutes) => {
  // 更新临时状态
  controlState.tempAutoShutdown.minutes = minutes;
  // 调用基础播放器的方法设置时间
  playerBase.value?.setAutoShutdownTime?.(minutes);
};

// 打开定时关闭面板时初始化临时状态
const toggleAutoShutdownPanel = (event) => {
  // 阻止事件冒泡
  event.stopPropagation();

  // 初始化临时状态
  controlState.tempAutoShutdown = {
    enabled: autoShutdownState.value.enabled,
    minutes: autoShutdownState.value.minutes
  };

  // 切换定时关闭面板显示状态
  controlState.showAutoShutdownPanel = !controlState.showAutoShutdownPanel;
};

// 格式化剩余时间
const formatRemainingTime = () => {
  return playerBase.value?.formatRemainingTime?.() || '';
};

// 处理进度条输入
const onProgressInput = (value) => {
  // 直接更新进度条值
  progressValue.value = value;

  // 调用基础播放器的处理函数
  playerBase.value?.handleProgressChange?.(value);
};

// 处理进度条拖动结束
const onProgressDragEnd = () => {
  // 调用基础播放器的处理函数
  playerBase.value?.endProgressDrag?.();

  // 确保进度条值与当前时间同步
  if (playerBase.value?.uiState?.currentTime && playerBase.value?.uiState?.duration) {
    const newValue = (playerBase.value.uiState.currentTime / playerBase.value.uiState.duration) * 100;
    progressValue.value = newValue;
  }
};

// 生命周期钩子
onMounted(() => {
  // 添加全局事件监听
  document.addEventListener('click', handleOutsideSpeedMenuClick);

  // 添加一个定时器，确保基础播放器已经初始化
  setTimeout(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      progressValue.value = playerBase.value.progressValue;
    }
  }, 100);

  // 添加一个定时器，定期从基础播放器同步进度值
  const syncInterval = setInterval(() => {
    if (playerBase.value && playerBase.value.progressValue !== undefined) {
      // 只有当用户没有交互时才同步进度值
      if (!playerBase.value.controlState?.isUserInteracting) {
        progressValue.value = playerBase.value.progressValue;
      }
    }
  }, 200); // 每200毫秒同步一次

  // 在组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(syncInterval);
  });
});

onUnmounted(() => {
  // 移除全局事件监听
  document.removeEventListener('click', handleOutsideSpeedMenuClick);

  // 清除定时器
  if (controlState.volumeSliderTimeout) {
    clearTimeout(controlState.volumeSliderTimeout);
  }
});

// 暴露方法给父组件
defineExpose({
  // 提供一个获取基础播放器方法的代理
  togglePlay: () => playerBase.value?.togglePlay?.(),
  jumpToIndex: (index) => playerBase.value?.jumpToIndex?.(index),
  jumpToCurrentSentenceStart: () => playerBase.value?.jumpToCurrentSentenceStart?.(),
  jumpToTime: (time) => playerBase.value?.jumpToTime?.(time),
  setVolume: (value) => playerBase.value?.setVolume?.(value),
  setPlaybackRate: (rate) => playerBase.value?.setPlaybackRate?.(rate),
  // toggleFullscreen 已移除
  toggleMute: () => playerBase.value?.toggleMute?.(),
  handleProgressChange: (value) => playerBase.value?.handleProgressChange?.(value),
  formatTime: (time) => playerBase.value?.formatTime?.(time) || '00:00',
  // 定时关闭相关方法
  toggleAutoShutdown: () => playerBase.value?.toggleAutoShutdown?.(),
  setAutoShutdownTime: (minutes) => playerBase.value?.setAutoShutdownTime?.(minutes),
  formatRemainingTime: () => playerBase.value?.formatRemainingTime?.() || '',
  // 提供访问基础播放器状态的代理
  get uiState() { return playerBase.value?.uiState || {} },
  get controlState() { return playerBase.value?.controlState || {} },
  get textStyle() { return playerBase.value?.textStyle || {} },
  get progressValue() { return playerBase.value?.progressValue || 0 },
  get autoShutdownState() { return playerBase.value?.autoShutdownState || { enabled: false, minutes: 15, remainingSeconds: 0 } },
});

const loop = ref(false);
const toggleLoop = () => {
  loop.value = !loop.value;
};

// 获取用户状态
const userStore = useUserStore();

// 检查用户是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 检查用户是否有视频导出权限
const hasExportPermission = ref(false);

// 在组件挂载时检查权限
onMounted(async () => {
  if (isLoggedIn.value) {
    try {
      hasExportPermission.value = await checkFeaturePermission('video_export');
    } catch (error) {
      console.error('检查视频导出权限失败:', error);
      hasExportPermission.value = false;
    }
  }
});

// 打开视频导出对话框
const openVideoExportDialog = () => {
  console.log('打开视频导出对话框', {
    audioBuffer: !!props.audioBuffer,
    timeline: props.timeline?.length || 0,
    isLoggedIn: isLoggedIn.value,
    hasExportPermission: hasExportPermission.value
  });

  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再导出视频');
    return;
  }

  // 检查用户是否有视频导出权限
  if (!hasExportPermission.value) {
    ElMessage.warning('您没有视频导出权限，请升级会员等级');
    // 跳转到升级页面
    window.location.href = '/upgrade?redirect=' + encodeURIComponent(window.location.pathname + window.location.search);
    return;
  }

  if (!props.audioBuffer) {
    ElMessage.warning('音频数据不可用，无法导出视频');
    return;
  }

  if (!props.timeline || props.timeline.length === 0) {
    ElMessage.warning('没有可导出的内容');
    return;
  }

  // 打开导出对话框
  controlState.showVideoExportDialog = true;
};

// 视频导出完成处理
const handleVideoExportComplete = (_result) => {
  ElMessage.success('视频导出成功');
  controlState.showVideoExportDialog = false;
};
</script>

<template>
  <div class="desktop-video-player" @mouseleave="playerBase?.hideControlsImmediately?.()"
    @mousemove="playerBase?.showControlsElements?.()">
    <!-- 基础播放器 - 现在作为主要内容显示 -->
    <div class="player-base-container">
      <VideoPlayerBase ref="playerBase" v-bind="props" :loop="loop" @timeupdate="$emit('timeupdate', $event)"
        @loading-change="$emit('loading-change', $event)" @index-change="$emit('index-change', $event)"
        @toggle-playlist="$emit('toggle-playlist')" @boundary-reached="handleBoundaryReached" />
    </div>

    <!-- 桌面端特有UI - 只包含控制元素，不再包含内容显示 -->
    <div class="player-controls" :style="{ backgroundColor: 'transparent' }"
      :class="{ 'show-controls': playerBase?.controlState.showControls }">
      <!-- 加载提示 -->
      <div class="loading-overlay" v-if="props.isLoading">
        <div class="loading-spinner"></div>
        <div class="loading-text">视频生成中...</div>
        <div class="loading-subtext">
          {{ getProgressText(props.audioSynthesisProgress.percentage) }}
        </div>
        <!-- 进度条 -->
        <div class="synthesis-progress-bar">
          <div class="synthesis-progress-fill" :style="{ width: props.audioSynthesisProgress.percentage + '%' }">
          </div>
        </div>
      </div>

      <!-- 内容显示已移至VideoPlayerBase.vue中处理 -->

      <!-- 版权信息已移至ContentDisplay组件中处理 -->

      <!-- 暂停蒙层已不再需要，点击事件由VideoPlayerBase处理 -->

      <!-- 桌面端控制按钮组 - 只在控制区域显示时显示 -->
      <div v-if="baseControlState.showControls" class="desktop-controls-group">
        <div class="controls-buttons-container">
          <!-- 后退按钮 -->
          <div class="desktop-nav-button" @click.stop="playerBase?.jumpToPreviousSentence?.()"
            :class="{ 'disabled': !canJumpToPrevious, 'boundary-feedback': boundaryFeedback.prev }">
            <el-icon>
              <ArrowLeft />
            </el-icon>
          </div>

          <!-- 播放/暂停按钮 -->
          <div class="desktop-play-button" @click.stop="playerBase?.togglePlay?.()">
            <el-icon v-if="!uiState.isPlaying">
              <VideoPlay />
            </el-icon>
            <el-icon v-else>
              <VideoPause />
            </el-icon>
          </div>

          <!-- 前进按钮 -->
          <div class="desktop-nav-button" @click.stop="playerBase?.jumpToNextSentence?.()"
            :class="{ 'disabled': !canJumpToNext, 'boundary-feedback': boundaryFeedback.next }">
            <el-icon>
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 视频内控制元素 -->
      <div class="video-controls" v-show="!uiState.isPlaying || (uiState.isPlaying && baseControlState.showControls)">
        <!-- 进度条 -->
        <div class="progress-slider-container">
          <el-slider v-model="progressValue" :min="0" :max="100"
            :format-tooltip="value => playerBase?.formatTime?.((value / 100) * uiState.duration) || '00:00'"
            @mousedown="playerBase?.startProgressDrag?.()" @mouseup="onProgressDragEnd"
            @touchstart="playerBase?.startProgressDrag?.()" @touchend="onProgressDragEnd" @input="onProgressInput"
            class="custom-slider" />
        </div>

        <div class="controls-bottom">
          <div class="controls-left">
            <!-- 播放/暂停按钮 -->
            <el-tooltip content="播放/暂停" placement="top" effect="dark" :enterable="false">
              <div class="play-pause-control" @click="playerBase?.togglePlay?.()">
                <el-icon v-if="!uiState.isPlaying">
                  <VideoPlay />
                </el-icon>
                <el-icon v-else>
                  <VideoPause />
                </el-icon>
              </div>
            </el-tooltip>

            <!-- 音量控制 -->
            <el-tooltip content="音量控制" placement="top" effect="dark" :enterable="false">
              <div class="volume-control" @mouseenter="showVolumeControl" @mouseleave="hideVolumeControl">
                <el-icon @click="handleMuteClick" class="volume-icon">
                  <Mute v-if="uiState.isMuted" />
                  <Headset v-else />
                </el-icon>
                <div class="volume-slider" v-show="controlState.showVolumeSlider">
                  <div class="volume-track" @click="setVolumeFromClick" @mousedown="startVolumeSlide">
                    <div class="volume-fill" :style="{ height: `${uiState.volume * 100}%` }"></div>
                    <div class="volume-handle" :style="{ bottom: `${uiState.volume * 100}%` }"></div>
                  </div>
                </div>
              </div>
            </el-tooltip>

            <!-- 时间显示 -->
            <div class="time-display">
              <span>{{ playerBase?.formatTime?.(uiState.currentTime) || '00:00' }}</span>
              <span>/</span>
              <span>{{ playerBase?.formatTime?.(uiState.duration) || '00:00' }}</span>
            </div>
          </div>

          <div class="controls-right">
            <!-- 播放速度 -->
            <el-tooltip content="播放速度" placement="top" effect="dark" :enterable="false">
              <div class="playback-speed">
                <div class="custom-speed-selector" @click="toggleSpeedMenu">
                  <span class="speed-display">{{ uiState.playbackRate }}x</span>
                </div>
                <!-- 自定义速度菜单 -->
                <div class="speed-menu" v-if="controlState.showSpeedMenu">
                  <div v-for="speed in playbackSpeeds" :key="speed" class="speed-menu-item"
                    :class="{ 'active-speed': speed === uiState.playbackRate }"
                    @click="playerBase?.setPlaybackRate?.(speed)">
                    {{ speed }}x
                  </div>
                </div>
              </div>
            </el-tooltip>

            <!-- 循环播放按钮 -->
            <el-tooltip content="循环播放" placement="top" effect="dark" :enterable="false">
              <div class="loop-control" :class="{ active: loop }" @click="toggleLoop">
                <el-icon>
                  <RefreshRight />
                </el-icon>
              </div>
            </el-tooltip>

            <!-- 播放列表开关 -->
            <el-tooltip content="显示/隐藏内容列表" placement="top" effect="dark" :enterable="false">
              <div class="playlist-switch">
                <el-switch v-model="uiState.showPlaylist" @change="playerBase?.togglePlaylist?.()" active-text=""
                  inactive-text="" size="small" class="custom-switch" />
              </div>
            </el-tooltip>

            <!-- 收藏按钮插槽 -->
            <slot name="favorite-button"></slot>

            <!-- 文本样式设置按钮 -->
            <el-tooltip content="文本样式设置" placement="top" effect="dark" :enterable="false">
              <div class="settings-control" @click="openTextStyleSettings">
                <el-icon>
                  <svg viewBox="0 0 1024 1024" width="16" height="16">
                    <path fill="currentColor"
                      d="M920 416H616c-4.4 0-8 3.6-8 8v112c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-56h60v320h-46c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h164c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8h-46V480h60v56c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V424c0-4.4-3.6-8-8-8zM656 296V168c0-4.4-3.6-8-8-8H104c-4.4 0-8 3.6-8 8v128c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-64h168v560h-92c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-92V232h168v64c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8z">
                    </path>
                  </svg>
                </el-icon>
              </div>
            </el-tooltip>

            <!-- 精听定制器按钮 -->
            <el-tooltip content="播放策略设置" placement="top" effect="dark" :enterable="false">
              <div class="settings-control" @click="$emit('open-settings')">
                <el-icon>
                  <svg viewBox="0 0 1024 1024" width="16" height="16">
                    <path fill="currentColor"
                      d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM368 744c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v464zm192-280c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v184zm192 72c0 4.4-3.6 8-8 8h-80c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h80c4.4 0 8 3.6 8 8v256z">
                    </path>
                  </svg>
                </el-icon>
              </div>
            </el-tooltip>

            <!-- 定时关闭按钮 -->
            <el-tooltip content="定时关闭" placement="top" effect="dark" :enterable="false">
              <div class="auto-shutdown">
                <div class="auto-shutdown-control" @click="toggleAutoShutdownPanel">
                  <el-icon>
                    <svg viewBox="0 0 1024 1024" width="16" height="16">
                      <path fill="currentColor"
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
                      </path>
                      <path fill="currentColor"
                        d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z">
                      </path>
                    </svg>
                  </el-icon>
                </div>
              </div>
            </el-tooltip>

            <!-- 导出视频按钮 - 只在音频准备好且用户有权限时显示 -->
            <el-tooltip v-if="props.audioBuffer && isLoggedIn && hasExportPermission" content="导出视频" placement="top"
              effect="dark" :enterable="false">
              <div class="download-control" @click="openVideoExportDialog">
                <el-icon>
                  <VideoCamera />
                </el-icon>
              </div>
            </el-tooltip>

            <!-- 下载音频按钮 - 只在音频准备好时显示 -->
            <el-tooltip v-if="props.audioBuffer" content="下载音频" placement="top" effect="dark" :enterable="false">
              <div class="download-control" @click="playerBase?.exportAudio?.()">
                <el-icon>
                  <svg viewBox="0 0 1024 1024" width="16" height="16">
                    <path fill="currentColor"
                      d="M624.8 473.6v-128c0-8.8-7.2-16-16-16H416c-8.8 0-16 7.2-16 16v128H232c-4.8 0-9.2 2.4-12 6.4-2.8 4-3.2 9.2-1.2 13.6l280 448c2.4 4 6.8 6.4 11.2 6.4s8.8-2.4 11.2-6.4l280-448c2-4.4 1.6-9.6-1.2-13.6-2.8-4-7.2-6.4-12-6.4H624.8z">
                    </path>
                  </svg>
                </el-icon>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 定时关闭右下角悬浮提示 -->
      <div v-if="autoShutdownState.enabled" class="auto-shutdown-indicator">
        <el-icon style="margin-right: 0.25rem;"><svg viewBox="0 0 1024 1024" width="16" height="16">
            <path fill="currentColor"
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z">
            </path>
            <path fill="currentColor"
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z">
            </path>
          </svg></el-icon>
        {{ formatRemainingTime() }}
        <span class="close-btn" @click="toggleAutoShutdown">×</span>
      </div>
    </div>

    <!-- 添加文本样式设置对话框 -->
    <el-dialog v-model="controlState.showTextStyleDialog" title="文本样式设置" width="400px" :close-on-click-modal="true"
      :show-close="true">
      <div class="text-style-dialog-content">
        <!-- 字体大小设置 -->
        <div class="style-section">
          <div class="section-title">字体大小</div>
          <div class="font-size-slider">
            <el-slider v-model="textStyle.fontSizePercent" :min="fontSizeRange.min" :max="fontSizeRange.max"
              :step="fontSizeRange.step" :format-tooltip="value => `${value}%`"
              @change="handleTextStyleChange({ fontSizePercent: textStyle.fontSizePercent })" class="custom-slider" />
          </div>
        </div>

        <!-- 字体颜色设置 -->
        <div class="style-section">
          <div class="section-title">字体颜色</div>
          <div class="color-options">
            <div v-for="color in colorOptions" :key="color.value" class="color-option"
              :style="{ backgroundColor: color.value }" :class="{ 'active-color': textStyle.color === color.value }"
              @click="handleTextStyleChange({ color: color.value })" :title="color.label">
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加定时关闭设置对话框 -->
    <el-dialog v-model="controlState.showAutoShutdownPanel" title="定时关闭设置" width="400px" :close-on-click-modal="true"
      :show-close="true">
      <div class="text-style-dialog-content">
        <!-- 定时关闭开关 -->
        <div class="style-section">
          <div class="section-title">启用定时关闭</div>
          <div class="shutdown-switch">
            <el-switch v-model="autoShutdownEnabled" />
          </div>
        </div>

        <!-- 定时时间设置 -->
        <div class="style-section">
          <div class="section-title">定时时间</div>
          <div class="font-size-slider">
            <el-slider v-model="controlState.tempAutoShutdown.minutes" :min="5" :max="120" :step="5"
              :format-tooltip="value => `${value}分钟`" @change="setAutoShutdownTime" class="custom-slider" />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 视频导出对话框 -->
    <VideoExportDialog v-model="controlState.showVideoExportDialog" :timeline="props.timeline"
      :audioBuffer="props.audioBuffer" :videoConfig="{
        backgroundColor: textStyle.backgroundColor || '#000000',
        textColor: textStyle.color || '#FFFFFF',
        fontSize: textStyle.fontSize || 1.25,
        copyright: props.videoConfig.copyright
      }" :configJson="props.configJson" :contentName="props.contentData?.name"
      @export-complete="handleVideoExportComplete" />
  </div>
</template>

<style scoped>
.desktop-video-player {
  width: 100%;
  aspect-ratio: 16/9;
  display: flex;
  flex-direction: column;
  background-color: #000;
  color: #fff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.3);
  position: relative;
}

.player-base-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.player-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  pointer-events: auto;
  /* 允许鼠标事件 */
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-controls.show-controls {
  opacity: 1;
}

/* 内容显示相关样式已移至VideoPlayerBase.vue和ContentDisplay.vue */

/* 加载提示样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

/* 封面图片样式 */
.cover-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 统一的播放按钮样式 */
.unified-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4.25rem;
  height: 4.25rem;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.8);
  color: #fff;
  font-size: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 4;
  border: 0.125rem solid white;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.5);
}

.unified-play-button:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: translate(-50%, -50%) scale(1.05);
}

.unified-play-button:active {
  transform: translate(-50%, -50%) scale(0.95);
}

/* 隐藏播放按钮 */
.unified-play-button.hidden {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
}

/* 桌面端控制按钮组 */
.desktop-controls-group {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 5;
}

.controls-buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.5rem;
  transform: translateY(-50%);
}

/* 桌面端导航按钮 */
.desktop-nav-button {
  width: 2.8rem;
  height: 2.8rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.0625rem solid rgba(255, 255, 255, 0.5);
  font-size: 1.25rem;
}

.desktop-nav-button:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.desktop-nav-button:active {
  transform: scale(0.95);
}

.desktop-nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.desktop-nav-button.disabled:hover {
  transform: none;
  background-color: rgba(0, 0, 0, 0.6);
}

.download-control.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.download-control.disabled:hover {
  transform: none;
  background-color: rgba(0, 0, 0, 0.6);
}

/* 边界反馈动画 */
@keyframes boundary-shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-3px);
  }

  75% {
    transform: translateX(3px);
  }
}

.desktop-nav-button.boundary-feedback {
  animation: boundary-shake 0.3s ease-in-out;
}

/* 桌面端播放按钮 */
.desktop-play-button {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.8);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 0.125rem solid rgba(255, 255, 255, 0.8);
  font-size: 1.6rem;
}

.desktop-play-button:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(1.05);
}

.desktop-play-button:active {
  transform: scale(0.95);
}

/* 版权信息样式已移至ContentDisplay.vue */

/* 暂停蒙层样式已移除 */

.loading-spinner {
  width: 3.125rem;
  height: 3.125rem;
  border: 0.3125rem solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 1rem;
  color: #fff;
  font-size: 1.125rem;
}

.loading-subtext {
  margin-top: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: 500;
}

/* 视频合成进度条 */
.synthesis-progress-bar {
  width: 80%;
  height: 0.625rem;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.3125rem;
  margin-top: 1.25rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

.synthesis-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff3333, #ff0000);
  transition: width 0.3s ease;
  border-radius: 0.3125rem;
}

/* 视频内控制元素样式 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
  box-sizing: border-box;
  opacity: 1;
  transition: opacity 0.3s;
  z-index: 5;
}

/* 未播放状态下控制区域背景更明显 */
.desktop-video-player:not(.playing) .video-controls {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
}

/* 进度条容器 */
.progress-slider-container {
  width: 100%;
  position: relative;
  z-index: 10;
  padding: 0 0.5rem;
  box-sizing: border-box;
}

/* 自定义滑动条样式 */
.custom-slider {
  --el-slider-height: 0.5rem;
  --el-slider-button-size: 1rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: rgba(255, 255, 255, 0.2);
  height: 0.5rem;
  margin: 0;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #ff0000;
  height: 0.5rem;
}

.custom-slider :deep(.el-slider__button) {
  border: 0.125rem solid #ffffff;
  background-color: #ff0000;
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s;
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

.custom-slider :deep(.el-slider__button):hover {
  transform: scale(1.2);
}

/* 字体大小设置样式 */
.font-size-slider {
  padding: 0 0.5rem;
  margin-top: 0.5rem;
}

.section-title {
  font-size: 0.875rem;
  color: #606266;
  margin-bottom: 0.5rem;
}

.controls-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 0.5rem;
}

.play-pause-control {
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #FFFFFF;
  font-size: 1.25rem;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: rgba(255, 0, 0, 0.8);
}

.play-pause-control:hover {
  background-color: rgba(255, 0, 0, 1);
  transform: scale(1.05);
}

.navigation-control {
  width: 1.75rem;
  height: 1.75rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #FFFFFF;
  font-size: 1rem;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: rgba(0, 0, 0, 0.5);
}

.navigation-control:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.05);
}

.navigation-control.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.navigation-control.disabled:hover {
  transform: none;
  background-color: rgba(0, 0, 0, 0.5);
}

.volume-control {
  position: relative;
  margin-right: 0.75rem;
}

.volume-icon {
  cursor: pointer;
  color: #fff;
  font-size: 1.25rem;
}

.volume-slider {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0.5rem;
  height: 5rem;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 0.25rem;
  padding: 0.5rem 0;
  display: flex;
  justify-content: center;
}

.volume-track {
  width: 0.25rem;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 0.125rem;
  position: relative;
  cursor: pointer;
}

.volume-fill {
  width: 100%;
  background-color: #ffffff;
  border-radius: 0.125rem;
  position: absolute;
  bottom: 0;
  left: 0;
}

.volume-handle {
  position: absolute;
  left: 50%;
  width: 0.75rem;
  height: 0.75rem;
  background-color: #fff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
  box-shadow: 0 0 0.25rem rgba(0, 0, 0, 0.5);
}

.time-display {
  color: #fff;
  font-size: 0.875rem;
  margin-right: 0.75rem;
}

.time-display span {
  margin: 0 0.125rem;
}

.playback-speed {
  margin-left: 0.5rem;
  position: relative;
}

.custom-speed-selector {
  cursor: pointer;
}

.speed-display {
  color: #fff;
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  background-color: #000000;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: inline-block;
}

.speed-display:hover {
  background-color: #222222;
}

/* 自定义速度菜单 */
.speed-menu {
  position: absolute;
  bottom: 2rem;
  right: 0;
  background-color: #000000;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.25rem;
  padding: 0.25rem 0;
  z-index: 10;
  min-width: 5rem;
}

.speed-menu-item {
  color: #fff;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s;
}

.speed-menu-item:hover {
  background-color: #222222;
}

.speed-menu-item.active-speed {
  background-color: #333333;
  font-weight: bold;
}

.settings-control,
.playlist-switch,
.download-control,
.auto-shutdown-control {
  cursor: pointer;
  color: #fff;
  font-size: 1.25rem;
  margin-left: 0.5rem;
  transition: transform 0.2s;
  padding: 0.25rem;
  position: relative;
}

.settings-control:hover,
.playlist-switch:hover,
.download-control:hover,
.auto-shutdown-control:hover {
  transform: scale(1.05);
}

/* 定时关闭相关样式 */
.auto-shutdown {
  position: relative;
}

.auto-shutdown-control {
  display: flex;
  align-items: center;
}

.remaining-time {
  font-size: 0.75rem;
  margin-left: 0.25rem;
  color: #fff;
  background-color: rgba(255, 0, 0, 0.8);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
}

.shutdown-switch {
  display: flex;
  justify-content: flex-start;
  margin-top: 0.5rem;
}

/* 移除不需要的样式 */
.time-options,
.time-option,
.dialog-footer {
  display: none;
}

/* 自定义Switch开关样式 */
.playlist-switch {
  display: flex;
  align-items: center;
}

.custom-switch {
  --el-switch-on-color: #ff0000;
}

:deep(.el-switch__label) {
  color: #fff !important;
  font-size: 0.75rem;
}

:deep(.el-switch.is-checked .el-switch__core) {
  border-color: #ff0000 !important;
  background-color: #ff0000 !important;
}

/* 文本样式设置对话框样式 */
.text-style-dialog-content {
  padding: 1rem;
}

.style-section {
  margin-bottom: 1.5rem;
}

.color-options {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.color-option {
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active-color {
  border-color: #409eff;
  transform: scale(1.1);
}

.dialog-footer {
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.dialog-footer .el-button {
  min-width: 5rem;
}

.auto-shutdown-indicator {
  position: absolute;
  right: 1.5rem;
  bottom: 5rem;
  /* 往上移，避免挡住控制栏 */
  background: #ff0000;
  color: #fff;
  padding: 0.35rem 1rem;
  border-radius: 1.25rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  z-index: 20;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.18);
  cursor: default;
}

.close-btn {
  margin-left: 0.75rem;
  cursor: pointer;
  font-weight: bold;
  font-size: 1.1rem;
  line-height: 1;
}

.close-btn:hover {
  color: #fff;
  opacity: 0.8;
}

.loop-control {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  color: #bbb;
  cursor: pointer;
  margin-left: 0.5rem;
  transition: background 0.2s, color 0.2s;
}

.loop-control.active {
  background: #ff0000;
  color: #fff;
}

.loop-control:hover {
  background: #409eff99;
  color: #fff;
}
</style>
