-- 创建数据库
CREATE DATABASE IF NOT EXISTS `echo-lab` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `echo-lab`;

-- 创建音频表
CREATE TABLE IF NOT EXISTS `audios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `text` text NOT NULL,
  `language` varchar(10) NOT NULL,
  `speaker` varchar(50) DEFAULT NULL,
  `speed` float DEFAULT 1.0,
  `url` varchar(255) NOT NULL,
  `duration` float DEFAULT NULL,
  `md5` varchar(32) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_md5` (`md5`),
  KEY `idx_language` (`language`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建素材内容表
CREATE TABLE IF NOT EXISTS `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `config_json` json NOT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `user_id` varchar(50) DEFAULT NULL,
  `tags` varchar(255) DEFAULT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'draft',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建用户表（如果需要）
-- CREATE TABLE IF NOT EXISTS `users` (
--   `id` int(11) NOT NULL AUTO_INCREMENT,
--   `username` varchar(50) NOT NULL,
--   `password` varchar(255) NOT NULL,
--   `email` varchar(100) DEFAULT NULL,
--   `role` varchar(20) NOT NULL DEFAULT 'user',
--   `created_at` datetime NOT NULL,
--   `updated_at` datetime NOT NULL,
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `idx_username` (`username`),
--   UNIQUE KEY `idx_email` (`email`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
