/**
 * 基于WebCodecs API的前端视频合成工具
 * 使用浏览器原生编码能力，性能更好，文件更小
 * 专为Chrome浏览器优化，完全替代后端处理
 */

// 静态导入MP4Muxer
import { Muxer, ArrayBufferTarget } from "mp4-muxer";

/**
 * WebCodecs视频导出器类
 */
export class WebCodecsVideoExporter {
  constructor() {
    this.isSupported = this._checkSupport();
  }

  /**
   * 检查浏览器支持
   */
  _checkSupport() {
    return (
      "VideoEncoder" in window &&
      "VideoFrame" in window &&
      "AudioEncoder" in window &&
      "AudioData" in window
    );
  }

  /**
   * 从时间线数据生成视频
   * @param {Array} timeline - 时间线数据
   * @param {AudioBuffer} audioBuffer - 音频缓冲区
   * @param {Function} renderContent - 内容渲染函数
   * @param {Object} options - 导出选项
   */
  async exportVideo(timeline, audioBuffer, renderContent, options = {}) {
    if (!this.isSupported) {
      throw new Error("当前浏览器不支持WebCodecs API，请使用Chrome 94+");
    }

    const {
      width = 1280,
      height = 720,
      frameRate = 30,
      quality = "medium",
      fileName = "video",
      generateSubtitles = false,
      subtitleLanguages = [],
      configJson = null,
      onProgress = null,
      signal = null,
    } = options;

    // 质量预设
    const qualityPresets = {
      low: { bitrate: 1000000 }, // 1Mbps
      medium: { bitrate: 2500000 }, // 2.5Mbps
      high: { bitrate: 5000000 }, // 5Mbps
    };

    const bitrate =
      qualityPresets[quality]?.bitrate || qualityPresets.medium.bitrate;

    try {
      // 阶段1: 生成帧
      if (onProgress) onProgress({ phase: "generating-frames", progress: 0 });

      const frames = await this._generateFrames(timeline, renderContent, {
        width,
        height,
        signal,
        onProgress: (progress, current, total) => {
          if (onProgress) {
            onProgress({
              phase: "generating-frames",
              progress: progress * 30, // 帧生成占30%，progress已经是0-1的值
              current: current,
              total: total,
            });
          }
        },
      });

      // 阶段2: 初始化编码器
      if (onProgress)
        onProgress({ phase: "initializing-encoder", progress: 30 });

      const { videoEncoder, audioEncoder, muxer, target, audioConfig } =
        await this._initializeEncoders({
          width,
          height,
          frameRate,
          bitrate,
          audioBuffer,
        });

      // 阶段3: 编码视频帧
      if (onProgress) onProgress({ phase: "encoding-video", progress: 35 });

      await this._encodeVideoFrames(videoEncoder, frames, frameRate, {
        signal,
        onProgress: (progress, current, total) => {
          if (onProgress) {
            onProgress({
              phase: "encoding-video",
              progress: 35 + progress * 40, // 视频编码占40%，progress已经是0-1的值
              current: current,
              total: total,
            });
          }
        },
      });

      // 阶段4: 编码音频
      if (onProgress) onProgress({ phase: "encoding-audio", progress: 75 });

      // 检查是否需要重采样
      let processedAudioBuffer = audioBuffer;
      const encoderSampleRate = audioConfig.sampleRate;

      if (audioBuffer.sampleRate !== encoderSampleRate) {
        console.log(
          `重采样音频: ${audioBuffer.sampleRate}Hz -> ${encoderSampleRate}Hz`
        );
        processedAudioBuffer = await this._resampleAudio(
          audioBuffer,
          encoderSampleRate
        );
      }

      await this._encodeAudio(audioEncoder, processedAudioBuffer, {
        signal,
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              phase: "encoding-audio",
              progress: 75 + progress * 15, // 音频编码占15%，progress已经是0-1的值
              frameProgress: progress,
            });
          }
        },
      });

      // 阶段5: 完成编码
      if (onProgress) onProgress({ phase: "finalizing", progress: 90 });

      await videoEncoder.flush();
      await audioEncoder.flush();

      // 阶段6: 生成最终文件
      muxer.finalize();
      const videoBlob = new Blob([target.buffer], { type: "video/mp4" });

      // 阶段7: 生成字幕文件（如果需要）
      let subtitleFiles = [];
      if (generateSubtitles && subtitleLanguages.length > 0) {
        if (onProgress)
          onProgress({ phase: "generating-subtitles", progress: 95 });

        const { generateSRTSubtitles } = await import("./videoExporter.js");

        for (const language of subtitleLanguages) {
          const srtContent = generateSRTSubtitles(
            timeline,
            language,
            configJson
          );
          subtitleFiles.push({
            name: `subtitle_${language}.srt`,
            content: srtContent,
          });
        }
      }

      if (onProgress) onProgress({ phase: "complete", progress: 100 });

      return {
        videoBlob,
        fileName: `${fileName}.mp4`,
        subtitleFiles,
      };
    } catch (error) {
      if (error.name === "AbortError") {
        console.log("视频导出被取消");
        throw error;
      }

      console.error("WebCodecs视频导出失败:", error);
      throw new Error(`视频导出失败: ${error.message}`);
    }
  }

  /**
   * 生成视频帧（带缓存优化）
   */
  async _generateFrames(timeline, renderContent, options) {
    const frames = [];

    // 导入帧缓存系统
    const { frameCache, resetCacheStats, printFinalCacheStats } = await import(
      "./frameCache.js"
    );

    // 重置缓存统计
    resetCacheStats();

    console.log(`开始生成 ${timeline.length} 个帧，启用缓存优化`);

    for (let i = 0; i < timeline.length; i++) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const item = timeline[i];

      // 生成缓存键，包含渲染选项
      const cacheOptions = {
        width: options.width,
        height: options.height,
        containerWidth: options.containerWidth,
        containerHeight: options.containerHeight,
        // 从renderContent函数中提取样式信息（如果可能）
        // 这里我们假设样式信息在item或全局配置中
      };

      const cacheKey = frameCache.generateCacheKey(item, cacheOptions);

      // 尝试从缓存获取
      let canvas = frameCache.get(cacheKey);

      if (!canvas) {
        // 缓存未命中，执行实际渲染
        const contentElement = await Promise.resolve(renderContent(item));

        // 使用现有的renderElementToCanvas函数
        const { renderElementToCanvas } = await import("./videoExporter.js");
        canvas = await renderElementToCanvas(contentElement, {
          width: options.width,
          height: options.height,
          // 传递容器尺寸信息，用于正确的缩放计算
          containerWidth: options.containerWidth,
          containerHeight: options.containerHeight,
          signal: options.signal,
        });

        // 将结果存入缓存
        frameCache.set(cacheKey, canvas);
      }

      const frameDuration = item.actualDuration || item.duration;
      const frameStartTime = item.actualStartTime || item.startTime;

      console.log(
        `帧 ${i + 1}/${timeline.length}: 内容="${item.content?.substring(
          0,
          20
        )}..." 开始时间=${frameStartTime.toFixed(
          3
        )}s 持续时间=${frameDuration.toFixed(3)}s`
      );

      frames.push({
        canvas,
        duration: frameDuration,
        startTime: frameStartTime,
        originalItem: item, // 保存原始时间线项目的引用
      });

      // 更新进度
      if (options.onProgress) {
        options.onProgress((i + 1) / timeline.length, i + 1, timeline.length);
      }
    }

    // 打印最终缓存统计
    printFinalCacheStats();

    return frames;
  }

  /**
   * 初始化编码器
   */
  async _initializeEncoders(options) {
    const { width, height, frameRate, bitrate, audioBuffer } = options;

    const target = new ArrayBufferTarget();
    const muxer = new Muxer({
      target,
      video: {
        codec: "avc",
        width,
        height,
        frameRate,
      },
      audio: {
        codec: "aac",
        numberOfChannels: audioBuffer.numberOfChannels,
        sampleRate: audioBuffer.sampleRate,
      },
      fastStart: "in-memory",
      firstTimestampBehavior: "offset",
    });

    // 创建视频编码器
    const videoEncoder = new VideoEncoder({
      output: (chunk, metadata) => {
        muxer.addVideoChunk(chunk, metadata);
      },
      error: (error) => {
        console.error("视频编码错误:", error);
      },
    });

    // 配置视频编码器 - 根据分辨率选择合适的AVC Level
    let codecString;
    if (width <= 640 && height <= 480) {
      codecString = "avc1.42E01E"; // Level 3.0 - 支持最大640x480
    } else if (width <= 1280 && height <= 720) {
      codecString = "avc1.42E01F"; // Level 3.1 - 支持最大1280x720
    } else if (width <= 1920 && height <= 1080) {
      codecString = "avc1.42E028"; // Level 4.0 - 支持最大1920x1080
    } else {
      codecString = "avc1.42E032"; // Level 5.0 - 支持更高分辨率
    }

    const videoConfig = {
      codec: codecString,
      width,
      height,
      bitrate,
      framerate: frameRate,
      hardwareAcceleration: "prefer-hardware",
    };

    videoEncoder.configure(videoConfig);

    // 创建音频编码器
    const audioEncoder = new AudioEncoder({
      output: (chunk, metadata) => {
        muxer.addAudioChunk(chunk, metadata);
      },
      error: (error) => {
        console.error("音频编码错误:", error);
      },
    });

    // 配置音频编码器 - 使用原始采样率或WebCodecs支持的采样率
    let targetSampleRate = audioBuffer.sampleRate;

    // 如果原始采样率不被WebCodecs支持，选择最接近的支持采样率
    if (targetSampleRate !== 44100 && targetSampleRate !== 48000) {
      // 对于24kHz，使用48kHz（2倍关系）
      if (targetSampleRate === 24000) {
        targetSampleRate = 48000;
      } else {
        targetSampleRate = 44100; // 默认使用44.1kHz
      }
    }

    const audioConfig = {
      codec: "mp4a.40.2", // AAC-LC
      sampleRate: targetSampleRate,
      numberOfChannels: audioBuffer.numberOfChannels,
      bitrate: 128000, // 128kbps
    };

    audioEncoder.configure(audioConfig);

    return { videoEncoder, audioEncoder, muxer, target, audioConfig };
  }

  /**
   * 编码视频帧
   */
  async _encodeVideoFrames(encoder, frames, frameRate, options) {
    const frameDuration = 1000000 / frameRate; // 微秒

    for (let i = 0; i < frames.length; i++) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const frame = frames[i];
      const canvas = frame.canvas;

      // 使用帧的实际开始时间
      const frameStartTime = frame.startTime * 1000000; // 转换为微秒

      // 计算这一帧应该重复多少次
      const frameCount = Math.round(frame.duration * frameRate);

      for (let j = 0; j < frameCount; j++) {
        // 计算当前子帧的时间戳
        const currentTimestamp = frameStartTime + j * frameDuration;

        // 创建VideoFrame
        const videoFrame = new VideoFrame(canvas, {
          timestamp: currentTimestamp,
          duration: frameDuration,
        });

        // 编码帧
        encoder.encode(videoFrame, { keyFrame: i === 0 && j === 0 });

        // 释放帧
        videoFrame.close();
      }

      // 更新进度
      if (options.onProgress) {
        options.onProgress((i + 1) / frames.length, i + 1, frames.length);
      }
    }
  }

  /**
   * 编码音频
   */
  async _encodeAudio(encoder, audioBuffer, options) {
    const originalSampleRate = audioBuffer.sampleRate;
    const numberOfChannels = audioBuffer.numberOfChannels;
    const frameSize = 1024; // AAC帧大小

    // 获取音频数据
    const audioData = [];
    for (let channel = 0; channel < numberOfChannels; channel++) {
      audioData.push(audioBuffer.getChannelData(channel));
    }

    const totalSamples = audioBuffer.length;
    let sampleIndex = 0;
    let timestamp = 0;

    while (sampleIndex < totalSamples) {
      if (options.signal?.aborted) {
        throw new DOMException("操作已取消", "AbortError");
      }

      const remainingSamples = totalSamples - sampleIndex;
      const currentFrameSize = Math.min(frameSize, remainingSamples);

      // 创建当前帧的数据
      const frameData = new Float32Array(currentFrameSize * numberOfChannels);

      for (let i = 0; i < currentFrameSize; i++) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
          frameData[i * numberOfChannels + channel] =
            audioData[channel][sampleIndex + i];
        }
      }

      // 创建AudioData - 使用音频缓冲区的采样率
      const audioFrame = new AudioData({
        format: "f32-planar",
        sampleRate: originalSampleRate, // 使用音频缓冲区的实际采样率
        numberOfChannels,
        numberOfFrames: currentFrameSize,
        timestamp: timestamp,
        data: frameData,
      });

      // 编码音频帧
      encoder.encode(audioFrame);

      // 释放帧
      audioFrame.close();

      sampleIndex += currentFrameSize;
      timestamp += (currentFrameSize / originalSampleRate) * 1000000; // 使用原始采样率计算时间

      // 更新进度
      if (options.onProgress) {
        options.onProgress(sampleIndex / totalSamples);
      }
    }
  }

  /**
   * 重采样音频到目标采样率
   */
  async _resampleAudio(audioBuffer, targetSampleRate) {
    const numChannels = audioBuffer.numberOfChannels;
    const duration = audioBuffer.duration;
    const newLength = Math.round(duration * targetSampleRate);

    // 创建离线音频上下文进行重采样
    const offlineContext = new OfflineAudioContext(
      numChannels,
      newLength,
      targetSampleRate
    );

    // 创建音频源
    const bufferSource = offlineContext.createBufferSource();
    bufferSource.buffer = audioBuffer;
    bufferSource.connect(offlineContext.destination);
    bufferSource.start();

    // 执行重采样
    return await offlineContext.startRendering();
  }

  /**
   * 下载生成的视频
   */
  downloadVideo(videoBlob, fileName) {
    const url = URL.createObjectURL(videoBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * 下载视频和字幕文件（打包为ZIP）
   */
  async downloadVideoWithSubtitles(videoBlob, fileName, subtitleFiles = []) {
    if (subtitleFiles.length === 0) {
      // 如果没有字幕文件，直接下载视频
      this.downloadVideo(videoBlob, fileName);
      return;
    }

    try {
      // 动态导入JSZip
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      // 添加视频文件
      zip.file(fileName, videoBlob);

      // 添加字幕文件
      for (const subtitle of subtitleFiles) {
        zip.file(subtitle.name, subtitle.content);
      }

      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({ type: "blob" });

      // 下载ZIP文件
      const baseName = fileName.replace(/\.[^/.]+$/, ""); // 移除扩展名
      const zipFileName = `${baseName}_with_subtitles.zip`;

      const url = URL.createObjectURL(zipBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = zipFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("打包下载失败:", error);
      // 如果打包失败，至少下载视频文件
      this.downloadVideo(videoBlob, fileName);
      throw new Error(`打包下载失败: ${error.message}`);
    }
  }
}

// 创建单例实例
export const webCodecsExporter = new WebCodecsVideoExporter();
