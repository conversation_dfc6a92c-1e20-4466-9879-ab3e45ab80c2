<template>
    <div class="content-list">
        <div class="content-header">
            <h1>内容管理</h1>
            <div class="header-buttons">
                <el-button type="primary" @click="goToEditor()">
                    <el-icon>
                        <Plus />
                    </el-icon>新建内容
                </el-button>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="filter-section">
            <el-input v-model="searchQuery" placeholder="搜索内容..." clearable class="search-input">
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>

            <el-select v-model="statusFilter" class="filter-item">
                <el-option label="全部" value="all" />
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
            </el-select>

            <el-select v-model="sortBy" class="filter-item">
                <el-option label="更新时间" value="updatedAt" />
                <el-option label="创建时间" value="createdAt" />
                <el-option label="标题" value="title" />
            </el-select>

            <el-select v-model="sortOrder" class="filter-item">
                <el-option label="降序" value="desc" />
                <el-option label="升序" value="asc" />
            </el-select>
        </div>

        <!-- 内容列表 -->
        <el-table v-loading="contentStore.loading" :data="contentStore.filteredContents" style="width: 100%">
            <el-table-column label="缩略图" width="120">
                <template #default="{ row }">
                    <div class="thumbnail-container">
                        <img v-if="row.thumbnailUrl" :src="row.thumbnailUrl" alt="缩略图" class="thumbnail-image" />
                        <div v-else class="thumbnail-placeholder">
                            <el-icon :size="24">
                                <Picture />
                            </el-icon>
                        </div>
                    </div>
                </template>
            </el-table-column>

            <el-table-column prop="name" label="标题" min-width="200">
                <template #default="{ row }">
                    <el-link @click="goToEditor(row.id)" type="primary">{{ row.name }}</el-link>
                </template>
            </el-table-column>

            <el-table-column prop="description" label="描述" min-width="300" show-overflow-tooltip />

            <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === 'published' ? 'success' : 'warning'">
                        {{ row.status === 'published' ? '已发布' : '草稿' }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="tags" label="标签" width="200">
                <template #default="{ row }">
                    <el-tag v-for="tag in parseTags(row.tags)" :key="tag" size="small" class="tag-item">
                        {{ tag }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column prop="updatedAt" label="更新时间" width="180">
                <template #default="{ row }">
                    {{ formatDate(row.updatedAt) }}
                </template>
            </el-table-column>

            <el-table-column label="操作" width="280" fixed="right">
                <template #default="{ row }">
                    <el-button-group>
                        <el-button size="small" type="primary" @click="goToEditor(row.id)" title="编辑">
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button size="small" type="success" @click="goToPlayer(row.id)" title="播放">
                            <el-icon>
                                <VideoPlay />
                            </el-icon>
                        </el-button>
                        <!-- 上线/下架按钮 -->
                        <el-button v-if="row.status === 'draft'" size="small" type="warning" @click="handlePublish(row)"
                            title="上线">
                            <el-icon>
                                <Upload />
                            </el-icon>
                        </el-button>
                        <el-button v-else size="small" type="info" @click="handleUnpublish(row)" title="下架">
                            <el-icon>
                                <Download />
                            </el-icon>
                        </el-button>
                        <el-button size="small" type="danger" @click="handleDelete(row)" title="删除">
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </el-button-group>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-section">
            <el-pagination v-model="currentPage" :page-size="pageSize" :total="contentStore.pagination?.total || 0"
                :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>

        <!-- 删除确认对话框 -->
        <el-dialog v-model="deleteDialogVisible" title="确认删除" width="30%" destroy-on-close>
            <p>确定要删除 "{{ deleteItem?.name }}" 吗？此操作不可恢复。</p>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="deleteDialogVisible = false">取消</el-button>
                    <el-button type="danger" @click="confirmDelete">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useContentStore } from '@/core/stores/contentStore';
import { Plus, Search, Edit, Delete, VideoPlay, Upload, Download, Picture } from '@element-plus/icons-vue';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';


const contentStore = useContentStore();

// 初始化SEO
useSEO(PAGE_SEO_CONFIG.content);

// 搜索和过滤状态
const searchQuery = ref('');
const statusFilter = ref('all');
const sortBy = ref('updatedAt');
const sortOrder = ref('desc');

// 分页状态
const currentPage = ref(1);
const pageSize = ref(10);

// 删除对话框状态
const deleteDialogVisible = ref(false);
const deleteItem = ref(null);

// 监听过滤条件变化
watch([searchQuery, statusFilter, sortBy, sortOrder], () => {
    contentStore.updateFilters({
        search: searchQuery.value,
        status: statusFilter.value,
        sortBy: sortBy.value,
        sortOrder: sortOrder.value
    });
});

// 监听分页变化
watch([currentPage, pageSize], () => {
    contentStore.updatePagination({
        page: currentPage.value,
        pageSize: pageSize.value
    });
});

// 跳转到编辑器（新标签页）
const goToEditor = (id) => {
    if (id) {
        window.open(`/editor/${id}`, '_blank');
    } else {
        window.open('/editor', '_blank');
    }
};

// 跳转到播放器（新标签页）
const goToPlayer = (id) => {
    if (id) {
        window.open(`/player/${id}`, '_blank');
    }
};

// 处理删除
const handleDelete = (item) => {
    deleteItem.value = item;
    deleteDialogVisible.value = true;
};

// 确认删除
const confirmDelete = async () => {
    try {
        await contentStore.deleteContent(deleteItem.value.id);
        deleteDialogVisible.value = false;
        ElMessage.success('删除成功');
    } catch (error) {
        ElMessage.error('删除失败');
    }
};

// 处理发布（上线）
const handlePublish = async (item) => {
    try {
        await contentStore.publishContent(item.id);
        ElMessage.success('内容已上线');
    } catch (error) {
        ElMessage.error('上线失败: ' + error.message);
    }
};

// 处理下架
const handleUnpublish = async (item) => {
    try {
        await contentStore.unpublishContent(item.id);
        ElMessage.success('内容已下架');
    } catch (error) {
        ElMessage.error('下架失败: ' + error.message);
    }
};

// 处理分页变化
const handleSizeChange = (val) => {
    pageSize.value = val;
};

const handleCurrentChange = (val) => {
    currentPage.value = val;
};

// 格式化日期
const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

// 解析标签字符串为数组
const parseTags = (tagsStr) => {
    if (!tagsStr) return [];
    if (Array.isArray(tagsStr)) return tagsStr;
    return tagsStr.split(',').filter(tag => tag.trim());
};

// 初始化数据
onMounted(() => {
    contentStore.fetchContents();
});
</script>

<style scoped>
.content-list {
    padding: 1.25rem;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.filter-section {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.search-input {
    width: 20rem;
}

.filter-item {
    width: 10rem;
}

.thumbnail-container {
    width: 100%;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 0.25rem;
    background-color: #f5f7fa;
}

.thumbnail-image {
    width: 100%;
    object-fit: cover;
}

.thumbnail-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #909399;
    background-color: #f5f7fa;
}

.tag-item {
    margin-right: 0.375rem;
}

.pagination-section {
    margin-top: 1.25rem;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.625rem;
}
</style>