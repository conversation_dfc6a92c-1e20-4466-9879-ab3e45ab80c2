/**
 * 全局错误处理器
 * 自动捕获和上报各种类型的错误
 */

import { trackError, trackApiError, EVENTS } from "./analytics";

/**
 * 错误处理器配置
 */
const ERROR_HANDLER_CONFIG = {
  // 是否启用错误上报
  enabled: true,
  // 是否在控制台输出错误
  logToConsole: true,
  // 错误采样率（0-1，1表示上报所有错误）
  sampleRate: 1.0,
  // 忽略的错误类型
  ignoredErrors: [
    "ResizeObserver loop limit exceeded",
    "Non-Error promise rejection captured",
    "Script error.",
    "Network request failed",
  ],
  // 忽略的URL模式
  ignoredUrls: [/chrome-extension:/, /moz-extension:/, /safari-extension:/],
};

/**
 * 检查是否应该忽略错误
 * @param {Error|string} error - 错误对象或消息
 * @param {string} [url] - 错误发生的URL
 * @returns {boolean} - 是否应该忽略
 */
function shouldIgnoreError(error, url) {
  const message = typeof error === "string" ? error : error.message;

  // 检查忽略的错误消息
  if (
    ERROR_HANDLER_CONFIG.ignoredErrors.some((ignored) =>
      message.includes(ignored)
    )
  ) {
    return true;
  }

  // 检查忽略的URL模式
  if (
    url &&
    ERROR_HANDLER_CONFIG.ignoredUrls.some((pattern) => pattern.test(url))
  ) {
    return true;
  }

  // 采样率检查
  if (Math.random() > ERROR_HANDLER_CONFIG.sampleRate) {
    return true;
  }

  return false;
}

/**
 * 处理JavaScript错误
 * @param {Error} error - 错误对象
 * @param {Object} [context={}] - 额外上下文
 */
export function handleJavaScriptError(error, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled || shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("JavaScript Error:", error);
  }

  trackError(error, {
    type: "javascript_error",
    ...context,
  });
}

/**
 * 处理Promise拒绝错误
 * @param {any} reason - 拒绝原因
 * @param {Object} [context={}] - 额外上下文
 */
export function handlePromiseRejection(reason, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const error = reason instanceof Error ? reason : new Error(String(reason));

  if (shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Unhandled Promise Rejection:", reason);
  }

  trackError(error, {
    type: "promise_rejection",
    ...context,
  });
}

/**
 * 处理Vue组件错误
 * @param {Error} error - 错误对象
 * @param {Object} instance - Vue组件实例
 * @param {string} info - 错误信息
 */
export function handleVueError(error, instance, info) {
  if (!ERROR_HANDLER_CONFIG.enabled || shouldIgnoreError(error)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Vue Error:", error, "Info:", info);
  }

  const componentName =
    instance?.$options?.name || instance?.$options?.__name || "Unknown";

  trackError(error, {
    type: "vue_error",
    component: componentName,
    info,
    lifecycle: info,
  });
}

/**
 * 处理网络错误
 * @param {string} url - 请求URL
 * @param {number} [status] - HTTP状态码
 * @param {string} [message] - 错误消息
 * @param {Object} [context={}] - 额外上下文
 */
export function handleNetworkError(url, status, message, context = {}) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const error = new Error(message || `Network request failed: ${status}`);

  if (shouldIgnoreError(error, url)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Network Error:", { url, status, message });
  }

  trackApiError(url, status || 0, message || "Network request failed", {
    type: "network_error",
    ...context,
  });
}

/**
 * 处理资源加载错误
 * @param {Event} event - 错误事件
 */
export function handleResourceError(event) {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  const target = event.target || event.srcElement;
  const resourceType = target.tagName?.toLowerCase() || "unknown";
  const resourceUrl = target.src || target.href || "unknown";

  if (shouldIgnoreError("Resource load failed", resourceUrl)) {
    return;
  }

  if (ERROR_HANDLER_CONFIG.logToConsole) {
    console.error("Resource Load Error:", { resourceType, resourceUrl });
  }

  trackError("Resource load failed", {
    type: "resource_error",
    resourceType,
    resourceUrl,
  });
}

/**
 * 初始化全局错误处理器
 */
export function initializeErrorHandler() {
  if (!ERROR_HANDLER_CONFIG.enabled) {
    return;
  }

  // 捕获JavaScript错误
  window.addEventListener("error", (event) => {
    handleJavaScriptError(event.error || new Error(event.message), {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });

  // 捕获Promise拒绝
  window.addEventListener("unhandledrejection", (event) => {
    handlePromiseRejection(event.reason);
  });

  // 捕获资源加载错误
  window.addEventListener(
    "error",
    (event) => {
      if (event.target !== window) {
        handleResourceError(event);
      }
    },
    true
  );

  console.log("全局错误处理器已初始化");
}

/**
 * 配置错误处理器
 * @param {Object} config - 配置选项
 */
export function configureErrorHandler(config) {
  Object.assign(ERROR_HANDLER_CONFIG, config);
}

/**
 * 手动上报错误
 * @param {Error|string} error - 错误对象或消息
 * @param {Object} [context={}] - 额外上下文
 */
export function reportError(error, context = {}) {
  handleJavaScriptError(
    error instanceof Error ? error : new Error(error),
    context
  );
}

export default {
  initializeErrorHandler,
  configureErrorHandler,
  reportError,
  handleJavaScriptError,
  handlePromiseRejection,
  handleVueError,
  handleNetworkError,
  handleResourceError,
};
