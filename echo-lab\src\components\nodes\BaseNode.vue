<!--
  基础节点组件
  所有节点类型的基础组件，提供通用的节点结构和功能
-->
<template>
  <div class="base-node" :class="{ 'has-error': hasError, 'is-selected': isSelected }">
    <!-- 拖动区域 -->
    <div class="drag-handle" :style="{ backgroundColor: nodeTypeColor }">
      <div class="drag-handle-icon">
        <el-icon>
          <DArrowLeft />
        </el-icon>
        <el-icon>
          <DArrowRight />
        </el-icon>
      </div>
    </div>

    <!-- 节点头部 -->
    <div class="node-header" :style="{ backgroundColor: nodeTypeColor }">
      <div class="node-title">
        <el-icon v-if="nodeTypeIcon">
          <component :is="nodeTypeIcon" />
        </el-icon>
        <span>{{ displayName }}</span>
      </div>
      <div class="node-actions">
        <el-tooltip content="编辑节点名称" placement="top">
          <el-button link size="small" @click="showEditNameDialog = true">
            <el-icon>
              <Edit />
            </el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="删除节点" placement="top">
          <el-button link size="small" @click="deleteNode">
            <el-icon>
              <Delete />
            </el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>

    <!-- 节点内容 -->
    <div class="node-content">
      <slot></slot>
    </div>

    <!-- 节点底部 -->
    <div class="node-footer" v-if="hasError || showFooter">
      <div class="error-message" v-if="hasError">
        <el-icon>
          <Warning />
        </el-icon>
        <span>{{ errorMessage }}</span>
      </div>
      <slot name="footer"></slot>
    </div>

    <!-- 连接点 -->
    <div class="connection-points">
      <div class="input-point" v-if="showInputPoint"></div>
      <div class="output-point" v-if="showOutputPoint"></div>
    </div>

    <!-- 编辑名称对话框 -->
    <standard-dialog v-model="showEditNameDialog" title="编辑节点名称" width="30%" :show-confirm="true" confirm-text="确定"
      @confirm="saveCustomName" @cancel="showEditNameDialog = false">
      <el-form :model="nameForm" label-position="top">
        <el-form-item label="节点名称">
          <el-input v-model="nameForm.customName" placeholder="输入自定义节点名称"></el-input>
        </el-form-item>
      </el-form>
    </standard-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Edit, Delete, DArrowLeft, DArrowRight, Warning } from '@element-plus/icons-vue';
import { useNodeStore } from '@/core/stores/nodeStore';
import { getNodeDisplayName, getNodeTypeColor, hasNodeError, getNodeErrorMessage } from '@/core/utils/nodeUtils';
import nodeFactory from '@/core/factories/NodeFactory';
import StandardDialog from '../common/StandardDialog.vue';

const props = defineProps({
  nodeId: {
    type: String,
    required: true
  },
  showInputPoint: {
    type: Boolean,
    default: true
  },
  showOutputPoint: {
    type: Boolean,
    default: true
  },
  showFooter: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['node-updated']);

const nodeStore = useNodeStore();

// 节点数据
const node = computed(() => nodeStore.getNode(props.nodeId));
const nodeType = computed(() => node.value?.type || '');
const nodeTypeConfig = computed(() => nodeFactory.getNodeTypeConfig(nodeType.value));
const nodeTypeColor = computed(() => nodeTypeConfig.value?.color || '#909399');
const nodeTypeIcon = computed(() => nodeTypeConfig.value?.icon || '');

// 节点状态
const isSelected = computed(() => nodeStore.selectedNodeId === props.nodeId);
const hasError = computed(() => {
  if (!node.value) return false;
  const sourceResults = node.value.sourceIds.map(id => nodeStore.getNodeResult(id));
  return hasNodeError(node.value, sourceResults);
});
const errorMessage = computed(() => {
  if (!node.value) return '';
  const sourceResults = node.value.sourceIds.map(id => nodeStore.getNodeResult(id));
  return getNodeErrorMessage(node.value, sourceResults);
});

// 节点名称
const displayName = computed(() => {
  if (!node.value) return '';
  return getNodeDisplayName(node.value, nodeFactory.getAllNodeTypes());
});

// 编辑名称对话框
const showEditNameDialog = ref(false);
const nameForm = ref({
  customName: ''
});

// 打开编辑名称对话框
function openEditNameDialog() {
  nameForm.value.customName = node.value?.customName || '';
  showEditNameDialog.value = true;
}

// 保存自定义名称
function saveCustomName() {
  if (node.value) {
    nodeStore.updateNodeCustomName(props.nodeId, nameForm.value.customName);
    ElMessage.success('节点名称已更新');
    showEditNameDialog.value = false;

    // 通知父组件节点已更新
    emit('node-updated', props.nodeId);
  }
}

// 删除节点
function deleteNode() {
  ElMessageBox.confirm('确定要删除此节点吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    nodeStore.removeNode(props.nodeId);
    ElMessage.success('节点已删除');
  }).catch(() => {
    // 取消操作
  });
}

// 暴露方法
defineExpose({
  openEditNameDialog,
  deleteNode
});
</script>

<style scoped>
.base-node {
  width: 18.75rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  overflow: visible;
  /* 修改为visible，允许内容溢出 */
  position: relative;
  transition: box-shadow 0.3s, transform 0.3s;
  border: 0.0625rem solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: auto;
  /* 自适应高度 */
}

.base-node.is-selected {
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2);
  border-color: #409EFF;
}

.base-node.has-error {
  border-color: #F56C6C;
}

.drag-handle {
  height: 0.5rem;
  width: 100%;
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  position: relative;
  z-index: 2;
  /* 确保拖动区域在最上层 */
}

.drag-handle-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  opacity: 0.7;
  transform: scale(0.8);
}

.drag-handle-icon .el-icon {
  color: #ffffff;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  color: #ffffff;
  cursor: move;
  /* 添加移动光标 */
}

.node-title {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 1rem;
}

.node-title .el-icon {
  margin-right: 0.5rem;
}

.node-actions {
  display: flex;
  align-items: center;
  cursor: default;
  /* 覆盖父元素的 cursor: move */
}

.node-actions .el-button {
  color: #ffffff;
  padding: 0.25rem;
  cursor: pointer;
  /* 确保按钮有正确的光标 */
}

.node-content {
  padding: 1rem;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  /* 确保flex子元素可以正确滚动 */
  overflow: visible;
  /* 允许内容溢出，使子元素的滚动生效 */
}

.node-footer {
  padding: 0.75rem 1rem;
  background-color: #f5f7fa;
  border-top: 0.0625rem solid #e4e7ed;
  font-size: 0.875rem;
}

.error-message {
  display: flex;
  align-items: center;
  color: #F56C6C;
}

.error-message .el-icon {
  margin-right: 0.5rem;
}

.connection-points {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.input-point {
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background-color: #409EFF;
  border-radius: 50%;
  top: 50%;
  left: -0.25rem;
  transform: translateY(-50%);
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
}

.output-point {
  position: absolute;
  width: 0.5rem;
  height: 0.5rem;
  background-color: #409EFF;
  border-radius: 50%;
  top: 50%;
  right: -0.25rem;
  transform: translateY(-50%);
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
}

.has-error .input-point,
.has-error .output-point {
  background-color: #F56C6C;
}
</style>
