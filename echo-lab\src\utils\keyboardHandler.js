/**
 * 键盘事件处理工具
 * 统一管理播放器的键盘快捷键逻辑
 */

/**
 * 键盘状态管理
 */
export class KeyboardState {
  constructor() {
    this.lastKeyTime = 0;
    this.lastKeyCode = '';
    this.pressCount = 0;
    this.resetTimer = null;
  }

  /**
   * 更新按键状态
   * @param {string} keyCode 按键代码
   * @returns {boolean} 是否为连续按键
   */
  updateKeyState(keyCode) {
    const now = Date.now();
    const isSameKey = keyCode === this.lastKeyCode;
    const isWithinTimeWindow = now - this.lastKeyTime <= 300;

    if (isSameKey && isWithinTimeWindow) {
      this.pressCount++;
    } else {
      this.pressCount = 1;
    }

    this.lastKeyTime = now;
    this.lastKeyCode = keyCode;

    // 清除之前的重置计时器
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
    }

    // 设置新的重置计时器
    this.resetTimer = setTimeout(() => {
      this.pressCount = 0;
    }, 500);

    return this.pressCount > 1;
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.resetTimer) {
      clearTimeout(this.resetTimer);
      this.resetTimer = null;
    }
  }
}

/**
 * 检查是否在输入状态
 * @param {Event} event 键盘事件
 * @returns {boolean} 是否在输入状态
 */
export function isInputting(event) {
  return event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA';
}

/**
 * 播放器控制动作
 */
export const PLAYER_ACTIONS = {
  TOGGLE_PLAY: 'togglePlay',
  JUMP_TO_CURRENT_START: 'jumpToCurrentStart',
  JUMP_TO_PREVIOUS: 'jumpToPrevious',
  JUMP_TO_NEXT: 'jumpToNext',
  JUMP_TO_FIRST: 'jumpToFirst',
  JUMP_TO_LAST: 'jumpToLast',
  JUMP_TO_CURRENT_END: 'jumpToCurrentEnd',
  JUMP_TO_PREVIOUS_SENTENCE: 'jumpToPreviousSentence',
  JUMP_TO_NEXT_SENTENCE: 'jumpToNextSentence'
};

/**
 * 键盘快捷键映射
 */
export const KEYBOARD_SHORTCUTS = {
  'Space': { action: PLAYER_ACTIONS.TOGGLE_PLAY, preventDefault: true },
  'ArrowLeft': { action: PLAYER_ACTIONS.JUMP_TO_CURRENT_START, continuous: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, preventDefault: true },
  'KeyA': { action: PLAYER_ACTIONS.JUMP_TO_CURRENT_START, continuous: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, preventDefault: true },
  'ArrowRight': { action: PLAYER_ACTIONS.JUMP_TO_NEXT, continuous: PLAYER_ACTIONS.JUMP_TO_NEXT, preventDefault: true },
  'KeyD': { action: PLAYER_ACTIONS.JUMP_TO_NEXT, continuous: PLAYER_ACTIONS.JUMP_TO_NEXT, preventDefault: true },
  'ArrowUp': { action: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, continuous: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, preventDefault: true },
  'KeyW': { action: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, continuous: PLAYER_ACTIONS.JUMP_TO_PREVIOUS, preventDefault: true },
  'ArrowDown': { action: null, preventDefault: true }, // 暂时禁用
  'Home': { action: PLAYER_ACTIONS.JUMP_TO_CURRENT_START, continuous: PLAYER_ACTIONS.JUMP_TO_FIRST, preventDefault: true },
  'End': { action: PLAYER_ACTIONS.JUMP_TO_LAST, continuous: PLAYER_ACTIONS.JUMP_TO_CURRENT_END, preventDefault: true },
  'BracketLeft': { action: PLAYER_ACTIONS.JUMP_TO_PREVIOUS_SENTENCE, preventDefault: true },
  'Comma': { action: PLAYER_ACTIONS.JUMP_TO_PREVIOUS_SENTENCE, preventDefault: true },
  'BracketRight': { action: PLAYER_ACTIONS.JUMP_TO_NEXT_SENTENCE, preventDefault: true },
  'Period': { action: PLAYER_ACTIONS.JUMP_TO_NEXT_SENTENCE, preventDefault: true }
};

/**
 * 键盘事件处理器
 */
export class KeyboardHandler {
  constructor(getVideoPlayer, currentIndex, timeline, updateIndex) {
    this.getVideoPlayer = getVideoPlayer;
    this.currentIndex = currentIndex;
    this.timeline = timeline;
    this.updateIndex = updateIndex;
    this.keyState = new KeyboardState();
  }

  /**
   * 处理键盘事件
   * @param {KeyboardEvent} event 键盘事件
   */
  handleKeyDown(event) {
    // 如果用户正在输入，不处理快捷键
    if (isInputting(event)) {
      return;
    }

    const shortcut = KEYBOARD_SHORTCUTS[event.code];
    if (!shortcut) {
      return;
    }

    if (shortcut.preventDefault) {
      event.preventDefault();
    }

    const videoPlayer = this.getVideoPlayer();
    if (!videoPlayer) {
      return;
    }

    const isContinuous = this.keyState.updateKeyState(event.code);
    const action = isContinuous && shortcut.continuous ? shortcut.continuous : shortcut.action;

    if (!action) {
      return;
    }

    this.executeAction(action, videoPlayer, isContinuous);
  }

  /**
   * 执行播放器动作
   * @param {string} action 动作类型
   * @param {Object} videoPlayer 播放器实例
   * @param {boolean} isContinuous 是否为连续按键
   */
  executeAction(action, videoPlayer, isContinuous) {
    const currentIdx = this.currentIndex.value;
    const timelineLength = this.timeline.value.length;

    switch (action) {
      case PLAYER_ACTIONS.TOGGLE_PLAY:
        this.togglePlay(videoPlayer);
        break;

      case PLAYER_ACTIONS.JUMP_TO_CURRENT_START:
        if (videoPlayer.jumpToCurrentSentenceStart) {
          videoPlayer.jumpToCurrentSentenceStart();
        }
        break;

      case PLAYER_ACTIONS.JUMP_TO_PREVIOUS:
        this.jumpToPrevious(videoPlayer, currentIdx, isContinuous);
        break;

      case PLAYER_ACTIONS.JUMP_TO_NEXT:
        this.jumpToNext(videoPlayer, currentIdx, timelineLength, isContinuous);
        break;

      case PLAYER_ACTIONS.JUMP_TO_FIRST:
        this.updateIndex(0);
        videoPlayer.jumpToIndex(0);
        break;

      case PLAYER_ACTIONS.JUMP_TO_LAST:
        const lastIndex = timelineLength - 1;
        this.updateIndex(lastIndex);
        videoPlayer.jumpToIndex(lastIndex);
        break;

      case PLAYER_ACTIONS.JUMP_TO_CURRENT_END:
        this.jumpToCurrentEnd(videoPlayer, currentIdx);
        break;

      case PLAYER_ACTIONS.JUMP_TO_PREVIOUS_SENTENCE:
        if (videoPlayer.jumpToPreviousSentence) {
          videoPlayer.jumpToPreviousSentence();
        }
        break;

      case PLAYER_ACTIONS.JUMP_TO_NEXT_SENTENCE:
        if (videoPlayer.jumpToNextSentence) {
          videoPlayer.jumpToNextSentence();
        }
        break;
    }
  }

  /**
   * 切换播放状态
   */
  togglePlay(videoPlayer) {
    if (videoPlayer.togglePlay) {
      videoPlayer.togglePlay();
    } else {
      // 降级处理
      const playButton = document.querySelector('.video-player-container .play-pause-control');
      if (playButton) {
        playButton.click();
      } else {
        const pauseOverlay = document.querySelector('.pause-overlay');
        if (pauseOverlay) {
          pauseOverlay.click();
        }
      }
    }
  }

  /**
   * 跳转到上一句
   */
  jumpToPrevious(videoPlayer, currentIdx, isContinuous) {
    if (currentIdx > 0) {
      const jumpOffset = isContinuous && currentIdx > 1 ? 2 : 1;
      const newIndex = Math.max(currentIdx - jumpOffset, 0);
      this.updateIndex(newIndex);
      videoPlayer.jumpToIndex(newIndex);
    }
  }

  /**
   * 跳转到下一句
   */
  jumpToNext(videoPlayer, currentIdx, timelineLength, isContinuous) {
    if (currentIdx < timelineLength - 1) {
      const jumpOffset = isContinuous && currentIdx < timelineLength - 2 ? 2 : 1;
      const newIndex = Math.min(currentIdx + jumpOffset, timelineLength - 1);
      this.updateIndex(newIndex);
      videoPlayer.jumpToIndex(newIndex);
    }
  }

  /**
   * 跳转到当前句子结尾
   */
  jumpToCurrentEnd(videoPlayer, currentIdx) {
    if (currentIdx >= 0 && currentIdx < this.timeline.value.length) {
      const item = this.timeline.value[currentIdx];
      const startTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;
      const duration = item.actualDuration !== undefined ? item.actualDuration : item.duration;
      const endTime = startTime + duration - 0.1; // 减去0.1秒，避免跳到下一句

      if (videoPlayer.jumpToTime) {
        videoPlayer.jumpToTime(endTime);
      }
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.keyState.cleanup();
  }
}
