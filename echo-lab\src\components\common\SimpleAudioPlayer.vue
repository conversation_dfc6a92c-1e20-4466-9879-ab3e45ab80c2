<!--
  简单音频播放器组件
  只在用户点击时才加载和播放音频
-->
<template>
  <div class="simple-audio-player">
    <div v-if="!audioLoaded" class="play-button" @click="loadAndPlay">
      <el-icon class="play-icon">
        <VideoPlay />
      </el-icon>
      <span class="play-text">播放</span>
    </div>
    <div v-else class="player-controls">
      <div class="control-button" @click="togglePlay">
        <el-icon v-if="isPlaying">
          <VideoPause />
        </el-icon>
        <el-icon v-else>
          <VideoPlay />
        </el-icon>
      </div>
      <div class="progress-container" @click="seekAudio">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
      </div>
      <div class="time-display">{{ formatTime(currentTime) }}/{{ formatTime(duration) }}</div>
    </div>
    <!-- 音频元素 - 不预加载 -->
    <audio ref="audioElement" preload="none" @timeupdate="updateProgress" @ended="handleEnded"
      @loadedmetadata="handleMetadataLoaded" @error="handleError" style="display: none;">
    </audio>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, VideoPause } from '@element-plus/icons-vue';

const props = defineProps({
  // 音频URL
  url: {
    type: String,
    required: true
  }
});

// 音频元素引用
const audioElement = ref(null);

// 播放状态
const audioLoaded = ref(false);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const progress = ref(0);

// 验证URL是否有效
const validateUrl = (url) => {
  if (!url) return false;

  // 如果URL是相对路径，确保它以/开头
  if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('/')) {
    return '/' + url;
  }

  return url;
};

// 加载并播放音频
const loadAndPlay = async () => {
  if (!props.url) {
    ElMessage.warning('没有可播放的音频');
    return;
  }

  try {
    // 设置音频元素
    if (audioElement.value) {
      // 确保音频元素的src是有效的
      audioElement.value.src = validateUrl(props.url);

      // 加载音频
      audioElement.value.load();
      audioLoaded.value = true;

      // 播放音频
      const playPromise = audioElement.value.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            isPlaying.value = true;
            console.log('开始播放音频');
          })
          .catch(error => {
            console.error('播放音频失败:', error);
            ElMessage.error('播放失败，请稍后重试');
            isPlaying.value = false;
            audioLoaded.value = false;
          });
      }
    }
  } catch (error) {
    console.error('加载音频失败:', error);
    ElMessage.error('加载音频失败，请稍后重试');
    audioLoaded.value = false;
  }
};

// 切换播放/暂停
const togglePlay = () => {
  if (!audioElement.value) return;

  try {
    if (isPlaying.value) {
      audioElement.value.pause();
      isPlaying.value = false;
    } else {
      const playPromise = audioElement.value.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            isPlaying.value = true;
          })
          .catch(error => {
            console.error('播放音频失败:', error);
            ElMessage.error('播放失败，请稍后重试');
            isPlaying.value = false;
          });
      }
    }
  } catch (error) {
    console.error('播放/暂停操作失败:', error);
    ElMessage.error('播放操作失败，请稍后重试');
  }
};

// 更新进度
const updateProgress = () => {
  if (!audioElement.value) return;

  currentTime.value = audioElement.value.currentTime;

  if (duration.value > 0) {
    progress.value = (currentTime.value / duration.value) * 100;
  }
};

// 处理音频结束
const handleEnded = () => {
  isPlaying.value = false;
  currentTime.value = 0;
  progress.value = 0;
};

// 处理元数据加载
const handleMetadataLoaded = () => {
  if (!audioElement.value) return;
  duration.value = audioElement.value.duration;
};

// 处理错误
const handleError = (error) => {
  console.error('音频加载错误:', error);
  ElMessage.error('音频加载失败，请稍后重试');
  audioLoaded.value = false;
  isPlaying.value = false;
};

// 跳转到指定位置
const seekAudio = (event) => {
  if (!audioElement.value || !audioLoaded.value) return;

  const container = event.currentTarget;
  const rect = container.getBoundingClientRect();
  const offsetX = event.clientX - rect.left;
  const percentage = offsetX / rect.width;

  // 设置新的播放位置
  audioElement.value.currentTime = percentage * duration.value;
  updateProgress();
};

// 格式化时间
const formatTime = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00';

  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 组件卸载时停止播放
onUnmounted(() => {
  if (audioElement.value && !audioElement.value.paused) {
    audioElement.value.pause();
  }
});
</script>

<style scoped>
.simple-audio-player {
  width: 100%;
  border-radius: 0.25rem;
  background-color: #f5f7fa;
  overflow: hidden;
}

.play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  cursor: pointer;
  background-color: #f5f7fa;
  transition: background-color 0.2s;
}

.play-button:hover {
  background-color: #e4e7ed;
}

.play-icon {
  font-size: 1.25rem;
  color: #409eff;
  margin-right: 0.5rem;
}

.play-text {
  font-size: 0.875rem;
  color: #606266;
}

.player-controls {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #f5f7fa;
}

.control-button {
  cursor: pointer;
  margin-right: 0.5rem;
  color: #409eff;
  font-size: 1.25rem;
}

.progress-container {
  flex: 1;
  height: 0.5rem;
  margin: 0 0.5rem;
  cursor: pointer;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background-color: #e4e7ed;
  border-radius: 0.25rem;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #409eff;
  border-radius: 0.25rem;
  transition: width 0.1s linear;
}

.time-display {
  font-size: 0.75rem;
  color: #606266;
  min-width: 5rem;
  text-align: right;
}
</style>
