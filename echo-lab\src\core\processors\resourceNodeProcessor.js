/**
 * 资源管理节点处理器
 * 整合标注、翻译和音频功能
 */

/**
 * 处理资源管理节点
 * @param {Object} node - 节点对象
 * @param {Array} sourceResults - 源节点结果数组
 * @returns {Object} 处理结果
 */
function resourceNodeProcessor(node, sourceResults = []) {
  // 检查源节点结果
  if (!sourceResults || sourceResults.length === 0) {
    return {
      annotations: {},
      translations: {},
      audioItems: [],
      isEmpty: true,
    };
  }

  // 处理所有源结果
  const allSegments = [];

  // 遍历所有源结果，收集分句
  for (const sourceResult of sourceResults) {
    if (sourceResult.segments) {
      allSegments.push(...sourceResult.segments);
    }
  }

  // 处理标注
  const annotations = processAnnotations(node, allSegments);

  // 处理翻译
  const translations = processTranslations(node);

  // 处理音频
  const audioItems = processAudioItems(node, allSegments);

  return {
    annotations,
    translations,
    audioItems,
    sourceSegments: allSegments.length > 0 ? allSegments : null,
    isEmpty: allSegments.length === 0,
  };
}

/**
 * 处理标注
 * @param {Object} node - 节点对象
 * @param {Array} allSegments - 所有分句
 * @returns {Object} 标注结果
 */
function processAnnotations(node, allSegments = []) {
  // 如果已有标注，直接使用
  if (
    node.params.annotations &&
    Object.keys(node.params.annotations).length > 0
  ) {
    return { ...node.params.annotations };
  }

  // 创建空标注对象
  const annotations = {};

  // 为每个分句创建空的标注项
  if (allSegments && allSegments.length > 0) {
    // 使用Set记录已处理的段落ID，避免重复添加
    const processedIds = new Set();

    allSegments.forEach((segment) => {
      // 检查是否已处理过该段落
      if (processedIds.has(segment.id)) {
        return;
      }

      // 标记为已处理
      processedIds.add(segment.id);

      // 根据语言创建不同类型的标注
      if (segment.language === "ja") {
        // 日语标注使用对象格式
        annotations[segment.id] = {
          reading: "",
          characters: [],
        };
      } else {
        // 非日语标注使用字符串格式
        annotations[segment.id] = "";
      }

      // 处理关键词
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach((keyword) => {
          const keywordId = keyword.id;
          const keywordLanguage =
            keyword.language || segment.language || "auto";

          // 根据语言创建不同类型的标注
          if (keywordLanguage === "ja") {
            // 日语关键词标注使用对象格式
            annotations[keywordId] = {
              reading: "",
              characters: [],
            };
          } else {
            // 非日语关键词标注使用字符串格式
            annotations[keywordId] = "";
          }
        });
      }
    });
  }

  return annotations;
}

/**
 * 处理翻译
 * @param {Object} node - 节点对象
 * @returns {Object} 翻译结果
 */
function processTranslations(node) {
  // 如果已有翻译，直接使用
  if (
    node.params.translations &&
    Object.keys(node.params.translations).length > 0
  ) {
    return { ...node.params.translations };
  }

  // 创建空翻译对象
  const translations = {};

  // 检查目标语言
  const targets = node.params.targets || [];

  // 为每个目标语言创建空对象
  targets.forEach((target) => {
    translations[target] = {};
  });

  return translations;
}

/**
 * 处理音频项
 * @param {Object} node - 节点对象
 * @param {Array} allSegments - 所有分句
 * @returns {Array} 音频项数组
 */
function processAudioItems(node, allSegments) {
  // 创建音频项
  const audioItems = [];
  // 音频项使用标准速度1.0，环节管理将控制实际播放速度和间隔

  // 保存已有音频项的完整状态，避免覆盖用户设置的字段
  const existingAudioMap = {};
  if (node.params.audioItems && node.params.audioItems.length > 0) {
    node.params.audioItems.forEach((item) => {
      // 保存完整的音频项数据，包括用户设置的所有字段
      existingAudioMap[item.id] = { ...item };
    });
  }

  /**
   * 检查文本内容是否发生变化，如果变化则重置音频状态
   * @param {string} audioId - 音频项ID
   * @param {string} newText - 新的文本内容
   * @returns {Object} 音频状态对象
   */
  function getAudioStateForText(audioId, newText) {
    const existingAudio = existingAudioMap[audioId];

    if (!existingAudio) {
      // 没有旧数据，返回默认状态
      return {
        url: "",
        duration: 0,
        status: "pending",
        speed: 1.0,
      };
    }

    // 检查文本内容是否发生变化
    if (existingAudio.text && existingAudio.text !== newText) {
      console.log(`[资源处理器] 检测到文本变化，重置音频状态: ${audioId}`);
      console.log(`旧文本: "${existingAudio.text}"`);
      console.log(`新文本: "${newText}"`);

      // 文本变化，重置音频相关状态但保持其他用户设置
      return {
        ...existingAudio, // 保持所有现有字段
        text: newText, // 更新文本
        url: "", // 重置音频URL
        duration: 0, // 重置时长
        status: "pending", // 重置状态
        audioGenerationHash: "", // 重置音频生成哈希
      };
    }

    // 文本没有变化，保持原有完整状态，只更新文本确保一致性
    return {
      ...existingAudio,
      text: newText, // 确保文本一致
    };
  }

  // 为每个分句创建音频项
  if (allSegments && allSegments.length > 0) {
    // 使用Set记录已处理的段落ID，避免重复添加
    const processedIds = new Set();

    allSegments.forEach((segment) => {
      // 检查是否已处理过该段落
      if (processedIds.has(segment.id)) {
        return;
      }

      // 标记为已处理
      processedIds.add(segment.id);

      // 获取音频状态（包含文本变化检测）
      const audioState = getAudioStateForText(segment.id, segment.content);

      // 判断是否为关键词（关键词是分句的属性，不是分句的类型）
      const isKeyword = segment.isKeyword || false;

      // 添加原文音频项
      audioItems.push({
        // 如果有现有数据，使用现有数据（保留用户设置的字段）
        ...audioState,
        // 确保基本字段正确
        id: segment.id,
        text: segment.content,
        language: segment.language || "ja",
        speaker: segment.speaker || "default", // 说话人标记
        sequenceId: segment.id,
        type: isKeyword ? "keyword" : segment.type || "normal", // 确保关键词类型正确
        audioType: isKeyword ? "keyword" : "source", // 关键词使用keyword类型，其他使用source
        isKeyword: isKeyword, // 标记是否为关键词
      });

      // 为每个翻译目标语言创建音频项
      if (
        node.params.targets &&
        node.params.targets.length > 0 &&
        node.params.translations &&
        Object.keys(node.params.translations).length > 0
      ) {
        node.params.targets.forEach((targetLang) => {
          // 检查是否有该语言的翻译
          if (
            node.params.translations[targetLang] &&
            node.params.translations[targetLang][segment.id] &&
            node.params.translations[targetLang][segment.id].trim()
          ) {
            // 获取翻译音频状态（包含文本变化检测）
            const translationId = `${segment.id}_${targetLang}`;
            const translationText =
              node.params.translations[targetLang][segment.id];
            const translationAudioState = getAudioStateForText(
              translationId,
              translationText
            );

            // 添加翻译音频项
            audioItems.push({
              id: translationId,
              text: translationText,
              language: targetLang,
              speaker: "default", // 翻译音频始终使用默认说话人
              url: translationAudioState.url,
              duration: translationAudioState.duration,
              status: translationAudioState.status,
              speed: translationAudioState.speed,
              sequenceId: `translation_${segment.id}_${targetLang}`,
              type: "normal", // 翻译音频使用普通文本类型
              audioType: "translation", // 标记为翻译音频
              sourceId: segment.id, // 关联到原文ID
              targetLanguage: targetLang, // 目标语言
              audioGenerationHash: translationAudioState.audioGenerationHash, // 音频生成哈希
            });
          }
        });
      }

      // 处理关键词音频
      if (segment.keywords && segment.keywords.length > 0) {
        segment.keywords.forEach((keyword) => {
          // 获取关键词音频状态（包含文本变化检测）
          const keywordId = keyword.id;
          const keywordAudioState = getAudioStateForText(
            keywordId,
            keyword.text
          );

          // 添加关键词音频项
          audioItems.push({
            id: keywordId,
            text: keyword.text,
            language: keyword.language || segment.language || "ja",
            speaker: segment.speaker || "default", // 使用分句的说话人
            url: keywordAudioState.url,
            duration: keywordAudioState.duration,
            status: keywordAudioState.status,
            speed: keywordAudioState.speed,
            sequenceId: `keyword_${segment.id}_${keywordId}`,
            type: "keyword", // 关键词使用keyword类型
            audioType: "keyword", // 标记为关键词音频
            sourceId: segment.id, // 关联到原文ID
            keywordId: keywordId, // 关键词ID
            isKeyword: true, // 明确标记为关键词
          });
        });
      }

      // 环节管理将控制音频播放顺序和间隔
    });
  }

  return audioItems;
}

export default resourceNodeProcessor;
