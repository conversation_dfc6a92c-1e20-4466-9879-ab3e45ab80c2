# Umami 统计工具接入指南

本文档提供了在 Echo Lab 项目中接入 Umami 统计工具的详细步骤。

## 概述

Umami 是一个开源、轻量级、注重隐私的网站统计工具，Echo Lab 使用 Umami 云服务 (https://cloud.umami.is/) 来收集使用数据。Umami 具有以下特点：

- 不使用 cookies，完全符合 GDPR、CCPA 等隐私法规
- 轻量级，不影响网站性能
- 简洁的仪表板，易于使用
- 支持多网站管理
- 支持事件跟踪

## 前端接入步骤

### 1. 安装依赖

不需要安装额外的依赖，Umami 使用简单的 JavaScript 脚本进行跟踪。

### 2. 添加跟踪代码

在 `index.html` 文件的 `<head>` 部分添加以下代码：

```html
<script async defer
  src="https://analytics.umami.is/script.js"
  data-website-id="YOUR-WEBSITE-ID"
></script>
```

注意：
- 我们使用 Umami 云服务的脚本 URL: `https://analytics.umami.is/script.js`
- 将 `YOUR-WEBSITE-ID` 替换为从 Umami 管理面板获取的网站 ID

### 3. 环境变量配置

为了在不同环境中灵活配置 Umami，建议使用环境变量：

1. 在 `.env` 文件中添加：

```
VITE_UMAMI_SCRIPT_URL=https://analytics.umami.is/script.js
VITE_UMAMI_WEBSITE_ID=f655df05-eff0-4bc8-977e-9573fd6130d3
```

2. 修改 `index.html` 中的跟踪代码：

```html
<script async defer
  src="<%= VITE_UMAMI_SCRIPT_URL %>"
  data-website-id="<%= VITE_UMAMI_WEBSITE_ID %>"
></script>
```

### 4. 动态加载（可选）

如果需要根据条件动态加载 Umami，可以在 `main.js` 中添加以下代码：

```javascript
// 动态加载 Umami 跟踪代码
function loadUmami() {
  if (import.meta.env.VITE_UMAMI_SCRIPT_URL && import.meta.env.VITE_UMAMI_WEBSITE_ID) {
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;
    script.src = import.meta.env.VITE_UMAMI_SCRIPT_URL;
    script.setAttribute('data-website-id', import.meta.env.VITE_UMAMI_WEBSITE_ID);
    document.head.appendChild(script);
  }
}

// 在生产环境中加载 Umami
if (import.meta.env.PROD) {
  loadUmami();
}
```

## 高级配置

### 自定义数据域

如果需要跟踪额外的用户数据，可以使用 `data-domains` 属性：

```html
<script async defer
  src="https://analytics.umami.is/script.js"
  data-website-id="f655df05-eff0-4bc8-977e-9573fd6130d3"
  data-domains="echolab.club,app.echolab.club"
></script>
```

### 禁用特定页面的跟踪

如果需要在特定页面禁用跟踪，可以使用 `data-do-not-track` 属性：

```html
<script async defer
  src="https://analytics.umami.is/script.js"
  data-website-id="f655df05-eff0-4bc8-977e-9573fd6130d3"
  data-do-not-track="true"
></script>
```

或者在特定页面动态设置：

```javascript
window.umami.disabled = true; // 禁用跟踪
window.umami.disabled = false; // 启用跟踪
```

### 自动跟踪

默认情况下，Umami 会自动跟踪页面浏览量。如果需要禁用自动跟踪，可以使用 `data-auto-track` 属性：

```html
<script async defer
  src="https://analytics.umami.is/script.js"
  data-website-id="f655df05-eff0-4bc8-977e-9573fd6130d3"
  data-auto-track="false"
></script>
```

## 事件跟踪

### 跟踪用户事件

Umami 允许跟踪用户事件，如按钮点击、表单提交等：

```javascript
// 基本事件跟踪
window.umami.track('按钮点击');

// 带有额外数据的事件跟踪
window.umami.track('视频生成', { language: 'ja', duration: 120 });
```

### 在 Vue 组件中使用

在 Vue 组件中使用 Umami 跟踪事件：

```vue
<script setup>
function trackEvent(eventName, eventData = {}) {
  if (window.umami) {
    window.umami.track(eventName, eventData);
  }
}

function handleButtonClick() {
  // 业务逻辑

  // 跟踪事件
  trackEvent('生成按钮点击', {
    nodeCount: 5,
    videoLength: 120
  });
}
</script>

<template>
  <button @click="handleButtonClick">生成视频</button>
</template>
```

### 推荐跟踪的事件

为了全面了解用户行为，建议跟踪以下事件：

1. **页面交互**
   - 节点创建
   - 节点连接
   - 节点配置修改
   - 项目保存/加载

2. **核心功能使用**
   - 视频生成
   - 视频播放
   - 视频下载
   - 配置导出/导入

3. **用户旅程**
   - 首次访问
   - 完成第一个项目
   - 使用高级功能

## 服务器部署

### 1. Umami 服务器配置

Echo Lab 使用独立的 Umami 实例，部署在 `stats.echolab.club` 子域名上：

1. 创建 Nginx 配置文件：

```bash
sudo nano /etc/nginx/conf.d/stats.echolab.club.conf
```

2. 配置文件内容：

```nginx
server {
    listen 80;
    server_name stats.echolab.club;

    # 重定向到 HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name stats.echolab.club;

    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/stats.echolab.club/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/stats.echolab.club/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;

    # 代理配置
    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

3. 获取 SSL 证书：

```bash
sudo certbot --nginx -d stats.echolab.club
```

4. 重启 Nginx：

```bash
sudo systemctl restart nginx
```

### 2. Umami 应用部署

1. 克隆 Umami 代码库：

```bash
git clone https://github.com/umami-software/umami.git
cd umami
```

2. 安装依赖：

```bash
npm install
```

3. 配置环境变量：

```bash
cp .env.example .env
```

4. 编辑 `.env` 文件：

```
DATABASE_URL=postgresql://username:password@localhost:5432/umami
HASH_SALT=your-random-string
```

5. 构建应用：

```bash
npm run build
```

6. 使用 PM2 运行应用：

```bash
pm2 start npm --name "umami" -- start
```

## 常见问题

### 1. 跟踪代码不工作

- 确保网站可以访问 Umami 服务器
- 检查浏览器控制台是否有错误
- 验证网站 ID 是否正确

### 2. 事件没有被记录

- 确保 `window.umami` 对象存在
- 检查是否启用了广告拦截器
- 验证 Umami 脚本是否正确加载

### 3. 如何测试跟踪是否正常工作

在浏览器控制台中运行以下代码：

```javascript
// 检查 Umami 是否加载
console.log('Umami loaded:', !!window.umami);

// 发送测试事件
if (window.umami) {
  window.umami.track('test-event');
  console.log('Test event sent');
}
```

然后在 Umami 仪表板中查看是否记录了测试事件。

### 4. 隐私合规性

Umami 不使用 cookies，完全符合 GDPR、CCPA 等隐私法规，无需显示 cookie 同意横幅。但仍建议在隐私政策中说明使用了 Umami 进行匿名统计。
