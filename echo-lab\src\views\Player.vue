<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DesktopPlayerLayout from '../components/player/DesktopPlayerLayout.vue';
import MobilePlayerLayout from '../components/player/MobilePlayerLayout.vue';
import PlaybackSettingsPanel from '../components/player/PlaybackSettingsPanel.vue';
import FavoriteButton from '../components/common/FavoriteButton.vue';
import contentService from '../services/contentService';
import { generateTimeline, extractSequencesFromNodes } from '../utils/timelineGenerator';
import { processAudio } from '../utils/audioProcessor';
import { isMobileDevice } from '../utils/deviceDetector';
import { loadUserConfig, updateUserConfig } from '../services/userConfigService';
import { trackEvent, EVENTS } from '@/utils/analytics';
import { useFavoriteStore } from '@/stores/favoriteStore';
import { useUserStore } from '@/stores/userStore';
import { useTemplateStore } from '@/stores/templateStore';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';
import { usePlayerConfig } from '@/composables/usePlayerConfig';
import { KeyboardHandler } from '@/utils/keyboardHandler';
import { collectAllAudioUrls, validateAudioUrls } from '@/utils/audioUrlCollector';
import { createProgressManager } from '@/utils/progressManager';
import { comparePlaybackConfigs } from '@/utils/playbackStrategy';

// 路由参数
const route = useRoute();
const router = useRouter();
const contentId = computed(() => route.params.id);

// 状态管理
const favoriteStore = useFavoriteStore();
const userStore = useUserStore();
const templateStore = useTemplateStore();

// 初始化SEO
const { setVideoSEO } = useSEO(PAGE_SEO_CONFIG.player);

// 状态
const content = ref(null);
const timeline = ref([]);
const audioBuffer = ref(null);
const isLoading = ref(false);
const showSettings = ref(false);
const showContextPanel = ref(true);
const currentIndex = ref(0);


// 使用配置管理 composable
const configManager = usePlayerConfig(contentId);
const { settings, currentTemplate, serverConfig } = configManager;





// 收藏状态
const isFavorited = computed(() => favoriteStore.isFavorite(contentId.value));
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 音频合成进度
const audioSynthesisProgress = reactive({
  total: 0,
  completed: 0,
  percentage: 0,
  isProcessing: false
});
// 视频配置信息
const videoConfig = ref({
  cover: {
    imageUrl: "",
    duration: 3
  },
  copyright: {
    text: "",
    position: "bottomRight"
  }
});

// 文本样式设置
const textStyle = ref({
  fontSize: 2.0, // rem，增加到2.0rem更适合电脑端
  color: '#FFFFFF',
  textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)'
});

// 计算属性
const isMobile = computed(() => {
  return isMobileDevice();
});

// 加载内容
// 设置面板引用
const settingsPanelRef = ref(null);
// 布局组件引用
const layoutRef = ref(null);

// 加载用户配置
const loadUserTextStyleConfig = () => {
  try {
    const userConfig = loadUserConfig();
    if (userConfig.player && userConfig.player.textStyle) {
      textStyle.value = {
        ...textStyle.value,
        ...userConfig.player.textStyle
      };
      console.log('已加载用户文本样式配置:', textStyle.value);
    }
  } catch (error) {
    console.error('加载用户文本样式配置失败:', error);
  }
};

// 处理文本样式变更
const handleTextStyleChange = (newTextStyle) => {
  console.log('文本样式已更新:', newTextStyle);

  // 更新本地文本样式
  textStyle.value = { ...newTextStyle };

  // 更新VideoPlayer组件的文本样式
  const videoPlayer = getVideoPlayer();
  if (videoPlayer) {
    // 如果VideoPlayer组件有textStyle属性，直接更新
    if (videoPlayer.textStyle) {
      videoPlayer.textStyle.fontSize = newTextStyle.fontSize;
      videoPlayer.textStyle.color = newTextStyle.color;
      videoPlayer.textStyle.textShadow = newTextStyle.textShadow;
    }
  }

  // 保存到用户配置
  try {
    const userConfig = loadUserConfig();
    userConfig.player = {
      ...userConfig.player,
      textStyle: { ...newTextStyle }
    };
    updateUserConfig('player.textStyle', newTextStyle);
    console.log('文本样式配置已保存');
  } catch (error) {
    console.error('保存文本样式配置失败:', error);
  }
};

// 获取VideoPlayer实例的辅助函数
const getVideoPlayer = () => {
  if (layoutRef.value && layoutRef.value.videoPlayerRef) {
    return layoutRef.value.videoPlayerRef;
  }
  return null;
};

const loadContent = async () => {
  if (!contentId.value) {
    ElMessage.error('内容ID不能为空');
    router.push('/content');
    return;
  }

  try {
    // 设置加载状态，由VideoPlayer组件显示loading
    isLoading.value = true;

    console.log('1. 开始请求内容数据...');
    const response = await contentService.getContent(contentId.value);
    console.log('1. 内容数据请求完成:', response);

    if (response && response.success && response.content) {
      // 先处理内容数据，确保所有数据都准备好
      const contentData = response.content;
      console.log('内容数据:', contentData);

      // 更新SEO信息
      setVideoSEO(contentData);

      // 检查configJson是否为字符串，如果是则解析为对象或解密
      if (contentData.configJson && typeof contentData.configJson === 'string') {
        try {
          // 导入解密工具
          const { decryptJSON } = await import('../utils/cryptoService');

          // 尝试解密或解析JSON
          contentData.configJson = decryptJSON(contentData.configJson);
          console.log('解析/解密后的configJson:', contentData.configJson);

          // 确保关键词设置正确处理
          if (contentData.configJson.sections) {
            contentData.configJson.sections.forEach(section => {
              // 明确设置关键词开关为布尔值
              section.enableKeywords = section.enableKeywords === true;
              console.log(`处理configJson中的环节 ${section.name}，enableKeywords = ${section.enableKeywords}`);
            });
          }

          // 提取视频配置信息
          if (contentData.configJson.nodes) {
            // 查找视频配置节点
            const videoConfigNode = Object.values(contentData.configJson.nodes).find(node => node.type === 'videoConfig');
            if (videoConfigNode && videoConfigNode.params) {
              console.log('找到视频配置节点:', videoConfigNode);
              // 更新视频配置信息
              if (videoConfigNode.params.cover) {
                videoConfig.value.cover = { ...videoConfigNode.params.cover };
              }
              if (videoConfigNode.params.copyright) {
                videoConfig.value.copyright = { ...videoConfigNode.params.copyright };
              }
              console.log('更新视频配置信息:', videoConfig.value);
            }
          }
        } catch (parseError) {
          console.error('解析/解密configJson失败:', parseError);
          ElMessage.error('解析内容配置失败');
        }
      }

      // 所有数据处理完成后，才设置到响应式变量
      content.value = contentData;
      console.log('1. 内容数据已设置到响应式变量');

      // 1. 生成服务器配置（从内容数据生成）
      console.log('1. 生成服务器配置...');
      generateServerConfig();

      // 2-3. 智能加载配置（内部包含模板加载和配置加载）
      await loadAndApplyConfiguration();

      // 4. 处理音频
      console.log('4. 开始处理音频...');
      await processAudioFiles();

    } else {
      console.error('API响应格式不正确:', response);
      ElMessage.error('获取内容数据失败');
      content.value = null;
    }

    // 加载完成后，isLoading状态已在processAudioFiles中设置为false
  } catch (error) {
    console.error('加载内容失败:', error);
    ElMessage.error('加载内容失败，请稍后重试');
    isLoading.value = false;
  }
};

// 生成服务器配置（从内容数据生成基础环节结构）
const generateServerConfig = () => {
  if (!content.value || !content.value.configJson) {
    console.error('无法生成服务器配置，内容数据格式不正确:', content.value);
    serverConfig.value = { sections: [] };
    return;
  }

  const sections = [];

  // 从节点中提取序列数据
  const sequences = extractSequencesFromNodes(content.value.configJson);

  // 检查是否有文本序列节点
  const textSequenceNodes = Object.values(content.value.configJson.nodes || {})
    .filter(node => node.type === 'textSequence');

  // 如果有文本序列节点，检查它们的环节设置
  if (textSequenceNodes.length > 0) {
    const firstSequenceNode = textSequenceNodes[0];

    // 检查节点是否有环节设置
    if (firstSequenceNode.params && firstSequenceNode.params.sections && firstSequenceNode.params.sections.length > 0) {
      console.log('使用节点中的环节设置:', firstSequenceNode.params.sections);

      // 使用节点中的完整环节设置，保留所有字段
      firstSequenceNode.params.sections.forEach((section, index) => {
        // 直接使用完整的环节配置，不丢弃任何字段
        const newSection = {
          ...section, // 保留所有原有字段
          id: section.id || `section_default_${Date.now() + index}`,
          title: section.title || `环节 ${index + 1}`,
          description: section.description || '',
          processingMode: section.processingMode || (section.sourceNodeId ? 'source' : 'sequence'),
          userEditable: section.userEditable !== false // 默认为true，除非明确设置为false
        };

        // 根据处理模式设置正确的属性
        if (newSection.processingMode === 'sequence') {
          newSection.sourceIndex = newSection.sourceIndex ?? 0; // 默认使用第一个序列
        } else if (newSection.processingMode === 'source') {
          // 如果是基于源节点，设置源节点ID
          if (section.sourceNodeIds && section.sourceNodeIds.length > 0) {
            newSection.sourceNodeIds = [...section.sourceNodeIds];
          } else if (section.sourceNodeId) {
            newSection.sourceNodeIds = [section.sourceNodeId];
          }
        }

        sections.push(newSection);
      });
    } else {
      console.log('节点中没有环节设置，创建默认环节');

      // 只创建一个默认的通读环节（基于序列）
      if (sequences.length > 0) {
        sections.push({
          id: `section_seq_${Date.now()}`,
          title: `环节: 基于序列`,
          description: '默认环节',
          processingMode: 'sequence',
          sourceIndex: 0, // 使用第一个序列
          userEditable: true
        });
      } else {
        // 如果没有序列，尝试创建基于源节点的通读环节
        const textContentNodes = Object.values(content.value.configJson.nodes || {})
          .filter(node => node.type === 'textContent');

        if (textContentNodes.length > 0) {
          sections.push({
            id: `section_src_${Date.now()}`,
            title: `环节: 基于源节点`,
            description: '默认环节',
            processingMode: 'source',
            sourceNodeIds: [textContentNodes[0].id],
            userEditable: true
          });
        }
      }
    }
  } else {
    console.log('未找到文本序列节点，创建默认环节');

    // 创建通读环节（基于序列）
    if (sequences.length > 0) {
      sections.push({
        id: `section_seq_${Date.now()}`,
        title: `环节: 基于序列`,
        description: '默认环节',
        processingMode: 'sequence',
        sourceIndex: 0, // 使用第一个序列
        userEditable: true
      });
    } else {
      // 如果没有序列，尝试创建基于源节点的通读环节
      const textContentNodes = Object.values(content.value.configJson.nodes || {})
        .filter(node => node.type === 'textContent');

      if (textContentNodes.length > 0) {
        sections.push({
          id: `section_src_${Date.now()}`,
          title: `环节: 基于源节点`,
          description: '默认环节',
          processingMode: 'source',
          sourceNodeIds: [textContentNodes[0].id],
          userEditable: true
        });
      }
    }
  }

  // 确保至少有一个环节
  if (sections.length === 0) {
    console.log('没有找到任何有效的环节配置，创建最基本的默认环节');
    sections.push({
      id: `section_fallback_${Date.now()}`,
      title: '默认播放',
      description: '基础播放环节',
      processingMode: 'sequence',
      sourceIndex: 0,
      userEditable: true
    });
  }

  serverConfig.value = {
    sections
  };

  console.log('生成的服务器配置:', serverConfig.value);
};

// 智能加载配置（新的核心逻辑）
const loadAndApplyConfiguration = async () => {
  try {
    console.log('开始智能加载配置...');

    // 2. 在有了内容数据和服务器配置后，加载模板数据
    console.log('加载模板数据...');
    if (templateStore.systemTemplates.length === 0) {
      await templateStore.loadTemplates();
    }

    // 3. 加载播放配置（基于服务器配置和模板数据）
    console.log('加载播放配置...');
    const success = await configManager.smartLoadConfig();

    if (success) {
      generateTimelineFromSettings();
    } else {
      console.log('配置加载被跳过，等待内容加载完成');
      // 如果配置加载失败（通常是因为serverConfig未准备好），使用默认配置
      if (serverConfig.value && serverConfig.value.sections && serverConfig.value.sections.length > 0) {
        configManager.applyDefaultConfig();
        generateTimelineFromSettings();
      }
    }

  } catch (error) {
    console.error('智能加载配置失败:', error);
    ElMessage.error('加载播放策略失败，使用默认配置');

    // 只有在有服务器配置时才应用默认配置
    if (serverConfig.value && serverConfig.value.sections && serverConfig.value.sections.length > 0) {
      configManager.applyDefaultConfig();
      generateTimelineFromSettings();
    }
  }
};

// 应用模板（统一逻辑）
const applyTemplate = async (template) => {
  try {
    console.log('开始应用模板:', template.name);

    const success = await configManager.applyTemplate(template);
    if (success) {
      // 应用模板只是保存配置，不重新生成时间线
      // 时间线的重新生成在用户点击"应用设置"时进行
      console.log('模板应用成功:', template.name);
      return true;
    } else {
      throw new Error('应用模板失败');
    }
  } catch (error) {
    console.error('应用模板失败:', error);
    ElMessage.error('应用模板失败，使用默认配置');
    configManager.applyDefaultConfig();
    return false;
  }
};



// 生成时间线
const generateTimelineFromSettings = () => {
  if (content.value && content.value.configJson) {
    timeline.value = generateTimeline(content.value.configJson, settings.value);

    // 检查是否生成了时间线
    if (timeline.value.length === 0) {
      console.error('未能生成时间线，请检查内容配置');
      ElMessage.error('未找到有效的序列数据，请确保内容中包含文本序列节点');
    }
  } else {
    console.error('内容数据格式不正确:', content.value);
    ElMessage.error('内容数据格式不正确');
    timeline.value = [];
  }
};

// 处理音频文件
const processAudioFiles = async () => {
  try {
    // 确保loading状态为true，由VideoPlayer组件显示loading
    isLoading.value = true;

    // 收集所有音频URL
    const audioUrls = collectAllAudioUrls(timeline.value, content.value);

    // 验证音频URL
    const validation = validateAudioUrls(audioUrls);
    if (!validation.isValid) {
      ElMessage.error(validation.message);
      isLoading.value = false;
      progressManager.setError();
      return;
    }

    console.log(validation.message);

    // 初始化进度状态
    const audioItems = timeline.value.filter(item => !item.isCover);
    const totalItems = audioItems.length || 1;
    progressManager.initialize(totalItems);

    // 清空当前音频缓冲区并确保UI更新
    audioBuffer.value = null;
    await nextTick();

    // 创建进度回调函数
    const progressCallback = progressManager.createCallback('loading');

    // 处理音频
    progressManager.setPercentage(90);
    const processedBuffer = await processAudio(audioUrls, timeline.value, settings.value, progressCallback);

    // 最终处理阶段
    progressManager.setPercentage(95);
    audioBuffer.value = processedBuffer;

    // 等待一小段时间确保音频缓冲区已被处理
    await new Promise(resolve => setTimeout(resolve, 100));

    // 完成处理
    progressManager.complete();
    isLoading.value = false;

  } catch (error) {
    console.error('处理音频失败:', error);
    ElMessage.error('处理音频失败，请稍后重试');

    // 重置所有状态
    progressManager.setError();
    isLoading.value = false;
  }
};

// 处理设置保存（来自MobileSettingsPanel的save事件）
const saveSettings = async (newSettings) => {
  try {
    // 验证和优化配置
    const optimizedConfig = configManager.validateAndOptimizeConfig(newSettings);
    if (!optimizedConfig) {
      ElMessage.error('设置格式不正确或验证失败');
      return;
    }

    // 检查配置是否真的发生了变化
    const hasActualChanges = !comparePlaybackConfigs(
      configManager.settings.value,
      newSettings
    );

    // 更新设置
    configManager.settings.value = optimizedConfig;

    // 只有在配置真的发生变化时才更新模板状态
    if (hasActualChanges) {
      configManager.updateTemplateState();
    }

    // 保存到localStorage
    configManager.saveToStorage();

    console.log('设置已保存:', optimizedConfig);
  } catch (error) {
    console.error('保存设置失败:', error);
    ElMessage.error('保存设置失败');
  }
};

// 处理设置保存（来自底部按钮）
const handleSettingsSave = async () => {
  if (settingsPanelRef.value) {
    try {
      // 保存设置
      await settingsPanelRef.value.saveSettings();

      // 如果有当前模板，确保模板状态被正确保存
      if (currentTemplate.value) {
        const keys = configManager.storageKeys.value;
        localStorage.setItem(keys.template, currentTemplate.value.id);
      }

      // 重新生成时间线
      generateTimelineFromSettings();

      // 重新处理音频
      await processAudioFiles();

      // 关闭设置面板
      showSettings.value = false;

      ElMessage.success('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      ElMessage.error('保存设置失败');
    }
  }
};

// 处理模板状态检测（实时检测，不保存设置）
const handleTemplateStateCheck = (newSettings) => {
  // 只有在有当前模板时才进行检测
  if (!currentTemplate.value) return;

  try {
    const isMatch = configManager.isSettingsMatchTemplate(currentTemplate.value, newSettings);
    if (!isMatch) {
      console.log('用户修改设置与模板不匹配，清除模板状态');
      currentTemplate.value = null;
      const keys = configManager.storageKeys.value;
      localStorage.removeItem(keys.template);

      // 通知用户
      ElMessage.warning('已清除模板状态，当前使用自定义设置');
    }
  } catch (error) {
    console.error('模板状态检测失败:', error);
  }
};

// 切换设置面板
const toggleSettings = () => {
  showSettings.value = !showSettings.value;
};

// 处理重置为已保存配置
const handleResetToSaved = async () => {
  try {
    // 重新加载保存的配置
    await configManager.smartLoadConfig();
    console.log('已重置为保存的配置');
  } catch (error) {
    console.error('重置配置失败:', error);
  }
};

// 监听设置面板打开状态，每次打开时重置草稿
watch(showSettings, (isOpen) => {
  if (isOpen && settingsPanelRef.value) {
    // 每次打开设置面板时，重置为真正保存到localStorage的配置
    nextTick(() => {
      settingsPanelRef.value.resetToSavedConfig?.();
    });
  }
});

// 处理设置重置（重新应用当前模板或默认配置）
const handleSettingsReset = async () => {
  try {
    if (currentTemplate.value) {
      // 如果有当前模板，重新应用模板
      const success = await applyTemplate(currentTemplate.value);
      if (success) {
        ElMessage.success('已重置为模板配置');
      }
    } else {
      // 如果没有模板，重新应用默认配置
      configManager.applyDefaultConfig();
      configManager.saveToStorage(); // 确保保存默认配置
      generateTimelineFromSettings();

      // 重新处理音频
      processAudioFiles().catch(error => {
        console.error('重新处理音频失败:', error);
      });

      ElMessage.success('已重置为默认配置');
    }
  } catch (error) {
    console.error('重置设置失败:', error);
    ElMessage.error('重置设置失败');
  }
};

// 选择模板（统一逻辑）
const selectTemplate = async (template) => {
  try {
    const success = await applyTemplate(template);
    if (success) {
      ElMessage.success(`已应用模板：${template.name}，点击"应用设置"保存`);
    }
  } catch (error) {
    console.error('选择模板失败:', error);
    ElMessage.error('应用模板失败');
  }
};

// 返回首页
const goToHomePage = () => {
  router.replace('/');
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再收藏内容');
    router.push('/login');
    return;
  }

  try {
    const result = await favoriteStore.toggleFavorite(contentId.value, content.value);
    if (result.success) {
      if (favoriteStore.isFavorite(contentId.value)) {
        ElMessage.success('收藏成功');
      } else {
        ElMessage.success('已取消收藏');
      }
    } else {
      ElMessage.error(result.error || '操作失败');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 切换上下文面板
const toggleContextPanel = () => {
  showContextPanel.value = !showContextPanel.value;

  // 如果有布局组件引用，同步状态
  if (layoutRef.value && layoutRef.value.videoPlayerRef) {
    layoutRef.value.videoPlayerRef.uiState.showPlaylist = showContextPanel.value;
  }
};

// 跳转到指定索引 - 只更新索引，实际跳转由 VideoPlayer 组件处理
const jumpToIndex = (index) => {
  if (index >= 0 && index < timeline.value.length) {
    // 只更新索引，实际跳转由 VideoPlayer 组件通过 watch 监听处理
    currentIndex.value = index;
  }
};

// 处理加载状态变化
const handleLoadingChange = (loading) => {
  // 更新加载状态
  isLoading.value = loading;
};

// 键盘事件处理器
let keyboardHandler = null;

// 进度管理器
const progressManager = createProgressManager(audioSynthesisProgress);

// 键盘快捷键处理（使用统一的键盘处理器）
const handleKeyDown = (event) => {
  if (keyboardHandler) {
    keyboardHandler.handleKeyDown(event);
  }
};





// 立即开始加载内容（不等待组件挂载）
const initializeContent = async () => {
  // 1. 首先加载内容数据（获取视频基础信息）
  await loadContent();
};

// 立即开始初始化
initializeContent();

// 生命周期钩子
onMounted(async () => {
  // 加载用户文本样式配置
  loadUserTextStyleConfig();

  // 初始化键盘处理器
  keyboardHandler = new KeyboardHandler(
    getVideoPlayer,
    currentIndex,
    timeline,
    (newIndex) => {
      currentIndex.value = newIndex;
    }
  );

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown);

  // 检查收藏状态
  if (userStore.isLoggedIn && contentId.value) {
    favoriteStore.checkFavorite(contentId.value);
  }

  // 跟踪视频播放页面访问
  trackEvent(EVENTS.VIDEO_PLAYED, {
    contentId: contentId.value,
    device: isMobile.value ? 'mobile' : 'desktop'
  });
});

onUnmounted(() => {
  // 清理键盘处理器
  if (keyboardHandler) {
    keyboardHandler.cleanup();
    keyboardHandler = null;
  }

  // 移除事件监听
  window.removeEventListener('keydown', handleKeyDown);
});

// 监听路由变化
watch(() => route.params.id, (newId, oldId) => {
  console.log('路由参数变化:', { newId, oldId });
  if (newId !== oldId) {
    loadContent();
  }
});
</script>

<template>
  <div class="player-page" :class="{ 'mobile-device': isMobile }">
    <!-- 调试信息 -->
    <!-- content: {{ content ? 'exists' : 'null' }}, configJson: {{ content?.configJson ? 'exists' : 'null' }} -->

    <!-- 只有在内容数据准备好之后才渲染播放器组件 -->
    <template v-if="content && content.configJson">
      <!-- 桌面端布局 -->
      <DesktopPlayerLayout v-if="!isMobile" ref="layoutRef" :timeline="timeline" :audioBuffer="audioBuffer"
        :currentIndex="currentIndex" :isLoading="isLoading" :showContextPanel="showContextPanel"
        :videoConfig="videoConfig" :configJson="content.configJson" :contentData="content"
        :audioSynthesisProgress="audioSynthesisProgress" @index-change="currentIndex = $event"
        @loading-change="handleLoadingChange" @open-settings="toggleSettings" @toggle-playlist="toggleContextPanel"
        @select-item="jumpToIndex" @go-home="goToHomePage">
        <!-- 收藏按钮 -->
        <template #actions>
          <FavoriteButton :content-id="contentId" :content-data="content" :in-player-controls="true" />
        </template>
      </DesktopPlayerLayout>

      <!-- 移动端布局 -->
      <MobilePlayerLayout v-else ref="layoutRef" :timeline="timeline" :audioBuffer="audioBuffer"
        :currentIndex="currentIndex" :isLoading="isLoading" :showContextPanel="showContextPanel"
        :videoConfig="videoConfig" :audioSynthesisProgress="audioSynthesisProgress"
        @index-change="currentIndex = $event" @loading-change="handleLoadingChange" @open-settings="toggleSettings"
        @toggle-playlist="toggleContextPanel" @select-item="jumpToIndex" @go-home="goToHomePage">
        <!-- 收藏按钮 -->
        <template #actions>
          <FavoriteButton :content-id="contentId" :content-data="content" size="small" :in-player-controls="true" />
        </template>
      </MobilePlayerLayout>
    </template>

    <!-- 内容加载中的占位符 -->
    <div v-else class="loading-placeholder" v-loading="true" element-loading-text="正在加载内容...">
    </div>

    <!-- 统一使用抽屉组件作为设置面板 -->
    <el-drawer v-model="showSettings" :direction="isMobile ? 'btt' : 'rtl'" :size="isMobile ? '75%' : '70%'"
      :with-header="false" class="settings-drawer">
      <div class="drawer-header">
        <h3>播放策略设置</h3>
        <el-button link @click="showSettings = false">关闭</el-button>
      </div>
      <div class="drawer-content">
        <PlaybackSettingsPanel ref="settingsPanelRef" :settings="settings" :server-config="serverConfig"
          :current-template="currentTemplate" :content="content" @save="saveSettings" @select-template="selectTemplate"
          @check-template-state="handleTemplateStateCheck" @reset-to-saved="handleResetToSaved" />
      </div>
      <div class="drawer-footer">
        <div class="footer-buttons">
          <el-button @click="handleSettingsReset" type="warning">重置</el-button>
          <el-button type="primary" @click="handleSettingsSave">应用设置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped>
.player-page {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #f0f2f5;
}

.loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #000;
}

/* 自定义 Element Plus 按钮样式 */
:deep(.el-button) {
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button:hover) {
  transform: translateY(-0.125rem);
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__header) {
  margin-bottom: 0;
  padding: 1rem;
  border-bottom: 1px solid #e6e6e6;
}

:deep(.el-dialog__body) {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}

/* 移动设备样式 - 不使用媒体查询 */
.mobile-device :deep(.el-dialog) {
  width: 95% !important;
  margin-top: 1rem !important;
}

.mobile-device :deep(.el-dialog__body) {
  max-height: 80vh;
  padding: 0;
}

.mobile-device :deep(.el-dialog__footer) {
  padding: 0.5rem;
}

.mobile-device :deep(.el-dialog__footer .el-button) {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

/* 统一抽屉式设置面板样式 */
:deep(.settings-drawer .el-drawer) {
  overflow: hidden;
}

:deep(.settings-drawer .el-drawer__body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  /* 确保抽屉内容不超出视口高度 */
}

/* 移动端特有样式 */
:deep(.settings-drawer .el-drawer[rtl="false"]) {
  border-radius: 0.75rem 0.75rem 0 0;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 0.0625rem solid #ebeef5;
  background-color: #ffffff;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.drawer-content {
  flex: 1;
  overflow-y: auto;
  height: calc(100% - 6rem);
  /* 减去头部和底部的高度 */
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.75rem 1rem;
  border-top: 0.0625rem solid #ebeef5;
  background-color: #fff;
}

.footer-buttons {
  display: flex;
  gap: 0.5rem;
}

/* 设置对话框样式 */
.settings-dialog {
  display: flex;
}

.settings-dialog :deep(.el-dialog__header) {
  background-color: #f5f7fa;
}

.settings-dialog :deep(.el-dialog__headerbtn) {
  top: 1rem;
}
</style>
