<template>
    <div class="mobile-tip">
        <div class="tip-container">
            <el-icon class="tip-icon" :size="48">
                <Monitor />
            </el-icon>
            <h2>请使用电脑访问</h2>
            <p>此功能需要在电脑端使用，移动设备暂不支持内容编辑、模板管理等复杂操作。</p>
            <p class="sub-tip">请使用桌面电脑或笔记本电脑访问完整功能。</p>
            <el-button type="primary" @click="goHome">返回首页</el-button>
        </div>
    </div>
</template>

<script setup>
import { Monitor } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
    router.push('/')
}
</script>

<style scoped>
.mobile-tip {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    background-color: #f5f7fa;
}

.tip-container {
    text-align: center;
    padding: 2rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tip-icon {
    color: #409eff;
    margin-bottom: 1rem;
}

h2 {
    margin: 1rem 0;
    color: #303133;
}

p {
    color: #606266;
    margin-bottom: 1rem;
}

.sub-tip {
    font-size: 0.875rem;
    color: #909399;
    margin-bottom: 1.5rem;
}
</style>