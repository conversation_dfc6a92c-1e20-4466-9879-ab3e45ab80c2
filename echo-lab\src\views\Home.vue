<!--
  首页视图
  展示公开内容并提供导航到编辑器和其他功能的入口
-->
<template>
  <div class="home-view">
    <!-- 左侧侧边栏 -->
    <div class="home-sidebar" :class="{ 'visible': sidebarVisible }">
      <HomeSidebar />
    </div>

    <!-- 主内容区域 -->
    <div class="home-content">
      <!-- 顶部导航栏 -->
      <div class="home-header">
        <!-- 左侧侧边栏按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon>
            <Menu />
          </el-icon>
        </div>

        <!-- 右侧标题和logo -->
        <div class="header-right">
          <img src="../assets/logo.jpg" alt="Echo Lab" class="header-logo" />
          <h1 class="header-title">Echo Lab</h1>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="home-main">
        <!-- 公告区域 -->
        <div class="announcement-section" v-if="showAnnouncement">
          <div class="announcement-bar">
            <div class="announcement-left">
              <el-icon class="announcement-icon">
                <Notification />
              </el-icon>
              <span class="announcement-label">公告</span>
            </div>

            <div class="announcement-center" @click="handleLearnMore" style="cursor: pointer;">
              <TextMarquee text="视频内容使用 AI 辅助生成文本和 TTS 合成音频，在语音表达上可能有所欠缺，建议作为学习辅助工具使用。" :speed="3" />
            </div>

            <div class="announcement-right">
              <el-button type="primary" link @click="closeAnnouncement" class="close-btn">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
          </div>


        </div>

        <!-- 快速操作区域 - 只有拥有权限的用户才能看到，且只在桌面端显示 -->
        <div class="quick-actions-section" v-if="isLoggedIn && hasContentCreationPermission && !isMobile">
          <div class="quick-actions-container">
            <!-- 内容管理入口 -->
            <div class="quick-action-card">
              <div class="quick-action-icon">
                <el-icon>
                  <Document />
                </el-icon>
              </div>
              <div class="quick-action-content">
                <h3>内容管理</h3>
                <p>管理您创建的所有内容</p>
              </div>
              <div class="quick-action-footer">
                <el-button type="primary" @click="goToContent">
                  进入管理
                </el-button>
              </div>
            </div>

            <!-- 编辑器入口 -->
            <div class="quick-action-card">
              <div class="quick-action-icon">
                <el-icon>
                  <Edit />
                </el-icon>
              </div>
              <div class="quick-action-content">
                <h3>编辑器</h3>
                <p>创建新的精听练习内容</p>
              </div>
              <div class="quick-action-footer">
                <el-button type="primary" @click="goToEditor">
                  开始创建
                </el-button>
              </div>
            </div>
          </div>
        </div>



        <!-- 所有内容区域 -->
        <div class="content-section" v-if="!isSearchMode">
          <div class="section-header">
            <h2>所有内容</h2>
          </div>

          <ContentGrid :contents="publicContentStore.contents" :loading="publicContentStore.loading"
            :pagination="publicContentStore.pagination" :show-pagination="false" empty-text="暂无内容"
            @pagination-change="handlePaginationChange" />
        </div>
      </div>
    </div>

    <!-- 遮罩层 - 点击关闭侧边栏 -->
    <div class="sidebar-overlay" v-if="sidebarVisible" @click="toggleSidebar"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { Edit, Document, Menu, Close, Notification } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { usePublicContentStore } from '@/core/stores/publicContentStore';
import { useUserStore } from '@/stores/userStore';
import ContentGrid from '@/components/content/ContentGrid.vue';
import HomeSidebar from '@/components/common/HomeSidebar.vue';
import TextMarquee from '@/components/common/TextMarquee.vue';
import { isMobileDevice } from '@/utils/deviceDetector';
import { ElMessage } from 'element-plus';
import { trackEvent } from '@/utils/analytics';
import { checkFeaturePermission } from '@/services/featurePermissionService';
import { useSEO, PAGE_SEO_CONFIG } from '@/composables/useSEO';

// 确保图标组件被使用（解决ESLint警告）
const _icons = { Edit, Document, Menu, Close, Notification };

// 初始化SEO
const { updateSEO } = useSEO(PAGE_SEO_CONFIG.home);

// 侧边栏状态
const sidebarVisible = ref(false);

// 切换侧边栏显示状态
function toggleSidebar() {
  sidebarVisible.value = !sidebarVisible.value;
}

const router = useRouter();
const publicContentStore = usePublicContentStore();
const userStore = useUserStore();

// 搜索功能已移除
const isSearchMode = computed(() => false);

// 设备检测
const isMobile = computed(() => isMobileDevice());

// 用户是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn);

// 权限检查状态
const hasContentCreationPermission = ref(false);

// 公告相关状态
const showAnnouncement = ref(false);

// 关闭公告
function closeAnnouncement() {
  showAnnouncement.value = false;
}

// 检查是否需要显示公告
function checkAnnouncementStatus() {
  // 默认显示公告
  showAnnouncement.value = true;
}

// 开发环境检测已移除

// 导航函数
async function goToEditor() {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再访问编辑器');
    router.push('/login?redirect=' + encodeURIComponent('/editor'));
    return;
  }

  try {
    const hasPermission = await checkFeaturePermission('content_creation');
    if (hasPermission) {
      window.open('/editor', '_blank');
    } else {
      ElMessage.warning('您没有内容创建与管理权限，请升级会员等级');
      router.push('/upgrade?redirect=' + encodeURIComponent('/editor'));
    }
  } catch (error) {
    console.error('检查权限失败:', error);
    ElMessage.error('检查权限失败，请稍后再试');
  }
}

async function goToContent() {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再访问内容管理');
    router.push('/login?redirect=' + encodeURIComponent('/content'));
    return;
  }

  try {
    const hasPermission = await checkFeaturePermission('content_creation');
    if (hasPermission) {
      window.open('/content', '_blank');
    } else {
      ElMessage.warning('您没有内容创建与管理权限，请升级会员等级');
      router.push('/upgrade?redirect=' + encodeURIComponent('/content'));
    }
  } catch (error) {
    console.error('检查权限失败:', error);
    ElMessage.error('检查权限失败，请稍后再试');
  }
}

// 搜索功能已移除

// 处理"了解更多"按钮点击
function handleLearnMore() {
  // 无论设备类型，都直接跳转到内容信息页面
  router.push({ name: 'ContentInfo' });
}

// 跳转到升级页面
function goToUpgrade(redirect) {
  router.push(`/upgrade?redirect=${encodeURIComponent(redirect)}`);
}

// 查看更多功能已移除

// 处理分页变化
function handlePaginationChange(pagination) {
  publicContentStore.updatePagination(pagination);
}

// 加载初始数据
function loadInitialData() {
  publicContentStore.fetchPublicContents();
}

// 组件挂载时加载数据
onMounted(async () => {
  loadInitialData();

  // 检查权限
  if (userStore.isLoggedIn) {
    try {
      hasContentCreationPermission.value = await checkFeaturePermission('content_creation');
    } catch (error) {
      console.error('检查内容创建权限失败:', error);
    }
  }

  // 检查是否需要显示公告
  checkAnnouncementStatus();

  // 跟踪首页访问
  trackEvent('home_page_visit', {
    device: isMobile.value ? 'mobile' : 'desktop',
    isLoggedIn: userStore.isLoggedIn
  });
});
</script>

<style scoped>
.home-view {
  min-height: 100%;
  background-color: #f5f7fa;
  display: flex;
  position: relative;
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  width: 2.25rem;
  height: 2.25rem;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 95;
}

.sidebar-toggle .el-icon {
  font-size: 1.125rem;
  color: #606266;
}

/* 侧边栏样式 */
.home-sidebar {
  width: 0;
  background-color: #ffffff;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  transition: width 0.3s;
  overflow: hidden;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.home-sidebar.visible {
  width: 16rem;
}

/* 遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

/* 主内容区域容器 */
.home-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  margin-left: 0;
  transition: margin-left 0.3s;
  position: relative;
  /* 添加相对定位，为子元素的绝对定位提供参考 */
  width: 100%;
}

/* 顶部导航栏 */
.home-header {
  background-color: #ffffff;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  box-sizing: border-box;
  height: auto;
}

.header-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.header-logo {
  width: 2.25rem;
  height: 2.25rem;
}

.header-title {
  font-size: 1.25rem;
  color: #303133;
  margin: 0 0 0 0.5rem;
}

/* 主要内容区域 */
.home-main {
  flex: 1;
  padding: 1rem;
  max-width: 75rem;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
  padding-top: 4rem;
  /* 为固定的header留出空间 */
}

/* 公告区域 */
.announcement-section {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.announcement-bar {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  min-height: 2.5rem;
}

.announcement-left {
  display: flex;
  align-items: center;
  min-width: 3rem;
}

.announcement-icon {
  font-size: 1.25rem;
  color: #409EFF;
  margin-right: 0.25rem;
}

.announcement-label {
  font-weight: bold;
  color: #303133;
}

.announcement-center {
  flex: 1;
  overflow: hidden;
  margin: 0 0.5rem;
}

.announcement-right {
  display: flex;
  align-items: center;
}



.close-btn {
  padding: 0.25rem;
}

/* 内容说明区域 */
.info-section {
  margin-bottom: 2rem;
}

.info-content {
  padding: 0.5rem 0;
}

.info-content p {
  margin: 0.5rem 0;
}

.info-content ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.info-content li {
  margin-bottom: 0.5rem;
  word-break: break-word;
  /* 确保长文本正确换行 */
}

.info-note {
  font-style: italic;
  color: #606266;
}

.info-actions {
  margin-top: 1rem;
  text-align: right;
}

/* 快速操作区域 */
.quick-actions-section {
  margin-top: 1rem;
}

.quick-actions-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.quick-action-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.quick-action-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.12);
}

.quick-action-icon {
  margin-bottom: 1rem;
  font-size: 2rem;
  color: #409eff;
}

.quick-action-content {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.quick-action-content h3 {
  font-size: 1.25rem;
  margin: 0 0 0.5rem 0;
  color: #303133;
}

.quick-action-content p {
  margin: 0;
  color: #606266;
  font-size: 0.875rem;
}

.quick-action-footer {
  display: flex;
  justify-content: flex-end;
}



/* 内容区域 */
.content-section {
  margin-top: 1rem;
  margin-bottom: 3rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #303133;
}

/* 内容信息对话框样式已移除 */
</style>
