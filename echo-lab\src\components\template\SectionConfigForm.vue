<template>
  <div class="section-config-form">
    <!-- 环节标题 -->
    <div class="settings-section">
      <div class="settings-section-title">环节标题</div>
      <div class="form-section-row">
        <div class="setting-item">
          <div class="setting-label">标题名称</div>
          <el-input v-model="localSection.title" placeholder="请输入环节标题" maxlength="50" show-word-limit
            class="setting-control" />
          <div class="form-help">给这个环节起一个有意义的名称，方便识别和管理</div>
        </div>
      </div>
    </div>

    <!-- 播放设置 -->
    <div class="settings-section">
      <div class="settings-section-title">播放设置</div>
      <div class="form-section-row">
        <div class="setting-item">
          <div class="setting-label">语速</div>
          <NumberEditor v-model="localSection.speed" :min="0.9" :max="1.1" :step="0.1" :precision="1" suffix="x"
            class="setting-control" @change="updateRepeatSpeeds" />
        </div>

        <div class="setting-item">
          <div class="setting-label">停顿时长</div>
          <NumberEditor v-model="localSection.pauseDuration" :min="0" :max="5000" :step="100" suffix="ms"
            class="setting-control" @change="updateRepeatPauses" />
        </div>
      </div>
    </div>

    <!-- 重复设置 -->
    <div class="settings-section">
      <div class="settings-section-title">重复设置</div>
      <div class="form-section-row">
        <div class="setting-item">
          <div class="setting-label">重复次数</div>
          <NumberEditor v-model="localSection.repeatCount" :min="1" :max="10" :step="1" class="setting-control"
            @change="updateRepeatArrays" />
        </div>
      </div>

      <div class="section-subtitle" style="margin-top: 1rem;">
        <div class="title-with-tooltip">
          <span class="setting-label">播放流程预览</span>
          <el-tooltip content="查看实际的播放顺序和参数设置" placement="top">
            <el-icon class="question-icon">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
      </div>

      <div class="playback-flow">
        <div class="flow-timeline">
          <div v-for="(_, index) in Array(localSection.repeatCount)" :key="index" class="flow-step">
            <!-- 播放步骤 -->
            <div class="step-content">
              <div class="step-header">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-title">播放原文</div>
              </div>

              <div class="step-params">
                <div class="param-item">
                  <span class="param-label">速度</span>
                  <NumberEditor v-model="localSection.repeatSpeeds[index]" :min="0.9" :max="1.1" :step="0.1"
                    :precision="1" suffix="x" class="param-input" />
                </div>
                <div class="param-item">
                  <span class="param-label">停顿</span>
                  <NumberEditor v-model="localSection.repeatPauses[index]" :min="0" :max="5000" :step="100" suffix="ms"
                    class="param-input" />
                </div>
              </div>
            </div>

            <!-- 插入内容提示 -->
            <div v-if="shouldShowInsert(index + 1)" class="insert-content">
              <div v-if="localSection.enableTranslation && localSection.translationPosition === index + 1"
                class="insert-item translation">
                <div class="insert-icon">🌐</div>
                <div class="insert-text">插入翻译</div>
              </div>
              <div v-if="localSection.enableKeywords && localSection.keywordPosition === index + 1"
                class="insert-item keyword">
                <div class="insert-icon">🔑</div>
                <div class="insert-text">插入关键词 ({{ localSection.keywordRepeatCount }}次)</div>
              </div>
            </div>

            <!-- 连接线 -->
            <div v-if="index < localSection.repeatCount - 1" class="flow-connector">
              <div class="connector-line"></div>
              <div class="connector-arrow">→</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 翻译设置 -->
    <div class="settings-section">
      <div class="section-title-with-switch">
        <div class="title-with-tooltip">
          <span class="settings-section-title">翻译设置</span>
          <el-tooltip content="在重复播放中插入翻译音频" placement="top">
            <el-icon class="question-icon">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <el-switch v-model="localSection.enableTranslation" />
      </div>

      <template v-if="localSection.enableTranslation">
        <div class="form-section-row">
          <div class="setting-item">
            <div class="setting-label">翻译语言</div>
            <el-select v-model="localSection.translationLanguage" placeholder="请选择翻译语言" class="setting-control">
              <el-option v-for="lang in SUPPORTED_LANGUAGES" :key="lang.value" :label="lang.label"
                :value="lang.value" />
            </el-select>
          </div>
        </div>

        <div class="form-section-row">
          <div class="setting-item">
            <div class="setting-label">插入位置</div>
            <NumberEditor v-model="localSection.translationPosition" :min="1" :max="localSection.repeatCount || 4"
              :step="1" class="setting-control" :disabled="localSection.repeatCount === 1" />
            <div class="form-help" v-if="localSection.repeatCount > 1">
              在第几次重复后插入翻译（1-{{ localSection.repeatCount || 4 }}）
            </div>
            <div class="form-help" v-else>
              重复次数为1时，翻译将在内容播放后立即插入
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 关键词设置 -->
    <div class="settings-section">
      <div class="section-title-with-switch">
        <div class="title-with-tooltip">
          <span class="settings-section-title">关键词设置</span>
          <el-tooltip content="在重复播放中插入关键词音频" placement="top">
            <el-icon class="question-icon">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <el-switch v-model="localSection.enableKeywords" />
      </div>

      <template v-if="localSection.enableKeywords">
        <div class="form-section-row">
          <div class="setting-item">
            <div class="setting-label">插入位置</div>
            <NumberEditor v-model="localSection.keywordPosition" :min="1" :max="localSection.repeatCount || 4" :step="1"
              class="setting-control" :disabled="localSection.repeatCount === 1" />
            <div class="form-help" v-if="localSection.repeatCount > 1">
              在第几次重复后插入关键词（1-{{ localSection.repeatCount || 4 }}）
            </div>
            <div class="form-help" v-else>
              重复次数为1时，关键词将在内容播放后立即插入
            </div>
          </div>
        </div>

        <div class="form-section-row">
          <div class="setting-item">
            <div class="setting-label">重复次数</div>
            <NumberEditor v-model="localSection.keywordRepeatCount" :min="1" :max="5" :step="1"
              class="setting-control" />
          </div>
        </div>

        <div class="form-section-row">
          <div class="setting-item">
            <div class="setting-label">语速</div>
            <NumberEditor v-model="localSection.keywordSpeed" :min="0.9" :max="1.1" :step="0.1" :precision="1"
              suffix="x" class="setting-control" />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { QuestionFilled } from '@element-plus/icons-vue';
import NumberEditor from '../common/NumberEditor.vue';
import { SUPPORTED_LANGUAGES } from '@/config/languages';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// 本地编辑状态
const localSection = ref({ ...props.modelValue });

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
  localSection.value = { ...newValue };
  updateRepeatArrays();
}, { deep: true });

// 监听本地变化并向外发送
watch(localSection, (newValue) => {
  emit('update:modelValue', { ...newValue });
}, { deep: true });

// 更新重复速度数组
const updateRepeatSpeeds = () => {
  const count = localSection.value.repeatCount || 4;
  const defaultSpeed = localSection.value.speed || 1.0;

  if (!Array.isArray(localSection.value.repeatSpeeds)) {
    localSection.value.repeatSpeeds = [];
  }

  while (localSection.value.repeatSpeeds.length < count) {
    localSection.value.repeatSpeeds.push(defaultSpeed);
  }

  if (localSection.value.repeatSpeeds.length > count) {
    localSection.value.repeatSpeeds = localSection.value.repeatSpeeds.slice(0, count);
  }
};

// 更新重复停顿数组
const updateRepeatPauses = () => {
  const count = localSection.value.repeatCount || 4;
  const defaultPause = localSection.value.pauseDuration || 3000;

  if (!Array.isArray(localSection.value.repeatPauses)) {
    localSection.value.repeatPauses = [];
  }

  while (localSection.value.repeatPauses.length < count) {
    localSection.value.repeatPauses.push(defaultPause);
  }

  if (localSection.value.repeatPauses.length > count) {
    localSection.value.repeatPauses = localSection.value.repeatPauses.slice(0, count);
  }
};

// 更新重复数组
const updateRepeatArrays = () => {
  updateRepeatSpeeds();
  updateRepeatPauses();

  // 确保翻译和关键词位置不超过重复次数
  const count = localSection.value.repeatCount || 4;
  if (localSection.value.translationPosition > count) {
    localSection.value.translationPosition = count;
  }
  if (localSection.value.keywordPosition > count) {
    localSection.value.keywordPosition = count;
  }
};

// 判断是否显示插入内容
const shouldShowInsert = (position) => {
  return (localSection.value.enableTranslation && localSection.value.translationPosition === position) ||
    (localSection.value.enableKeywords && localSection.value.keywordPosition === position);
};

// 初始化
updateRepeatArrays();
</script>

<style scoped>
.section-config-form {
  width: 100%;
}

.settings-section {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #fff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #303133;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-subtitle {
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.section-title-with-switch {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 0.0625rem solid #f0f0f0;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.title-with-tooltip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 500;
}

.question-icon {
  font-size: 1rem;
  color: #909399;
  cursor: help;
  display: flex;
  align-items: center;
}

.form-section-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-section-row>* {
  flex: 1;
  min-width: 45%;
}

.setting-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 45%;
}

.setting-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #606266;
  margin-bottom: 0.5rem;
}

.setting-control {
  width: 100%;
}

.form-help {
  font-size: 0.75rem;
  color: #909399;
  margin-top: 0.25rem;
  line-height: 1.4;
}

/* 播放流程样式 */
.playback-flow {
  margin: 0.5rem 0;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
}

.flow-timeline {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.flow-step {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.step-content {
  background: white;
  border-radius: 6px;
  padding: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.step-content:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #f8f9fa;
}

.step-number {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.step-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.step-params {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.param-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-input {
  width: 100%;
}

.insert-content {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-left: 1rem;
}

.insert-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  border-left: 3px solid;
}

.insert-item.translation {
  background: #e3f2fd;
  border-left-color: #2196f3;
  color: #1565c0;
}

.insert-item.keyword {
  background: #fff3e0;
  border-left-color: #ff9800;
  color: #e65100;
}

.insert-icon {
  font-size: 1rem;
}

.insert-text {
  flex: 1;
}

.flow-connector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0.25rem 0;
  position: relative;
}

.connector-line {
  width: 1px;
  height: 12px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
  border-radius: 1px;
}

.connector-arrow {
  position: absolute;
  bottom: -3px;
  font-size: 1rem;
  color: #667eea;
  font-weight: bold;
  transform: rotate(90deg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-section-row {
    flex-direction: column;
    gap: 1rem;
  }

  .form-section-row>* {
    min-width: 100%;
  }

  .setting-item {
    min-width: 100%;
  }

  .playback-flow {
    padding: 1rem;
  }

  .step-params {
    grid-template-columns: 1fr;
  }

  .insert-content {
    margin-left: 1rem;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .step-number {
    align-self: center;
  }
}
</style>
