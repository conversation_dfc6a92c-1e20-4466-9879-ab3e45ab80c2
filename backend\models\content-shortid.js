/**
 * 内容模型 (使用 ShortID)
 * 用于存储用户创建的素材内容配置
 */
module.exports = (sequelize, DataTypes) => {
  const Content = sequelize.define('Content', {
    // 主键ID，使用ShortID
    id: {
      type: DataTypes.STRING(14),
      primaryKey: true
    },
    
    // 素材名称
    name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    
    // 素材描述
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    
    // 配置JSON数据
    configJson: {
      type: DataTypes.JSON,
      allowNull: false,
      field: 'config_json'
    },
    
    // 缩略图URL(可选)
    thumbnailUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      field: 'thumbnail_url'
    },
    
    // 用户ID(可选)
    userId: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'user_id'
    },
    
    // 标签，逗号分隔(可选)
    tags: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    
    // 状态：draft(草稿), published(已发布)
    status: {
      type: DataTypes.STRING(20),
      allowNull: false,
      defaultValue: 'draft'
    }
  }, {
    tableName: 'contents',
    underscored: true,
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_name',
        fields: ['name']
      },
      {
        name: 'idx_user_id',
        fields: ['user_id']
      },
      {
        name: 'idx_created_at',
        fields: ['created_at']
      },
      {
        name: 'idx_status',
        fields: ['status']
      }
    ]
  });

  return Content;
};
