<!--
  收藏按钮组件
  用于在内容卡片和播放器页面添加收藏功能
-->
<template>
  <div class="favorite-button" :class="{ 'is-favorited': isFavorited, 'in-player-controls': inPlayerControls }">
    <el-tooltip :content="isFavorited ? '取消收藏' : '收藏'" placement="top" :show-after="300">
      <el-button :type="isFavorited ? 'danger' : 'default'" :icon="isFavorited ? StarFilled : Star" circle :size="size"
        @click.stop="handleToggleFavorite" :disabled="loading || disabled" :class="{ 'is-loading': loading }" />
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Star, StarFilled } from "@element-plus/icons-vue";
import { useFavoriteStore } from "@/stores/favoriteStore";
import { useUserStore } from "@/stores/userStore";
import { useRouter } from "vue-router";

const props = defineProps({
  contentId: {
    type: String,
    required: true,
  },
  contentData: {
    type: Object,
    default: null,
  },
  size: {
    type: String,
    default: "default",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  inPlayerControls: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["favorite", "unfavorite"]);

const favoriteStore = useFavoriteStore();
const userStore = useUserStore();
const router = useRouter();

const loading = ref(false);
const isFavorited = computed(() => favoriteStore.isFavorite(props.contentId));

// 初始化时检查收藏状态
onMounted(async () => {
  if (userStore.isLoggedIn && props.contentId) {
    await favoriteStore.checkFavorite(props.contentId);
  }
});

// 切换收藏状态
const handleToggleFavorite = async (event) => {
  // 阻止事件冒泡，避免触发卡片点击
  event.stopPropagation();

  // 检查用户是否已登录
  if (!userStore.isLoggedIn) {
    ElMessage.warning("请先登录后再收藏内容");
    router.push("/login");
    return;
  }

  loading.value = true;
  try {
    const result = await favoriteStore.toggleFavorite(
      props.contentId,
      props.contentData
    );

    if (result.success) {
      if (favoriteStore.isFavorite(props.contentId)) {
        ElMessage.success("收藏成功");
        emit("favorite", props.contentId);
      } else {
        ElMessage.success("已取消收藏");
        emit("unfavorite", props.contentId);
      }
    } else {
      ElMessage.error(result.error || "操作失败");
    }
  } catch (error) {
    console.error("收藏操作失败:", error);
    ElMessage.error("操作失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.favorite-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.favorite-button .el-button {
  transition: all 0.3s;
}

/* 已收藏状态样式 */
.favorite-button.is-favorited .el-button {
  background-color: #f56c6c;
  color: #ffffff;
  border-color: #f56c6c;
}

/* 未收藏状态悬停样式 */
.favorite-button:not(.is-favorited) .el-button:hover {
  color: #f56c6c;
  border-color: #f56c6c;
}

.favorite-button .is-loading {
  pointer-events: none;
  opacity: 0.8;
}

/* 在播放器控制区中的样式 */
.favorite-button.in-player-controls {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-button.in-player-controls .el-button {
  width: 1.75rem;
  height: 1.75rem;
  padding: 0;
  font-size: 1.25rem;
}

.favorite-button.in-player-controls .el-button :deep(.el-icon) {
  font-size: 1.25rem;
  width: 1.25rem;
  height: 1.25rem;
}
</style>
