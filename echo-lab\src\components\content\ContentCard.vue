<!--
  内容卡片组件
  用于在首页和内容列表中展示内容项目
-->
<template>
  <div class="content-card" @click="handleCardClick">
    <!-- 卡片缩略图 -->
    <div class="card-thumbnail">
      <img v-if="content.thumbnailUrl" :src="content.thumbnailUrl" alt="缩略图" />
      <div v-else class="thumbnail-placeholder">
        <el-icon :size="32">
          <Picture />
        </el-icon>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="card-title">{{ content.name }}</h3>
      <p v-if="content.description" class="card-description">{{ content.description }}</p>
      <div class="card-footer">
        <el-tag :type="content.status === 'published' ? 'success' : 'warning'" size="small">
          {{ content.status === 'published' ? '已发布' : '草稿' }}
        </el-tag>
        <span class="card-date">{{ formatDate(content.updatedAt) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Picture } from '@element-plus/icons-vue';

const props = defineProps({
  content: {
    type: Object,
    required: true
  }
});

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

// 处理卡片点击（新标签页）
const handleCardClick = () => {
  window.open(`/player/${props.content.id}`, '_blank');
};
</script>

<style scoped>
.content-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.content-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

.card-thumbnail {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  background-color: #f5f7fa;
  overflow: hidden;
}

.card-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-description {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.card-date {
  font-size: 0.75rem;
  color: #909399;
}
</style>
