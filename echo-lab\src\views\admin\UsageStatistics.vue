<template>
  <div class="usage-statistics">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>功能使用统计</h2>
          <div class="header-actions">
            <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" :shortcuts="dateShortcuts" @change="handleDateRangeChange" />
            <el-button type="primary" @click="fetchStatistics">刷新</el-button>
          </div>
        </div>
      </template>

      <!-- 功能使用概览 -->
      <div class="statistics-overview">
        <el-row :gutter="20">
          <el-col :span="8" v-for="(stat, index) in overviewStats" :key="index">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-content">
                <div class="stat-icon" :style="{ backgroundColor: stat.color }">
                  <el-icon>
                    <component :is="stat.icon" />
                  </el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 功能使用详情 -->
      <div class="statistics-details">
        <h3>功能使用详情</h3>
        <el-table v-loading="loading" :data="featureStats" border>
          <el-table-column prop="featureKey" label="功能" width="150">
            <template #default="scope">
              {{ getFeatureName(scope.row.featureKey) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalUsage" label="总使用次数" width="120" sortable />
          <el-table-column prop="uniqueUsers" label="使用用户数" width="120" sortable />
          <el-table-column prop="avgPerUser" label="人均使用" width="120" sortable>
            <template #default="scope">
              {{ scope.row.avgPerUser.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="使用趋势" min-width="300">
            <template #default="scope">
              <div class="trend-chart">
                <el-progress v-for="(day, index) in scope.row.dailyTrend" :key="index"
                  :percentage="calculatePercentage(day.count, scope.row.maxDaily)"
                  :color="getProgressColor(day.count, scope.row.maxDaily)" :stroke-width="12" :show-text="false"
                  class="trend-bar">
                  <template #default>
                    <el-tooltip :content="`${day.date}: ${day.count}次`" placement="top">
                      <div class="trend-tooltip-trigger"></div>
                    </el-tooltip>
                  </template>
                </el-progress>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 用户使用排行 -->
      <div class="user-rankings">
        <h3>用户使用排行</h3>
        <el-table v-loading="loading" :data="userRankings" border>
          <el-table-column type="index" label="排名" width="80" />
          <el-table-column prop="user.email" label="用户邮箱" min-width="180" show-overflow-tooltip />
          <el-table-column prop="user.level" label="用户等级" width="100">
            <template #default="scope">
              {{ getLevelName(scope.row.user.level) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalUsage" label="总使用次数" width="120" sortable />
          <el-table-column label="功能使用分布" min-width="300">
            <template #default="scope">
              <div class="feature-distribution">
                <el-tooltip v-for="(feature, index) in scope.row.featureUsage" :key="index"
                  :content="`${getFeatureName(feature.featureKey)}: ${feature.count}次`" placement="top">
                  <div class="feature-block" :style="{
                    width: `${calculatePercentage(feature.count, scope.row.totalUsage)}%`,
                    backgroundColor: getFeatureColor(feature.featureKey)
                  }"></div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import {
  Document,
  VideoPlay,
  User,
  DataLine
} from '@element-plus/icons-vue';
import {
  getAllLevels,
  getUsageStatistics,
  getUserRankings
} from '@/services/adminService';
import { getAllFeatures } from '@/services/featurePermissionService';

// 数据和状态
const loading = ref(false);
const levels = ref([]);
const featureStats = ref([]);
const userRankings = ref([]);
const dateRange = ref([]);

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 功能列表 - 从服务器获取，而不是硬编码
const allFeatures = ref([
  { key: 'player_access', name: '视频播放', color: '#409EFF' },
  { key: 'content_creation', name: '内容创建与管理', color: '#E6A23C' },
  { key: 'video_export', name: '视频导出', color: '#909399' }
]);

// 概览统计
const overviewStats = computed(() => {
  if (!featureStats.value.length) {
    return [
      { label: '总使用次数', value: 0, icon: 'DataLine', color: '#409EFF' },
      { label: '活跃用户数', value: 0, icon: 'User', color: '#67C23A' },
      { label: '视频导出次数', value: 0, icon: 'VideoPlay', color: '#E6A23C' }
    ];
  }

  const totalUsage = featureStats.value.reduce((sum, stat) => sum + stat.totalUsage, 0);
  const uniqueUsers = new Set(featureStats.value.flatMap(stat => stat.userIds)).size;
  const videoExports = featureStats.value.find(stat => stat.featureKey === 'video_export')?.totalUsage || 0;

  return [
    { label: '总使用次数', value: totalUsage, icon: DataLine, color: '#409EFF' },
    { label: '活跃用户数', value: uniqueUsers, icon: User, color: '#67C23A' },
    { label: '视频导出次数', value: videoExports, icon: VideoPlay, color: '#E6A23C' }
  ];
});

// 获取所有等级
async function fetchLevels() {
  try {
    const result = await getAllLevels();
    levels.value = result;
  } catch (error) {
    console.error('获取等级列表失败:', error);
  }
}

// 获取使用统计
async function fetchStatistics() {
  loading.value = true;
  try {
    // 设置默认日期范围为最近30天
    if (!dateRange.value || !dateRange.value.length) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      dateRange.value = [start, end];
    }

    const startDate = dateRange.value[0].toISOString();
    const endDate = dateRange.value[1].toISOString();

    // 获取功能使用统计
    const stats = await getUsageStatistics(startDate, endDate);
    featureStats.value = stats;

    // 获取用户排行
    const rankings = await getUserRankings(startDate, endDate, 10);
    userRankings.value = rankings;
  } catch (error) {
    console.error('获取使用统计失败:', error);
  } finally {
    loading.value = false;
  }
}

// 处理日期范围变化
function handleDateRangeChange() {
  fetchStatistics();
}

// 获取等级名称
function getLevelName(level) {
  const levelObj = levels.value.find(l => l.level === level);
  return levelObj ? levelObj.name : `等级 ${level}`;
}

// 获取功能名称
function getFeatureName(featureKey) {
  const feature = allFeatures.find(f => f.key === featureKey);
  return feature ? feature.name : featureKey;
}

// 获取功能颜色
function getFeatureColor(featureKey) {
  const feature = allFeatures.find(f => f.key === featureKey);
  return feature ? feature.color : '#409EFF';
}

// 计算百分比
function calculatePercentage(value, total) {
  if (!total) return 0;
  return Math.min(100, Math.max(5, (value / total) * 100));
}

// 获取进度条颜色
function getProgressColor(value, max) {
  const percentage = (value / max) * 100;
  if (percentage < 30) return '#909399';
  if (percentage < 60) return '#67C23A';
  if (percentage < 80) return '#E6A23C';
  return '#F56C6C';
}

// 获取功能列表
async function fetchFeatures() {
  try {
    const features = await getAllFeatures();
    if (features && features.length > 0) {
      // 保留原有的颜色设置
      allFeatures.value = features.map(feature => {
        const existingFeature = allFeatures.value.find(f => f.key === feature.key);
        return {
          ...feature,
          color: existingFeature?.color || '#409EFF'
        };
      });
    }
  } catch (error) {
    console.error('获取功能列表失败:', error);
  }
}

onMounted(async () => {
  await fetchFeatures();
  fetchLevels();
  fetchStatistics();
});
</script>

<style scoped>
.usage-statistics {
  padding: 1rem;
}

.page-card {
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.statistics-overview {
  margin-bottom: 2rem;
}

.stat-card {
  height: 100%;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
}

.stat-icon .el-icon {
  font-size: 1.5rem;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: var(--el-text-color-secondary);
}

.statistics-details,
.user-rankings {
  margin-top: 2rem;
}

.trend-chart {
  display: flex;
  align-items: flex-end;
  height: 2rem;
  gap: 0.25rem;
}

.trend-bar {
  flex: 1;
  position: relative;
}

.trend-tooltip-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.feature-distribution {
  display: flex;
  height: 1.5rem;
  width: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
}

.feature-block {
  height: 100%;
  min-width: 5%;
  transition: all 0.3s;
}

.feature-block:hover {
  opacity: 0.8;
}
</style>
