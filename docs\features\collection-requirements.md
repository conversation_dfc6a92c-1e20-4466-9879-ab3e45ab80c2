# Echo Lab 合集功能需求文档

## 1. 功能概述

合集功能允许用户将多个内容组织成一个播放列表，支持连续播放和管理。

## 2. 核心功能

### 2.1 合集管理
- **创建合集**：用户可以创建新的合集，设置名称、描述、标签
- **编辑合集**：修改合集信息，添加/删除内容，调整播放顺序
- **删除合集**：删除不需要的合集
- **合集列表**：查看用户创建的所有合集

### 2.2 内容管理
- **添加内容**：从内容库中选择内容添加到合集
- **移除内容**：从合集中移除不需要的内容
- **排序调整**：拖拽调整合集中内容的播放顺序
- **批量操作**：支持批量添加/删除内容

### 2.3 合集播放
- **连续播放**：自动播放合集中的下一个内容
- **手动切换**：用户可以手动跳转到任意内容
- **播放进度**：显示当前播放第几个内容（如：3/15）
- **播放列表**：显示合集中所有内容的列表

### 2.4 分享功能
- **公开合集**：设置合集为公开，其他用户可以查看
- **私有合集**：仅创建者可以查看和编辑
- **收藏合集**：用户可以收藏其他人的公开合集

## 3. 用户界面

### 3.1 合集管理页面
- 合集列表展示（卡片形式）
- 创建合集按钮
- 搜索和筛选功能
- 每个合集显示：封面、名称、描述、内容数量、操作按钮

### 3.2 合集编辑页面
- 合集基本信息编辑
- 内容列表管理
- 拖拽排序功能
- 添加内容的选择界面

### 3.3 合集播放页面
- 合集信息展示
- 当前播放内容信息
- 播放进度指示（当前第几个）
- 播放列表侧边栏
- 上一个/下一个切换按钮

## 4. 权限控制

### 4.1 创建权限
- 需要 `content_creation` 权限才能创建合集

### 4.2 访问权限
- 公开合集：所有用户可查看
- 私有合集：仅创建者可查看

### 4.3 编辑权限
- 仅合集创建者和管理员可编辑

## 5. 数据结构

### 5.1 合集表
- ID、名称、描述、封面、创建者、公开状态、标签、时间戳

### 5.2 合集内容关联表
- 合集ID、内容ID、排序顺序、添加时间

## 6. 技术要求

### 6.1 前端组件
- CollectionCard（合集卡片）
- CollectionEditor（合集编辑器）
- CollectionPlayer（合集播放器）
- CollectionList（合集列表）

### 6.2 后端API
- 合集CRUD接口
- 合集内容管理接口
- 合集播放接口

### 6.3 状态管理
- 合集数据管理
- 播放状态管理
- 用户权限管理

## 7. 用户流程

### 7.1 创建合集
1. 点击"创建合集"
2. 填写合集信息
3. 选择要添加的内容
4. 调整内容顺序
5. 保存合集

### 7.2 播放合集
1. 点击合集卡片
2. 进入播放页面
3. 自动播放第一个内容
4. 内容结束后自动播放下一个
5. 显示播放进度和列表

### 7.3 分享合集
1. 设置合集为公开
2. 其他用户可在公开合集中查看
3. 支持收藏功能

## 8. 移动端适配

- 响应式布局
- 触摸友好的操作
- 简化的编辑界面
- 适配移动端播放器

## 9. 实施优先级

### 高优先级
- 合集基础CRUD功能
- 合集播放功能
- 内容添加/删除功能

### 中优先级
- 拖拽排序功能
- 公开/分享功能
- 移动端优化

### 低优先级
- 高级筛选功能
- 批量操作功能
- 统计分析功能

## 10. 注意事项

- Echo Lab 的内容没有固定时长，因此合集不显示总时长信息
- 播放进度仅显示当前播放第几个内容，不显示时间进度
- 合集播放基于时间线生成，每个内容动态生成播放序列
- 需要复用现有的收藏系统来实现合集收藏功能
