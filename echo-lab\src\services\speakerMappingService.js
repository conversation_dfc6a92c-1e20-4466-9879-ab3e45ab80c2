/**
 * 说话人映射服务
 * 提供说话人映射相关的功能，使用本地存储保存用户自定义映射
 */
import {
  getDefaultMappings,
  getVoiceById,
  getVoicesForLanguage,
} from "./ttsInfoService";

// 本地存储键名
const STORAGE_KEY = "echolab_speaker_mappings";

class SpeakerMappingService {
  /**
   * 保存用户自定义的说话人映射到本地存储
   * @param {Object} mappings 说话人映射对象
   */
  saveMappings(mappings) {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(mappings));
      return true;
    } catch (error) {
      console.error("保存说话人映射失败:", error);
      return false;
    }
  }

  /**
   * 从本地存储加载用户自定义的说话人映射
   */
  loadMappings() {
    try {
      const mappings = localStorage.getItem(STORAGE_KEY);
      return mappings ? JSON.parse(mappings) : null;
    } catch (error) {
      console.error("加载说话人映射失败:", error);
      return null;
    }
  }

  /**
   * 清除本地存储中的说话人映射
   */
  clearMappings() {
    try {
      localStorage.removeItem(STORAGE_KEY);
      return true;
    } catch (error) {
      console.error("清除说话人映射失败:", error);
      return false;
    }
  }

  /**
   * 获取说话人信息
   * @param {string} speaker 说话人名称
   * @param {string} language 语言代码
   * @returns {Promise<Object|null>} 说话人信息对象，包含name、speaker_id、id和voice_info属性
   */
  async getSpeakerInfo(speaker, language) {
    if (!speaker || !language) return null;

    try {
      // 1. 尝试从用户自定义映射中获取
      const userMappings = this.loadMappings();
      const defaultMappings = await getDefaultMappings();

      // 2. 确定要使用的映射（优先使用用户自定义映射）
      const mappings = userMappings || defaultMappings;
      const langMappings = mappings[language] || {};

      // 3. 查找说话人映射
      let voiceId = null;

      if (langMappings[speaker]) {
        // 找到特定说话人的映射
        voiceId = langMappings[speaker];
        console.log(`找到说话人 "${speaker}" 的映射，使用声音ID: ${voiceId}`);
      } else if (langMappings.default) {
        // 使用默认映射
        voiceId = langMappings.default;
        console.log(
          `未找到说话人 "${speaker}" 的映射，使用默认声音ID: ${voiceId}`
        );
      } else if (defaultMappings[language]?.default) {
        // 使用系统默认映射
        voiceId = defaultMappings[language].default;
        console.log(`未找到用户映射，使用系统默认声音ID: ${voiceId}`);
      } else {
        // 获取该语言的第一个可用声音
        const voices = await getVoicesForLanguage(language);
        if (voices.length > 0) {
          voiceId = voices[0].id;
          console.log(
            `没有找到说话人 "${speaker}" 的映射，使用语言 "${language}" 的第一个可用声音ID: ${voiceId}`
          );
        } else {
          console.error(
            `无法为说话人 "${speaker}" 和语言 "${language}" 找到声音`
          );
          return null;
        }
      }

      // 4. 获取声音信息
      const voiceInfo = await getVoiceById(voiceId);
      if (!voiceInfo) {
        console.error(`无法获取ID为 ${voiceId} 的声音信息`);
        return null;
      }

      // 5. 返回说话人信息
      return {
        name: speaker,
        speaker_id: voiceInfo.speaker_id,
        id: voiceId, // 数据库ID
        voice_info: voiceInfo,
        // 添加人类可读的声音名称
        displayName: voiceInfo.name || `声音ID: ${voiceId}`,
        // 添加服务ID，用于特殊词汇处理
        service_id: voiceInfo.service_id || "google",
      };
    } catch (error) {
      console.error("获取说话人信息失败:", error);
      return null;
    }
  }
}

// 创建单例
const speakerService = new SpeakerMappingService();

// 导出方法
export const saveSpeakerMappings = (mappings) =>
  speakerService.saveMappings(mappings);
export const loadSpeakerMappings = () => speakerService.loadMappings();
export const clearSpeakerMappings = () => speakerService.clearMappings();
export const getSpeakerInfo = (speaker, language) =>
  speakerService.getSpeakerInfo(speaker, language);

export default {
  saveSpeakerMappings,
  loadSpeakerMappings,
  clearSpeakerMappings,
  getSpeakerInfo,
};
