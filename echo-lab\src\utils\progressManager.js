/**
 * 进度管理工具
 * 统一管理音频合成进度状态
 */

/**
 * 进度管理器类
 */
export class ProgressManager {
  constructor(progressState) {
    this.progressState = progressState;
  }

  /**
   * 初始化进度状态
   * @param {number} total 总数量
   */
  initialize(total = 0) {
    Object.assign(this.progressState, {
      total,
      completed: 0,
      percentage: 0,
      isProcessing: true
    });
  }

  /**
   * 更新进度
   * @param {number} completed 已完成数量
   * @param {string} stage 当前阶段 ('loading', 'processing', 'finalizing')
   */
  update(completed, stage = 'processing') {
    this.progressState.completed = completed;
    
    // 根据阶段计算百分比
    let percentage = 0;
    
    switch (stage) {
      case 'loading':
        // 加载阶段占0-80%
        percentage = Math.min((completed / this.progressState.total) * 80, 80);
        break;
      case 'processing':
        // 处理阶段占80-95%
        percentage = 80 + Math.min((completed / this.progressState.total) * 15, 15);
        break;
      case 'finalizing':
        // 最终处理阶段95-100%
        percentage = 95 + Math.min((completed / this.progressState.total) * 5, 5);
        break;
      default:
        percentage = (completed / this.progressState.total) * 100;
    }
    
    this.progressState.percentage = Math.round(percentage);
  }

  /**
   * 设置特定百分比
   * @param {number} percentage 百分比
   */
  setPercentage(percentage) {
    this.progressState.percentage = Math.round(Math.min(Math.max(percentage, 0), 100));
  }

  /**
   * 完成进度
   */
  complete() {
    Object.assign(this.progressState, {
      completed: this.progressState.total,
      percentage: 100,
      isProcessing: false
    });
  }

  /**
   * 重置进度
   */
  reset() {
    Object.assign(this.progressState, {
      total: 0,
      completed: 0,
      percentage: 0,
      isProcessing: false
    });
  }

  /**
   * 设置错误状态
   */
  setError() {
    Object.assign(this.progressState, {
      percentage: 0,
      isProcessing: false
    });
  }

  /**
   * 创建进度回调函数
   * @param {string} stage 当前阶段
   * @returns {Function} 进度回调函数
   */
  createCallback(stage = 'processing') {
    return (completed) => {
      this.update(completed, stage);
    };
  }
}

/**
 * 创建进度管理器
 * @param {Object} progressState 进度状态对象
 * @returns {ProgressManager} 进度管理器实例
 */
export function createProgressManager(progressState) {
  return new ProgressManager(progressState);
}
