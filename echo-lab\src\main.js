// 确保Vue核心首先加载
import { createApp, h } from "vue";

// 按需引入Element Plus样式
import "@/styles/element-plus.scss";
import "@/assets/styles/element-overrides.css";

// 其他核心导入
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import { registerNodeComponents } from "@/components/nodes";
import "@/core";
import { useFavoriteStore } from "@/stores/favoriteStore";
import { useUserStore } from "@/stores/userStore";
import { registerSW } from "virtual:pwa-register";
import { hideInitialLoading } from "./utils/hideLoading";
import { initializeErrorHandler, handleVueError } from "@/utils/errorHandler";

// 在所有核心模块加载完成后，再导入图标
// 只导入实际使用的图标
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Cellphone,
  ChatLineRound,
  Check,
  Close,
  CopyDocument,
  Delete,
  Document,
  Download,
  Edit,
  EditPen,
  Headset,
  InfoFilled,
  Loading,
  Lock,
  Menu,
  Monitor,
  More,
  Mute,
  Notification,
  Operation,
  Picture,
  Plus,
  QuestionFilled,
  Refresh,
  RefreshRight,
  Search,
  Select,
  Setting,
  Star,
  StarFilled,
  Timer,
  Top,
  Upload,
  User,
  VideoCamera,
  VideoPause,
  VideoPlay,
  View,
  WarningFilled,
  ZoomIn,
  ZoomOut,
} from "@element-plus/icons-vue";

// 开发环境下添加WebCodecs测试工具到全局
if (import.meta.env.DEV) {
  import("./utils/webCodecsTest.js").then((module) => {
    window.webCodecsTest = module;
    console.log(
      "WebCodecs测试工具已加载，使用 webCodecsTest.runFullCompatibilityTest() 进行测试"
    );
  });

  // 加载快速测试工具
  import("./utils/webCodecsQuickTest.js").then(() => {
    console.log("WebCodecs快速测试工具已加载");
  });

  // 加载Muxer配置测试工具
  import("./utils/muxerConfigTest.js").then(() => {
    console.log("MP4Muxer配置测试工具已加载");
  });
}

// 创建应用实例（使用 h 函数）
const app = createApp({
  render: () => h(App), // 确保渲染函数可用
});

// 配置Vue错误处理器
app.config.errorHandler = handleVueError;

// 注册图标组件
function registerIcons(app) {
  try {
    const icons = {
      ArrowDown,
      ArrowLeft,
      ArrowRight,
      ArrowUp,
      Cellphone,
      ChatLineRound,
      Check,
      Close,
      CopyDocument,
      Delete,
      Document,
      Download,
      Edit,
      EditPen,
      Headset,
      InfoFilled,
      Loading,
      Lock,
      Menu,
      Monitor,
      More,
      Mute,
      Notification,
      Operation,
      Picture,
      Plus,
      QuestionFilled,
      Refresh,
      RefreshRight,
      Search,
      Select,
      Setting,
      Star,
      StarFilled,
      Timer,
      Top,
      Upload,
      User,
      VideoCamera,
      VideoPause,
      VideoPlay,
      View,
      WarningFilled,
      ZoomIn,
      ZoomOut,
    };

    Object.entries(icons).forEach(([key, component]) => {
      app.component(key, component);
    });
    console.log("图标注册完成");
  } catch (error) {
    console.error("图标注册失败:", error);
  }
}

// 注册节点组件
registerNodeComponents(app);

// 使用插件（确保顺序正确）
app.use(createPinia());
app.use(router);

// 认证拦截器已在httpClient中统一设置，无需单独设置

// 初始化用户状态和收藏状态
const initUserAndFavorites = async () => {
  const userStore = useUserStore();
  const favoriteStore = useFavoriteStore();

  // 如果本地存储中有登录状态，从服务器获取最新的用户信息
  if (userStore.isLoggedIn) {
    // 获取最新用户信息
    await userStore.fetchCurrentUser();

    // 获取用户收藏
    await favoriteStore.fetchFavorites();
  }
};

// 初始化应用
async function initializeApp() {
  try {
    // 注册图标
    registerIcons(app);

    // 挂载应用
    app.mount("#app");

    // 初始化全局错误处理器
    initializeErrorHandler();

    // 初始化用户状态和收藏状态
    await initUserAndFavorites();

    console.log("应用初始化完成");
  } catch (error) {
    console.error("应用初始化失败:", error);
    // 确保应用已挂载
    try {
      app.mount("#app");
    } catch (mountError) {
      console.error("应用挂载失败:", mountError);
    }
  }
}

// 启动应用
initializeApp();

// 注册 Service Worker
console.log("开始注册 Service Worker...");
try {
  const updateSW = registerSW({
    onNeedRefresh() {
      // 当有新版本时，可以提示用户刷新
      console.log("有新版本可用，请刷新页面");
    },
    onOfflineReady() {
      // 当离线就绪时
      console.log("应用已准备好离线使用");
    },
    onRegistered(registration) {
      console.log("Service Worker 注册成功:", registration);
      // 在Service Worker注册成功后隐藏加载画面
      hideInitialLoading();
    },
    onRegisterError(error) {
      console.error("Service Worker 注册失败:", error);
      // 即使注册失败也隐藏加载画面
      hideInitialLoading();
    },
  });
} catch (error) {
  console.error("Service Worker 注册过程中发生错误:", error);
  // 出错时也隐藏加载画面
  hideInitialLoading();
}

// 如果Service Worker注册时间过长，3秒后强制隐藏加载画面
setTimeout(hideInitialLoading, 3000);
