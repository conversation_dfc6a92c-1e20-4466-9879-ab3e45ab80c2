<!--
  音频切割对话框组件
  使用 WaveSurfer.js 实现专业的音频波形图和切割功能
-->
<template>
  <standard-dialog v-model="dialogVisible" title="音频切割工具" width="90%" :before-close="handleDialogClose">

    <div class="audio-cutting-container">
      <!-- 音频文件管理区域 -->
      <div class="audio-files-section">
        <h4>📁 音频文件管理</h4>
        <div class="audio-files-list">
          <!-- 音频文件列表 -->
          <div v-if="audioFiles.length > 0" class="files-grid">
            <div v-for="(file, index) in audioFiles" :key="file.id"
                 class="audio-file-item"
                 :class="{ 'active': currentAudioId === file.id }">
              <div class="file-info">
                <el-icon class="file-icon"><VideoPlay /></el-icon>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-duration" v-if="file.duration">{{ formatDuration(file.duration) }}</span>
              </div>
              <div class="file-actions">
                <el-button v-if="currentAudioId !== file.id" size="small" type="primary" @click="switchToAudioFile(file.id)">
                  使用
                </el-button>
                <span v-else class="current-label">当前</span>
                <el-button size="small" type="danger" @click="removeAudioFile(file.id)">
                  删除
                </el-button>
              </div>
            </div>
          </div>

          <!-- 添加音频文件按钮 -->
          <div class="add-audio-section">
            <el-upload ref="uploadRef" class="audio-upload" drag :auto-upload="false" :show-file-list="false"
              accept="audio/*" :on-change="handleFileSelect">
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text">
                {{ audioFiles.length === 0 ? '将音频文件拖到此处，或' : '添加更多音频文件，或' }}<em>点击上传</em>
              </div>
              <div class="el-upload__tip">
                支持 MP3、WAV、M4A 格式
              </div>
            </el-upload>
          </div>
        </div>
      </div>

      <!-- 音频处理区域 -->
      <div v-if="currentAudioFile" class="cutting-section">
        <!-- 当前音频信息 -->
        <div class="current-audio-info">
          <div class="audio-file-info">
            <span class="file-name">🎵 当前音频: {{ currentAudioFile.name }}</span>
            <span class="file-duration" v-if="audioDuration > 0">
              ({{ formatDuration(audioDuration) }})
            </span>
            <el-button size="small" @click="switchToFileManagement">切换音频文件</el-button>
          </div>
        </div>

        <!-- WaveSurfer 波形图容器 -->
        <div class="waveform-container">
          <div id="waveform-container" ref="waveformRef" class="waveform">
            <div v-if="isWaveSurferLoading" class="loading-indicator">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <span>正在初始化音频处理器...</span>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="waveform-controls">
          <div class="control-buttons">
            <el-button-group>
              <el-button size="small" @click="playPause">
                <el-icon>
                  <VideoPlay v-if="!isPlaying" />
                  <VideoPause v-else />
                </el-icon>
                {{ isPlaying ? '暂停' : '播放' }}
              </el-button>
              <el-button size="small" @click="addRegionAtCursor">
                <el-icon>
                  <Scissor />
                </el-icon>
                添加区域
              </el-button>
              <el-button size="small" @click="clearAllRegions">
                <el-icon>
                  <RefreshLeft />
                </el-icon>
                清除所有区域
              </el-button>
              <el-button size="small" @click="zoomIn">
                <el-icon>
                  <ZoomIn />
                </el-icon>
                放大
              </el-button>
              <el-button size="small" @click="zoomOut">
                <el-icon>
                  <ZoomOut />
                </el-icon>
                缩小
              </el-button>
            </el-button-group>
          </div>
          <div class="control-tips">
            <el-text size="small" type="info">
              💡 操作提示：拖拽波形图选择音频区域，然后建立与文本的对应关系
            </el-text>
          </div>
        </div>
      </div>

      <!-- 区域列表和文本对应 -->
      <div class="mapping-section">
        <div class="mapping-header">
          <h4>音频区域与文本对应 ({{ regions.length }} 个区域)</h4>
          <div class="mapping-actions">
            <el-button size="small" type="primary" @click="autoMapping">
              自动对应
            </el-button>
            <el-button size="small" type="success" @click="createManualMapping"
              :disabled="selectedTextIndex === -1 || selectedRegionIndex === -1">
              建立对应
            </el-button>
            <el-button size="small" @click="clearAllMappings">
              清除对应
            </el-button>
          </div>
        </div>

        <div class="mapping-content">
          <!-- 文本分句列表 -->
          <div class="text-segments">
            <h5>文本分句 ({{ textSegments.length }} 个)
              <span v-if="selectedTextIndex !== -1" class="selection-hint">
                - 已选择第{{ selectedTextIndex + 1 }}个
              </span>
            </h5>
            <div class="segment-list">
              <div v-for="(segment, index) in textSegments" :key="segment.id" class="text-segment-item" :class="{
                'selected': selectedTextIndex === index,
                'mapped': isMapped('text', index)
              }" @click="selectTextSegment(index)">
                <div class="segment-number">{{ index + 1 }}</div>
                <div class="segment-content">
                  <div class="segment-text">{{ segment.content }}</div>
                  <div class="segment-meta">
                    <el-tag size="small" :type="getSpeakerTagType(segment.speaker)">
                      {{ segment.speaker || '默认' }}
                    </el-tag>
                    <span class="segment-language">{{ getLanguageLabel(segment.language) }}</span>
                    <span v-if="isMapped('text', index)" class="mapping-indicator">
                      ↔ 区域{{ getMappedRegionIndex(index) + 1 }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 音频区域列表 -->
          <div class="audio-regions">
            <h5>音频区域 ({{ regions.length }} 个)
              <span v-if="selectedRegionIndex !== -1" class="selection-hint">
                - 已选择第{{ selectedRegionIndex + 1 }}个
              </span>
            </h5>
            <div class="segment-list">
              <div v-for="(region, index) in regions" :key="region.id" class="audio-segment-item" :class="{
                'selected': selectedRegionIndex === index,
                'mapped': isMapped('region', index)
              }" @click="selectRegion(index)">
                <div class="segment-number">{{ index + 1 }}</div>
                <div class="segment-content">
                  <div class="segment-time">
                    {{ formatTime(region.start) }} - {{ formatTime(region.end) }}
                    <span v-if="isMapped('region', index)" class="mapping-indicator">
                      ↔ 文本{{ getMappedTextIndex(index) + 1 }}
                    </span>
                  </div>
                  <div class="segment-duration">
                    时长: {{ formatDuration(region.end - region.start) }}
                  </div>
                  <div class="region-actions">
                    <el-button size="small" @click.stop="playRegion(region)">
                      <el-icon>
                        <VideoPlay />
                      </el-icon>
                      试听
                    </el-button>
                    <el-button size="small" type="danger" @click.stop="removeRegion(region)">
                      <el-icon>
                        <Delete />
                      </el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 对应关系映射 -->
        <div class="mapping-result">
          <h5>对应关系 ({{ mappings.length }} 个)</h5>
          <div class="mapping-list">
            <div v-for="(mapping, index) in mappings" :key="index" class="mapping-item">
              <div class="mapping-text">
                {{ index + 1 }}. {{ getTextSegmentContent(mapping.textIndex) }}
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-audio">
                区域{{ mapping.regionIndex + 1 }}
                ({{ formatTime(regions[mapping.regionIndex]?.start) }} -
                {{ formatTime(regions[mapping.regionIndex]?.end) }})
              </div>
              <el-button size="small" type="danger" @click="removeMapping(index)">
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <span class="segment-count">
            已上传 {{ audioFiles.length }} 个音频文件
            <span v-if="currentAudioFile">
              ，当前文件：{{ regions.length }} 个区域，{{ mappings.length }} 个对应
            </span>
          </span>
        </div>
        <div class="footer-right">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="confirmCutting" :disabled="!canConfirm" :loading="isUploading">
            {{ isUploading ? '处理中...' : '确认并保存' }}
          </el-button>
        </div>
      </div>
    </template>
  </standard-dialog>
</template>

<script setup>
import { ref, computed, watch, onUnmounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  UploadFilled,
  VideoPlay,
  VideoPause,
  Scissor,
  RefreshLeft,
  Delete,
  ZoomIn,
  ZoomOut,
  Loading
} from '@element-plus/icons-vue';
import StandardDialog from '../common/StandardDialog.vue';
import { getLanguageLabel } from '@/config/languages';
import { getSpeakerTagType } from '../nodes/ResourceNodeUtils';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  textSegments: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'confirm']);

// 对话框状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 多音频文件管理
const audioFiles = ref([]); // 所有音频文件列表
const currentAudioId = ref(null); // 当前使用的音频文件ID
const audioDuration = ref(0);
const isPlaying = ref(false);

// 计算当前音频文件
const currentAudioFile = computed(() => {
  return audioFiles.value.find(file => file.id === currentAudioId.value) || null;
});

// WaveSurfer 相关
const waveformRef = ref(null);
let wavesurfer = null;
let regionsPlugin = null;
const isWaveSurferLoading = ref(false);

// 区域和映射
const regions = ref([]);
const selectedTextIndex = ref(-1);
const selectedRegionIndex = ref(-1);
const mappings = ref([]);

// 上传状态
const isUploading = ref(false);

// 计算属性
const canConfirm = computed(() => {
  // 检查当前文件是否有对应关系
  const currentHasMappings = regions.value.length > 0 && mappings.value.length > 0;

  // 检查其他文件是否有对应关系
  const otherFilesHaveMappings = audioFiles.value.some(file =>
    file.mappings && file.mappings.length > 0
  );

  return currentHasMappings || otherFilesHaveMappings;
});

// 监听当前音频文件变化，当有文件时才初始化 WaveSurfer
watch(currentAudioFile, async (newFile) => {
  if (newFile && !wavesurfer && dialogVisible.value) {
    // 等待 DOM 更新完成
    await nextTick();
    await loadWaveSurfer();
  }
}, { flush: 'post' });

// 监听对话框关闭，清理资源
watch(dialogVisible, (newValue) => {
  if (!newValue) {
    // 对话框关闭时清理 WaveSurfer 实例
    if (wavesurfer) {
      wavesurfer.destroy();
      wavesurfer = null;
      regionsPlugin = null;
    }
  }
});

onUnmounted(() => {
  if (wavesurfer) {
    wavesurfer.destroy();
  }
});

// 动态加载 WaveSurfer.js
async function loadWaveSurfer() {
  if (wavesurfer) return;

  try {
    isWaveSurferLoading.value = true;

    const WaveSurfer = (await import('wavesurfer.js')).default;
    const RegionsPlugin = (await import('wavesurfer.js/dist/plugins/regions.js')).default;
    const TimelinePlugin = (await import('wavesurfer.js/dist/plugins/timeline.js')).default;

    await initWaveSurfer(WaveSurfer, RegionsPlugin, TimelinePlugin);

    ElMessage.success('音频处理器初始化完成');
  } catch (error) {
    console.error('加载 WaveSurfer.js 失败:', error);
    ElMessage.error('加载音频处理库失败: ' + error.message);
  } finally {
    isWaveSurferLoading.value = false;
  }
}

// 初始化 WaveSurfer
async function initWaveSurfer(WaveSurfer, RegionsPlugin, TimelinePlugin) {
  // 检查容器元素是否存在
  if (!waveformRef.value) {
    throw new Error('波形图容器元素未找到');
  }

  // 创建 regions 插件
  regionsPlugin = RegionsPlugin.create({
    dragSelection: {
      slop: 5
    }
  });

  // 创建 WaveSurfer 实例
  wavesurfer = WaveSurfer.create({
    container: waveformRef.value,
    waveColor: '#409eff',
    progressColor: '#67c23a',
    cursorColor: '#f56c6c',
    barWidth: 2,
    barRadius: 3,
    responsive: true,
    height: 100, // 更紧凑的波形图高度
    normalize: true,
    plugins: [
      regionsPlugin,
      TimelinePlugin.create({
        height: 20,
        insertPosition: 'beforebegin'
      })
    ]
  });

  // 绑定事件
  setupWaveSurferEvents();
}

// 设置 WaveSurfer 事件监听
function setupWaveSurferEvents() {
  if (!wavesurfer || !regionsPlugin) return;

  // 播放状态事件
  wavesurfer.on('play', () => {
    isPlaying.value = true;
  });

  wavesurfer.on('pause', () => {
    isPlaying.value = false;
  });

  wavesurfer.on('finish', () => {
    isPlaying.value = false;
  });

  wavesurfer.on('ready', () => {
    const duration = wavesurfer.getDuration();
    audioDuration.value = duration;

    // 同时更新文件数据中的时长
    if (currentAudioId.value) {
      const currentFile = audioFiles.value.find(f => f.id === currentAudioId.value);
      if (currentFile) {
        currentFile.duration = duration;
      }
    }

    console.log('WaveSurfer ready, duration:', duration);
    ElMessage.success(`音频加载完成 (${formatDuration(duration)})`);
  });

  // 添加错误处理
  wavesurfer.on('error', (error) => {
    console.error('WaveSurfer 错误:', error);
    ElMessage.error('音频加载失败: ' + error.message);
  });

  // 添加加载事件
  wavesurfer.on('loading', (percent) => {
    console.log('音频加载进度:', percent + '%');
  });

  // 区域事件
  regionsPlugin.on('region-created', (region) => {
    // 为新区域添加颜色和标识
    region.setOptions({
      color: getRandomRegionColor(),
      drag: true,
      resize: true
    });

    // 添加到区域列表
    regions.value.push({
      id: region.id,
      start: region.start,
      end: region.end,
      region: region
    });


  });

  regionsPlugin.on('region-updated', (region) => {
    // 更新区域信息
    const regionIndex = regions.value.findIndex(r => r.id === region.id);
    if (regionIndex !== -1) {
      regions.value[regionIndex].start = region.start;
      regions.value[regionIndex].end = region.end;
    }
  });

  regionsPlugin.on('region-removed', (region) => {
    // 从区域列表中移除
    const regionIndex = regions.value.findIndex(r => r.id === region.id);
    if (regionIndex !== -1) {
      regions.value.splice(regionIndex, 1);

      // 移除相关的映射并重新索引
      mappings.value = mappings.value
        .filter(mapping => mapping.regionIndex !== regionIndex)
        .map(mapping => ({
          ...mapping,
          regionIndex: mapping.regionIndex > regionIndex ? mapping.regionIndex - 1 : mapping.regionIndex
        }));
    }
  });

  regionsPlugin.on('region-clicked', (region) => {
    // 点击区域时选中
    const regionIndex = regions.value.findIndex(r => r.id === region.id);
    selectedRegionIndex.value = regionIndex;
  });


}

// 文件选择处理
async function handleFileSelect(file) {
  try {
    console.log('选择文件对象:', file);
    console.log('文件属性:', {
      name: file.name,
      size: file.size,
      type: file.type,
      raw: file.raw
    });

    // 获取实际的文件对象
    const actualFile = file.raw || file;
    const fileName = file.name || actualFile.name;
    const fileType = file.type || actualFile.type || '';
    const fileSize = file.size || actualFile.size || 0;

    console.log('实际文件信息:', {
      name: fileName,
      size: fileSize,
      type: fileType
    });

    // 检查文件类型（如果有的话）
    if (fileType && !fileType.startsWith('audio/')) {
      // 如果没有type信息，通过文件扩展名判断
      const audioExtensions = ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'];
      const hasAudioExtension = audioExtensions.some(ext =>
        fileName.toLowerCase().endsWith(ext)
      );

      if (!hasAudioExtension) {
        ElMessage.error('请选择音频文件');
        return;
      }
    }

    // 检查文件大小 (50MB限制)
    if (fileSize > 50 * 1024 * 1024) {
      ElMessage.error('音频文件大小不能超过50MB');
      return;
    }

    // 生成唯一ID
    const fileId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 添加到音频文件列表
    const audioFileData = {
      id: fileId,
      name: fileName,
      file: actualFile,
      duration: 0,
      regions: [], // 每个文件独立的区域
      mappings: [] // 每个文件独立的映射
    };

    console.log('创建音频文件数据:', audioFileData);
    audioFiles.value.push(audioFileData);

    // 如果是第一个文件，自动设为当前文件
    if (audioFiles.value.length === 1) {
      await switchToAudioFile(fileId);
    } else {
      ElMessage.success(`音频文件 "${fileName}" 已添加，点击"使用"按钮切换到该文件`);
    }

  } catch (error) {
    console.error('添加音频文件失败:', error);
    ElMessage.error('添加音频文件失败: ' + error.message);
  }
}

// 切换到指定音频文件
async function switchToAudioFile(fileId) {
  try {
    const targetFile = audioFiles.value.find(f => f.id === fileId);
    if (!targetFile) {
      ElMessage.error('音频文件不存在');
      return;
    }

    // 保存当前文件的状态
    if (currentAudioId.value) {
      saveCurrentFileState();
    }

    // 切换到新文件
    currentAudioId.value = fileId;

    // 确保 WaveSurfer 已初始化
    if (!wavesurfer) {
      await loadWaveSurfer();
    }

    // 验证音频文件
    await validateAudioFile(targetFile.file);

    // 加载音频文件
    console.log('开始加载音频文件:', {
      name: targetFile.name,
      size: targetFile.file.size,
      type: targetFile.file.type || '未知类型',
      fileObject: targetFile.file
    });
    await wavesurfer.loadBlob(targetFile.file);

    // 恢复该文件的状态
    restoreFileState(targetFile);

    ElMessage.success(`已切换到音频文件: ${targetFile.name}`);

  } catch (error) {
    console.error('切换音频文件失败:', error);
    ElMessage.error('切换音频文件失败: ' + error.message);
  }
}

// 保存当前文件的状态
function saveCurrentFileState() {
  const currentFile = audioFiles.value.find(f => f.id === currentAudioId.value);
  if (currentFile) {
    currentFile.regions = [...regions.value];
    currentFile.mappings = [...mappings.value];
    currentFile.duration = audioDuration.value;
  }
}

// 恢复文件状态
function restoreFileState(fileData) {
  // 清除当前状态
  clearAllRegions();
  mappings.value = [];

  // 恢复区域
  if (fileData.regions && fileData.regions.length > 0) {
    fileData.regions.forEach(regionData => {
      if (regionsPlugin) {
        const region = regionsPlugin.addRegion({
          start: regionData.start,
          end: regionData.end,
          color: getRandomRegionColor(),
          drag: true,
          resize: true
        });

        regions.value.push({
          id: region.id,
          start: regionData.start,
          end: regionData.end,
          region: region
        });
      }
    });
  }

  // 恢复映射
  if (fileData.mappings && fileData.mappings.length > 0) {
    mappings.value = [...fileData.mappings];
  }

  // 恢复时长
  if (fileData.duration > 0) {
    audioDuration.value = fileData.duration;
  }
}

// 删除音频文件
function removeAudioFile(fileId) {
  ElMessageBox.confirm(
    '确定要删除这个音频文件吗？相关的区域和对应关系也会被删除。',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 如果删除的是当前文件，需要切换到其他文件或清空
    if (currentAudioId.value === fileId) {
      const otherFile = audioFiles.value.find(f => f.id !== fileId);
      if (otherFile) {
        switchToAudioFile(otherFile.id);
      } else {
        // 没有其他文件了，清空状态
        currentAudioId.value = null;
        clearAllRegions();
        mappings.value = [];
        audioDuration.value = 0;

        // 销毁 WaveSurfer
        if (wavesurfer) {
          wavesurfer.destroy();
          wavesurfer = null;
          regionsPlugin = null;
        }
      }
    }

    // 从列表中删除
    audioFiles.value = audioFiles.value.filter(f => f.id !== fileId);
    ElMessage.success('音频文件已删除');
  }).catch(() => {
    // 用户取消
  });
}

// 切换到文件管理界面
function switchToFileManagement() {
  // 保存当前状态
  if (currentAudioId.value) {
    saveCurrentFileState();
  }

  // 清空当前选择，显示文件管理界面
  currentAudioId.value = null;

  // 销毁 WaveSurfer
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
    regionsPlugin = null;
  }
}

// 验证音频文件
async function validateAudioFile(file) {
  try {
    // 创建临时音频上下文来验证文件
    const audioContext = new AudioContext();
    const arrayBuffer = await file.arrayBuffer();

    // 尝试解码音频数据
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    console.log('音频文件验证成功:', {
      duration: audioBuffer.duration,
      sampleRate: audioBuffer.sampleRate,
      channels: audioBuffer.numberOfChannels
    });

    // 关闭音频上下文
    audioContext.close();

    return audioBuffer;
  } catch (error) {
    console.error('音频文件验证失败:', error);
    throw new Error('音频文件格式不支持或已损坏: ' + error.message);
  }
}

// 等待 WaveSurfer 初始化完成
async function waitForWaveSurfer() {
  let attempts = 0;
  const maxAttempts = 50; // 最多等待5秒

  while (!wavesurfer && attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 100));
    attempts++;
  }

  if (!wavesurfer) {
    throw new Error('WaveSurfer 初始化超时');
  }
}

// 播放/暂停控制
function playPause() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  wavesurfer.playPause();
}

// 在当前播放位置添加区域
function addRegionAtCursor() {
  if (!wavesurfer || !regionsPlugin) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentTime = wavesurfer.getCurrentTime();
  const duration = wavesurfer.getDuration();

  // 创建一个2秒的区域
  const regionDuration = Math.min(2, duration - currentTime);
  const start = currentTime;
  const end = currentTime + regionDuration;

  if (end > duration) {
    ElMessage.warning('无法在当前位置添加区域');
    return;
  }

  regionsPlugin.addRegion({
    start,
    end,
    color: getRandomRegionColor(),
    drag: true,
    resize: true
  });

  ElMessage.success(`已添加区域: ${formatTime(start)} - ${formatTime(end)}`);
}

// 清除所有区域
function clearAllRegions() {
  if (regionsPlugin) {
    regionsPlugin.clearRegions();
  }

  regions.value = [];
  mappings.value = [];
  ElMessage.success('已清除所有区域');
}

// 放大波形图
function zoomIn() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentZoom = wavesurfer.options.minPxPerSec || 50;
  wavesurfer.zoom(Math.min(currentZoom * 1.5, 500));
}

// 缩小波形图
function zoomOut() {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const currentZoom = wavesurfer.options.minPxPerSec || 50;
  wavesurfer.zoom(Math.max(currentZoom / 1.5, 10));
}

// 获取随机区域颜色
function getRandomRegionColor() {
  const colors = [
    'rgba(255, 0, 0, 0.3)',
    'rgba(0, 255, 0, 0.3)',
    'rgba(0, 0, 255, 0.3)',
    'rgba(255, 255, 0, 0.3)',
    'rgba(255, 0, 255, 0.3)',
    'rgba(0, 255, 255, 0.3)',
    'rgba(255, 165, 0, 0.3)',
    'rgba(128, 0, 128, 0.3)'
  ];

  return colors[Math.floor(Math.random() * colors.length)];
}

function selectTextSegment(index) {
  selectedTextIndex.value = index;
}

function selectRegion(index) {
  selectedRegionIndex.value = index;
}

// 播放指定区域
function playRegion(regionData) {
  if (!wavesurfer) {
    ElMessage.warning('音频处理器未初始化');
    return;
  }

  const region = regionData.region;
  const start = region ? region.start : regionData.start;
  const end = region ? region.end : regionData.end;

  // 停止当前播放
  if (wavesurfer.isPlaying()) {
    wavesurfer.pause();
  }

  // 设置播放位置到区域开始
  wavesurfer.setTime(start);

  // 开始播放
  wavesurfer.play();

  // 清理之前的监听器
  if (wavesurfer._currentStopPlayback) {
    wavesurfer.un('audioprocess', wavesurfer._currentStopPlayback);
  }

  // 在区域结束时自动暂停
  const stopPlayback = () => {
    if (wavesurfer.getCurrentTime() >= end) {
      wavesurfer.pause();
      wavesurfer.un('audioprocess', stopPlayback);
      wavesurfer._currentStopPlayback = null;
    }
  };

  wavesurfer._currentStopPlayback = stopPlayback;
  wavesurfer.on('audioprocess', stopPlayback);

  ElMessage.success(`播放区域: ${formatTime(start)} - ${formatTime(end)}`);
}

// 删除指定区域
function removeRegion(regionData) {
  if (!regionsPlugin) {
    ElMessage.warning('区域插件未初始化');
    return;
  }

  const region = regionData.region;
  if (region && region.remove) {
    region.remove(); // 会触发 region-removed 事件，自动处理清理逻辑
    ElMessage.success('已删除区域');
  }
}

function autoMapping() {
  // 自动按顺序对应
  mappings.value = [];
  const maxMappings = Math.min(props.textSegments.length, regions.value.length);

  for (let i = 0; i < maxMappings; i++) {
    mappings.value.push({
      textIndex: i,
      regionIndex: i
    });
  }

  ElMessage.success(`已自动对应 ${maxMappings} 个片段`);
}

// 手动建立对应关系
function createManualMapping() {
  if (selectedTextIndex.value === -1 || selectedRegionIndex.value === -1) {
    ElMessage.warning('请先选择文本分句和音频区域');
    return;
  }

  // 检查是否已经存在对应关系
  const existingTextMapping = mappings.value.find(m => m.textIndex === selectedTextIndex.value);
  const existingRegionMapping = mappings.value.find(m => m.regionIndex === selectedRegionIndex.value);

  if (existingTextMapping) {
    ElMessage.warning(`文本${selectedTextIndex.value + 1}已经对应了区域${existingTextMapping.regionIndex + 1}`);
    return;
  }

  if (existingRegionMapping) {
    ElMessage.warning(`区域${selectedRegionIndex.value + 1}已经对应了文本${existingRegionMapping.textIndex + 1}`);
    return;
  }

  // 创建新的对应关系
  mappings.value.push({
    textIndex: selectedTextIndex.value,
    regionIndex: selectedRegionIndex.value
  });

  ElMessage.success(`已建立对应：文本${selectedTextIndex.value + 1} ↔ 区域${selectedRegionIndex.value + 1}`);

  // 清除选择状态
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;
}

// 清除所有对应关系
function clearAllMappings() {
  mappings.value = [];
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;
  ElMessage.success('已清除所有对应关系');
}

// 检查是否已映射
function isMapped(type, index) {
  if (type === 'text') {
    return mappings.value.some(m => m.textIndex === index);
  } else if (type === 'region') {
    return mappings.value.some(m => m.regionIndex === index);
  }
  return false;
}

// 获取映射的区域索引
function getMappedRegionIndex(textIndex) {
  const mapping = mappings.value.find(m => m.textIndex === textIndex);
  return mapping ? mapping.regionIndex : -1;
}

// 获取映射的文本索引
function getMappedTextIndex(regionIndex) {
  const mapping = mappings.value.find(m => m.regionIndex === regionIndex);
  return mapping ? mapping.textIndex : -1;
}

function removeMapping(index) {
  mappings.value.splice(index, 1);
}

function getTextSegmentContent(textIndex) {
  return props.textSegments[textIndex]?.content || '';
}

// 删除兼容性函数，直接使用 resetAllState

async function confirmCutting() {
  // 检查是否有任何音频文件和对应关系
  const hasAnyMappings = audioFiles.value.some(file =>
    file.mappings && file.mappings.length > 0
  ) || mappings.value.length > 0;

  if (!hasAnyMappings) {
    ElMessage.warning('请先完成音频区域选择和文本对应');
    return;
  }

  try {
    isUploading.value = true;

    // 保存当前文件状态
    if (currentAudioId.value) {
      saveCurrentFileState();
    }

    // 合并所有音频文件的切割结果
    const allExportedSegments = await exportAllAudioSegments();

    ElMessage.success(`音频切割完成，共处理 ${allExportedSegments.length} 个音频片段`);
    emit('confirm', {
      audioSegments: allExportedSegments
    });

    // 清理状态
    resetAllState();
    dialogVisible.value = false;

  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败: ' + error.message);
  } finally {
    isUploading.value = false;
  }
}

// 导出所有音频文件的片段
async function exportAllAudioSegments() {
  const allExportedSegments = [];

  // 处理每个音频文件
  for (const audioFileData of audioFiles.value) {
    if (!audioFileData.mappings || audioFileData.mappings.length === 0) {
      continue; // 跳过没有映射的文件
    }

    try {
      // 临时切换到该文件进行处理
      const segments = await exportAudioSegmentsForFile(audioFileData);
      allExportedSegments.push(...segments);
    } catch (error) {
      console.error(`处理音频文件 ${audioFileData.name} 失败:`, error);
      ElMessage.warning(`音频文件 "${audioFileData.name}" 处理失败: ${error.message}`);
    }
  }

  return allExportedSegments;
}

// 为指定音频文件导出片段
async function exportAudioSegmentsForFile(audioFileData) {
  const exportedSegments = [];

  // 创建临时的音频上下文来处理该文件
  const audioContext = new AudioContext();
  const arrayBuffer = await audioFileData.file.arrayBuffer();
  const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

  // 为每个映射关系导出音频片段
  for (let i = 0; i < audioFileData.mappings.length; i++) {
    const mapping = audioFileData.mappings[i];
    const region = audioFileData.regions[mapping.regionIndex];
    const textSegment = props.textSegments[mapping.textIndex];

    if (!region || !textSegment) continue;

    try {
      // 切割音频片段
      const segmentBuffer = extractAudioSegment(audioBuffer, region.start, region.end);

      // 转换为WAV格式
      const audioBlob = audioBufferToWav(segmentBuffer);

      // 上传音频片段
      const uploadedUrl = await uploadAudioBlob(audioBlob, textSegment.id);

      exportedSegments.push({
        start: region.start,
        end: region.end,
        duration: region.end - region.start,
        url: uploadedUrl,
        textSegmentId: textSegment.id,
        textContent: textSegment.content,
        speaker: textSegment.speaker,
        language: textSegment.language,
        sourceAudioFile: audioFileData.name // 记录来源音频文件
      });

    } catch (error) {
      console.error(`导出音频片段 ${i + 1} 失败:`, error);
      // 失败的片段跳过，不添加到结果中
      ElMessage.warning(`音频片段 ${i + 1} 导出失败: ${error.message}`);
    }
  }

  // 关闭音频上下文
  audioContext.close();

  return exportedSegments;
}

// 删除兼容性函数，只保留多文件版本

// 从音频缓冲区中提取片段
function extractAudioSegment(audioBuffer, startTime, endTime) {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(startTime * sampleRate);
  const endSample = Math.floor(endTime * sampleRate);
  const segmentLength = endSample - startSample;

  // 创建新的音频缓冲区
  const segmentBuffer = new AudioContext().createBuffer(
    audioBuffer.numberOfChannels,
    segmentLength,
    sampleRate
  );

  // 复制音频数据
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    const originalData = audioBuffer.getChannelData(channel);
    const segmentData = segmentBuffer.getChannelData(channel);

    for (let i = 0; i < segmentLength; i++) {
      segmentData[i] = originalData[startSample + i] || 0;
    }
  }

  return segmentBuffer;
}

// 将音频缓冲区转换为WAV格式
function audioBufferToWav(buffer) {
  const length = buffer.length;
  const numberOfChannels = buffer.numberOfChannels;
  const sampleRate = buffer.sampleRate;
  const bytesPerSample = 2; // 16-bit
  const blockAlign = numberOfChannels * bytesPerSample;
  const byteRate = sampleRate * blockAlign;
  const dataSize = length * blockAlign;
  const bufferSize = 44 + dataSize;

  const arrayBuffer = new ArrayBuffer(bufferSize);
  const view = new DataView(arrayBuffer);

  // WAV文件头
  const writeString = (offset, string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  writeString(0, 'RIFF');
  view.setUint32(4, bufferSize - 8, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, numberOfChannels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, byteRate, true);
  view.setUint16(32, blockAlign, true);
  view.setUint16(34, 16, true);
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);

  // 音频数据
  let offset = 44;
  for (let i = 0; i < length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }
  }

  return new Blob([arrayBuffer], { type: 'audio/wav' });
}

// 上传音频片段
async function uploadAudioBlob(audioBlob, segmentId) {
  const formData = new FormData();
  formData.append('file', audioBlob, `audio_segment_${segmentId}.wav`);
  formData.append('type', 'manual'); // 标记为手动切割音频
  formData.append('language', 'auto'); // 语言自动检测

  const response = await fetch('/api/audio/upload', {
    method: 'POST',
    body: formData
  });

  if (!response.ok) {
    throw new Error(`上传失败: ${response.statusText}`);
  }

  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || '上传失败');
  }

  return result.data.url;
}

// 重置所有状态
function resetAllState() {
  // 清空音频文件列表
  audioFiles.value = [];
  currentAudioId.value = null;

  // 清空当前状态
  regions.value = [];
  mappings.value = [];
  selectedTextIndex.value = -1;
  selectedRegionIndex.value = -1;
  audioDuration.value = 0;
  isPlaying.value = false;

  // 销毁 WaveSurfer
  if (wavesurfer) {
    wavesurfer.destroy();
    wavesurfer = null;
    regionsPlugin = null;
  }
}

function handleDialogClose() {
  const hasAnyData = audioFiles.value.length > 0 ||
                     mappings.value.length > 0 ||
                     audioFiles.value.some(file =>
                       (file.mappings && file.mappings.length > 0) ||
                       (file.regions && file.regions.length > 0)
                     );

  if (hasAnyData) {
    ElMessageBox.confirm(
      '确定要关闭吗？未保存的更改将会丢失。',
      '关闭确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      resetAllState();
      dialogVisible.value = false;
    }).catch(() => {
      // 用户取消
    });
  } else {
    dialogVisible.value = false;
  }
}

// 工具函数
function formatTime(seconds) {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

function formatDuration(seconds) {
  return `${seconds.toFixed(1)}秒`;
}
</script>

<style scoped>
.audio-cutting-container {
  min-height: 500px;
}

/* 音频文件管理区域样式 */
.audio-files-section {
  margin-bottom: 1rem; /* 减少下边距 */
}

.audio-files-section h4 {
  margin: 0 0 0.5rem 0; /* 减少下边距 */
  color: #303133;
  font-size: 1rem; /* 减小字体 */
}

.audio-files-list {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.files-grid {
  background-color: #f8f9fa;
  padding: 0.75rem; /* 减少内边距 */
}

.audio-file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem; /* 减少内边距 */
  margin-bottom: 0.375rem; /* 减少下边距 */
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.audio-file-item:last-child {
  margin-bottom: 0;
}

.audio-file-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.audio-file-item:hover {
  border-color: #c0c4cc;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.file-icon {
  color: #409eff;
  font-size: 1.125rem;
}

.file-name {
  font-weight: 500;
  color: #303133;
}

.file-duration {
  color: #909399;
  font-size: 0.875rem;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-label {
  color: #67c23a;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0 0.5rem;
}

.add-audio-section {
  padding: 0.75rem; /* 减少内边距 */
  border-top: 1px solid #e4e7ed;
}

.upload-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 当前音频信息样式 */
.current-audio-info {
  margin-bottom: 0.5rem; /* 减少下边距 */
  padding: 0.75rem; /* 减少内边距 */
  background-color: #f0f9ff;
  border: 1px solid #e0f2fe;
  border-radius: 0.5rem;
}

.audio-file-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.audio-file-info .file-name {
  font-weight: 600;
  color: #303133;
  flex: 1;
}

.audio-file-info .file-duration {
  color: #606266;
}

.waveform-container {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
  margin: 1rem 0;
  /* 移除固定高度，让内容自适应 */
}

.waveform {
  background-color: #f8f9fa;
  position: relative;
  /* 移除固定高度，让 WaveSurfer 控制高度 */
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* 强制 WaveSurfer 容器占用完整空间 */
.waveform > div {
  margin: 0 !important;
  padding: 0 !important;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #606266;
  font-size: 1rem;
}

.loading-indicator .el-icon {
  font-size: 2rem;
  color: #409eff;
}

.waveform-controls {
  padding: 1rem;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
}

.control-buttons {
  margin-bottom: 0.75rem;
}

.control-tips {
  text-align: center;
  padding: 0.5rem;
  background-color: #f0f9ff;
  border-radius: 0.25rem;
  border: 1px solid #e0f2fe;
}

/* 底部对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.footer-left {
  flex: 1;
}

.footer-right {
  display: flex;
  gap: 0.75rem;
}

.segment-count {
  color: #606266;
  font-size: 0.875rem;
}

/* 映射区域样式 */
.mapping-section {
  margin-top: 1.5rem;
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.mapping-header h4 {
  margin: 0;
  color: #303133;
  font-size: 1.125rem;
}

.mapping-actions {
  display: flex;
  gap: 0.5rem;
}

.selection-hint {
  color: #67c23a;
  font-size: 0.875rem;
  font-weight: normal;
}

.mapping-indicator {
  color: #67c23a;
  font-size: 0.75rem;
  font-weight: bold;
  margin-left: 0.5rem;
}

.mapping-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.text-segments,
.audio-regions {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.text-segments h5,
.audio-regions h5 {
  margin: 0;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
}

.segment-list {
  max-height: 300px;
  overflow-y: auto;
}

.text-segment-item,
.audio-segment-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.text-segment-item:hover,
.audio-segment-item:hover {
  background-color: #f8f9fa;
}

.text-segment-item.selected,
.audio-segment-item.selected {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.text-segment-item.mapped,
.audio-segment-item.mapped {
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.text-segment-item.mapped.selected,
.audio-segment-item.mapped.selected {
  background-color: #d9f7be;
  border-color: #73d13d;
}

.segment-number {
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: bold;
}

.segment-content {
  flex: 1;
  min-width: 0;
}

.segment-text {
  font-size: 0.875rem;
  color: #303133;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.segment-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.segment-language {
  font-size: 0.75rem;
  color: #909399;
}

.segment-time {
  font-size: 0.875rem;
  color: #303133;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.segment-duration {
  font-size: 0.75rem;
  color: #909399;
  margin-bottom: 0.5rem;
}

.region-actions {
  display: flex;
  gap: 0.5rem;
}

/* 映射结果样式 */
.mapping-result {
  border: 1px solid #e4e7ed;
  border-radius: 0.5rem;
  overflow: hidden;
}

.mapping-result h5 {
  margin: 0;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
}

.mapping-list {
  max-height: 200px;
  overflow-y: auto;
}

.mapping-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.mapping-item:last-child {
  border-bottom: none;
}

.mapping-text {
  flex: 2;
  font-size: 0.875rem;
  color: #303133;
}

.mapping-arrow {
  flex-shrink: 0;
  color: #909399;
  font-weight: bold;
}

.mapping-audio {
  flex: 1;
  font-size: 0.875rem;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mapping-content {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .footer-left {
    text-align: center;
  }

  .footer-right {
    justify-content: center;
  }
}
</style>
