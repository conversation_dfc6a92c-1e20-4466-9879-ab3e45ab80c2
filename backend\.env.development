PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USERNAME=chengxiakuan
DB_PASSWORD=f6zx3qc1
DB_NAME=echo-lab

# 阿里云OSS配置
OSS_REGION=oss-cn-hongkong
OSS_ACCESS_KEY=LTAI5tLVFTqpVkTtLCvroWbg
OSS_SECRET_KEY=******************************
OSS_BUCKET=echolab
OSS_ENDPOINT=oss-cn-hongkong.aliyuncs.com

# OSS代理配置
# 开发环境中，前端Vite服务器会代理/oss-resources路径的请求到阿里云OSS
# 生产环境中，Nginx会代理这些请求
SERVER_DOMAIN=http://localhost:3001
OSS_PROXY_PATH=/oss-resources

# TTS API配置
GOOGLE_API_KEY=AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw
BAIDU_APP_ID=*********
BAIDU_API_KEY=REi2kJMug7PysLSXD98MV3W6
BAIDU_SECRET_KEY=b6vdIzTNm1ZnIso1OtNE9dt4wScbtoA4

# 翻译API配置
BAIDU_TRANSLATE_APP_ID=*****************
BAIDU_TRANSLATE_API_KEY=4i6TCIrFNb9ss1YYIa9I

# Yahoo! Japan Jlp API配置
YAHOO_CLIENT_ID=dj00aiZpPWgwdmhnSk84c1R6MiZzPWNvbnN1bWVyc2VjcmV0Jng9YzY-

# 邮件配置 - 通用
EMAIL_FROM=<EMAIL>

# 阿里云邮件配置
ALIYUN_ACCESS_KEY=LTAI5tLVFTqpVkTtLCvroWbg
ALIYUN_SECRET_KEY=******************************
ALIYUN_EMAIL_ACCOUNT=<EMAIL>
ALIYUN_EMAIL_REGION=cn-hangzhou

# JWT配置
JWT_SECRET=echo-lab-secret-key-2025
JWT_EXPIRES_IN=30d

# 验证码配置
CODE_EXPIRES_IN=600
CODE_LENGTH=6
MAX_ATTEMPTS=5
IP_LOCK_TIME=3600
