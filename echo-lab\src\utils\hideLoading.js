/**
 * 隐藏初始加载画面
 * 在应用初始化完成后调用此函数
 */
export function hideInitialLoading() {
  // 获取加载元素
  const loadingElement = document.getElementById('loading');
  
  // 如果元素存在，添加淡出效果并移除
  if (loadingElement) {
    // 添加淡出样式
    loadingElement.style.transition = 'opacity 0.5s ease';
    loadingElement.style.opacity = '0';
    
    // 等待动画完成后移除元素
    setTimeout(() => {
      if (loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
      }
    }, 500);
  }
}
