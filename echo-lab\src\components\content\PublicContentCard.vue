<!--
  公开内容卡片组件
  用于在首页展示公开的视频内容
-->
<template>
  <div class="public-content-card" @click="handleCardClick">
    <!-- 卡片缩略图 -->
    <div class="card-thumbnail">
      <img v-if="content.thumbnailUrl" :src="content.thumbnailUrl" alt="缩略图" />
      <div v-else class="thumbnail-placeholder">
        <el-icon :size="32">
          <VideoPlay />
        </el-icon>
      </div>

      <!-- 操作按钮区域 -->
      <div class="card-actions" @click.stop>
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <h3 class="card-title">{{ content.name }}</h3>
      <p v-if="content.description" class="card-description">{{ content.description }}</p>
      <div class="card-footer">
        <div class="card-tags">
          <el-tag v-for="tag in parseTags(content.tags)" :key="tag" size="small" class="tag-item">
            {{ tag }}
          </el-tag>
        </div>
        <span class="card-date">{{ formatDate(content.updatedAt) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoPlay } from '@element-plus/icons-vue';

const props = defineProps({
  content: {
    type: Object,
    required: true
  }
});

// 格式化日期为相对时间
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);

  // 根据时间差返回不同的格式
  if (diffSec < 60) {
    return '刚刚';
  } else if (diffMin < 60) {
    return `${diffMin}分钟前`;
  } else if (diffHour < 24) {
    return `${diffHour}小时前`;
  } else if (diffDay < 30) {
    return `${diffDay}天前`;
  } else if (diffMonth < 12) {
    return `${diffMonth}个月前`;
  } else {
    return `${diffYear}年前`;
  }
};

// 解析标签
const parseTags = (tagsStr) => {
  if (!tagsStr) return [];
  if (Array.isArray(tagsStr)) return tagsStr;
  return tagsStr.split(',').filter(tag => tag.trim()).slice(0, 2); // 最多显示2个标签
};

// 处理卡片点击（新标签页）
const handleCardClick = () => {
  window.open(`/player/${props.content.id}`, '_blank');
};
</script>

<style scoped>
.public-content-card {
  background-color: #ffffff;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.public-content-card:hover {
  transform: translateY(-0.25rem);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

/* 移除悬停时显示播放按钮的效果 */
/* .public-content-card:hover .play-overlay {
  opacity: 1;
} */

.card-thumbnail {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  background-color: #f5f7fa;
  overflow: hidden;
}

.card-thumbnail img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  background-color: #f5f7fa;
}

/* 操作按钮区域 */
.card-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  z-index: 2;
  display: flex;
  gap: 0.25rem;
}

.card-content {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  margin: 0 0 0.5rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.card-description {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  flex: 1;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.card-tags {
  display: flex;
  gap: 0.25rem;
}

.tag-item {
  max-width: 5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-date {
  font-size: 0.75rem;
  color: #909399;
}

/* 播放按钮覆盖层样式已移除 */

/* 移动设备样式 - 使用类名而非媒体查询 */
.mobile-device .public-content-card {
  box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.06);
}

.mobile-device .card-content {
  padding: 0.5rem;
}

.mobile-device .card-title {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
  -webkit-line-clamp: 1;
  /* 移动端只显示一行标题 */
}

.mobile-device .card-description {
  display: none;
  /* 两列布局中隐藏描述 */
}

.mobile-device .card-footer {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.mobile-device .card-tags {
  width: 100%;
  overflow: hidden;
}

.mobile-device .tag-item {
  max-width: 3rem;
  font-size: 0.625rem;
  padding: 0 0.25rem;
}

.mobile-device .card-date {
  font-size: 0.625rem;
  width: 100%;
  text-align: right;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
