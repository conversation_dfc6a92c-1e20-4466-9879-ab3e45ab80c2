<script setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { loadUserConfig, updateUserConfig } from '@/services/userConfigService';
import ContentDisplay from './ContentDisplay.vue';
import { isMobileDevice } from '@/utils/deviceDetector';

// 接收属性
const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },
  audioBuffer: {
    type: Object,
    default: null
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  loop: {
    type: Boolean,
    default: false
  }
});

// 事件
const emit = defineEmits(['timeupdate', 'loading-change', 'open-settings', 'index-change', 'toggle-playlist', 'boundary-reached']);

// 音频元素 - 真实数据源
const audioContext = ref(null);
const audioSource = ref(null);
const audioElement = ref(null);

// UI状态 - 从音频元素同步
const uiState = reactive({
  currentTime: 0,
  duration: 0,
  isPlaying: false,
  volume: 1,
  isMuted: false,
  playbackRate: 1,
  currentIndex: 0,
  showPlaylist: true, // 播放列表显示状态
});

// 基准字体大小 - 根据设备类型不同
// 设置不同设备的基准字体大小
const BASE_FONT_SIZE = isMobileDevice() ? 1.0 : 1.8; // 手机端1.0rem，电脑端1.8rem
console.log('设备类型:', isMobileDevice() ? '移动端' : '电脑端', '基准字体大小:', BASE_FONT_SIZE, 'rem');

// 文本样式设置
const textStyle = reactive({
  fontSizePercent: 100, // 默认为100%
  get fontSize() {
    const size = BASE_FONT_SIZE * (this.fontSizePercent / 100);
    return size;
  },
  color: '#FFFFFF', // 默认颜色
  textShadow: '0.0625rem 0.0625rem 0.125rem rgba(0, 0, 0, 0.7)'
});

// 字体大小百分比范围
const fontSizeRange = {
  min: 80,  // 80%
  max: 200, // 200%
  step: 5    // 5%步长
};

// 字体颜色选项
const colorOptions = [
  { value: '#FFFFFF', label: '白色' },
  { value: '#FFD700', label: '金色' },
  { value: '#FF6B6B', label: '红色' },
  { value: '#4ECDC4', label: '青色' },
  { value: '#9ACD32', label: '绿色' }
];

// 控制状态
const controlState = reactive({
  isUserInteracting: false,
  isDraggingProgress: false, // 是否正在拖动进度条
  showControls: false,
  showVolumeSlider: false,
  isVolumeSliderActive: false,
  showSpeedMenu: false,
  showMobileMenu: false,
  showTextStyleDialog: false, // 文本样式设置对话框显示状态
  showAutoShutdownPanel: false, // 定时关闭面板显示状态
  volumeSliderTimeout: null,
  controlsTimeout: null,
});

// 定时关闭状态
const autoShutdownState = reactive({
  enabled: false, // 是否启用定时关闭
  minutes: 15, // 定时关闭时间（分钟），默认15分钟
  remainingSeconds: 0, // 剩余秒数
  timerId: null, // 定时器ID
});

// 动画帧ID
let animationFrameId = null;

// 上次更新时间
let lastUpdateTime = 0;

// 进度条值 - 用于v-model绑定
const progressValue = ref(0);

// 监听 uiState.currentTime 和 uiState.duration 的变化，更新 progressValue
watch([() => uiState.currentTime, () => uiState.duration], ([currentTime, duration]) => {
  // 只在用户没有交互时更新 progressValue
  if (!controlState.isUserInteracting && !controlState.isDraggingProgress && duration) {
    progressValue.value = (currentTime / duration) * 100;
  }
}, { immediate: true });

// 当前时间线项
const currentItem = computed(() => {
  if (!props.timeline || props.timeline.length === 0) {
    console.log('当前时间线为空或不存在');
    return null;
  }

  const currentIdx = uiState.currentIndex;
  if (currentIdx >= 0 && currentIdx < props.timeline.length) {
    // 获取当前项
    const item = props.timeline[currentIdx];
    return item;
  }

  console.log('当前索引超出范围:', currentIdx, '时间线长度:', props.timeline.length);
  return null;
});

// 当前内容是否为图片
const isImageContent = computed(() => {
  return currentItem.value?.contentType === 'image';
});

// 当前内容的图片URL（如果是图片内容）
const imageUrl = computed(() => {
  if (!isImageContent.value || !currentItem.value) return '';
  return currentItem.value.imageUrl || '';
});

// UI更新循环
const startUIUpdateLoop = () => {
  if (animationFrameId !== null) return;

  const updateUI = () => {
    if (!audioElement.value) {
      animationFrameId = null;
      return;
    }

    // 只在用户没有交互时从音频元素同步状态
    if (!controlState.isUserInteracting) {
      // 更新当前时间和播放状态
      uiState.currentTime = audioElement.value.currentTime;
      uiState.isPlaying = !audioElement.value.paused;

      // 确保总时长已设置（可能在初始化时未正确设置）
      if (audioElement.value.duration && audioElement.value.duration !== Infinity && uiState.duration === 0) {
        uiState.duration = audioElement.value.duration;
        console.log('从音频元素更新总时长:', uiState.duration);
      }

      // 更新当前内容索引（只在非用户交互时）
      updateCurrentIndex();

      // 发送时间更新事件给父组件
      emit('timeupdate', uiState.currentTime);
    }

    // 继续下一帧
    animationFrameId = requestAnimationFrame(updateUI);
  };

  // 立即开始第一帧
  animationFrameId = requestAnimationFrame(updateUI);
  console.log('UI更新循环已启动');
};

// 停止UI更新循环
const stopUIUpdateLoop = () => {
  if (animationFrameId !== null) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

// 更新当前内容索引
const updateCurrentIndex = () => {
  if (!props.timeline || props.timeline.length === 0) return;

  // 查找当前时间对应的内容项
  const currentTime = uiState.currentTime;
  let newIndex = -1;

  for (let i = 0; i < props.timeline.length; i++) {
    const item = props.timeline[i];
    const startTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;
    const duration = item.actualDuration !== undefined ? item.actualDuration : item.duration;

    if (currentTime >= startTime && currentTime < startTime + duration) {
      newIndex = i;
      break;
    }
  }

  // 如果找到有效索引且与当前索引不同，更新索引
  if (newIndex !== -1 && newIndex !== uiState.currentIndex) {
    uiState.currentIndex = newIndex;
    emit('index-change', newIndex);
  }
};

// 初始化音频上下文
const initAudio = () => {
  console.log('初始化音频上下文，isLoading =', props.isLoading);
  // 如果正在加载或者没有音频缓冲区，不进行初始化
  if (props.isLoading || !props.audioBuffer) {
    console.log('音频尚未准备好或正在加载中');
    return;
  }

  try {
    // 创建音频上下文
    // 使用标准 AudioContext API
    audioContext.value = new AudioContext();

    // 创建音频源
    const source = audioContext.value.createBufferSource();
    source.buffer = props.audioBuffer;
    source.connect(audioContext.value.destination);

    // 保存音频源
    audioSource.value = source;

    // 设置音频元素
    if (audioElement.value) {
      try {
        // 将AudioBuffer转换为WAV格式（使用优化选项）
        const wavData = audioBufferToWav(props.audioBuffer, {
          // 使用单声道以减小文件大小
          channels: 1,
          // 使用固定的24kHz采样率
          targetSampleRate: 24000
        });
        const blob = new Blob([wavData], { type: 'audio/wav' });
        const url = URL.createObjectURL(blob);

        // 设置音频元素
        audioElement.value.src = url;
        audioElement.value.volume = uiState.isMuted ? 0 : uiState.volume;
        audioElement.value.playbackRate = uiState.playbackRate;

        // 设置总时长
        uiState.duration = props.audioBuffer.duration || 0;

        console.log('音频元素已初始化，时长:', uiState.duration);
      } catch (error) {
        console.error('创建音频URL失败:', error);
        ElMessage.error('初始化音频播放器失败，请刷新页面重试');
      }
    }
  } catch (error) {
    console.error('初始化音频上下文失败:', error);
    ElMessage.error('初始化音频播放器失败，请刷新页面重试');
  }
};

// 不再需要显示/隐藏封面的函数，因为我们现在使用统一的内容类型处理

// 检查音频是否已合成
const checkAudioAvailability = (showWarning = true) => {
  // 检查音频元素是否存在
  if (!audioElement.value) {
    if (showWarning) {
      ElMessage.warning('音频尚未加载完成，请稍候');
    }
    return false;
  }

  // 检查音频是否有效（有源和时长）
  if (!audioElement.value.src || audioElement.value.duration === 0 || audioElement.value.duration === Infinity) {
    if (showWarning) {
      ElMessage.warning('音频文件尚未合成完成，请稍候');
    }
    return false;
  }

  return true;
};

// 统一的播放状态管理方法
const updatePlayState = (isPlaying, options = {}) => {
  // 更新播放状态
  uiState.isPlaying = isPlaying;

  // 处理控制区域显示/隐藏
  if (isPlaying) {
    // 播放状态：如果没有特殊指示，则隐藏控制区域
    if (!options.keepControlsVisible) {
      // 立即隐藏控制区域
      controlState.showControls = false;

      // 清除任何可能存在的隐藏定时器
      if (controlState.controlsTimeout) {
        clearTimeout(controlState.controlsTimeout);
        controlState.controlsTimeout = null;
      }
    } else {
      // 如果需要保持控制区域可见一段时间
      resetControlsTimeout();
    }
  } else {
    // 暂停状态：始终显示控制区域
    controlState.showControls = true;

    // 清除任何可能存在的隐藏定时器
    if (controlState.controlsTimeout) {
      clearTimeout(controlState.controlsTimeout);
      controlState.controlsTimeout = null;
    }
  }

  // 返回当前播放状态，方便链式调用
  return isPlaying;
};

// 播放/暂停
const togglePlay = () => {
  // 检查音频是否可用，显示警告消息
  if (!checkAudioAvailability(true)) {
    return;
  }

  try {
    // 播放/暂停逻辑
    if (uiState.isPlaying) {
      // 如果正在播放，则暂停
      audioElement.value.pause();
      updatePlayState(false);
    } else {
      // 如果已暂停，则开始播放
      const playPromise = audioElement.value.play();

      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            console.log('开始播放音频');
            updatePlayState(true);
          })
          .catch(error => {
            console.error('播放音频失败:', error);
            ElMessage.error('播放失败，请刷新页面重试');
            updatePlayState(false);
          });
      }
    }
  } catch (error) {
    console.error('播放/暂停操作失败:', error);
    ElMessage.error('播放操作失败，请刷新页面重试');
  }
};

// 开始拖动进度条
const startProgressDrag = () => {
  console.log('开始拖动进度条');
  // 标记正在拖动进度条
  controlState.isDraggingProgress = true;
  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 注意：不再暂停播放，允许在拖动时继续播放
};

// 结束拖动进度条
const endProgressDrag = () => {
  console.log('结束拖动进度条');
  // 延迟后恢复自动更新，增加延迟时间以确保不会立即被timeupdate事件覆盖
  setTimeout(() => {
    controlState.isDraggingProgress = false;
    controlState.isUserInteracting = false;
  }, 1000);
};

// 处理进度条变化
const handleProgressChange = (value) => {
  if (!audioElement.value || !uiState.duration || value === undefined || value === null) return;

  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 计算新的时间点
  const newTime = (value / 100) * uiState.duration;

  // 只在拖动结束或者拖动过程中每隔一段时间更新一次音频位置，避免频繁更新
  if (!controlState.isDraggingProgress || Date.now() - lastUpdateTime > 200) {
    lastUpdateTime = Date.now();

    console.log(`进度条变化: ${Number(value).toFixed(0)}%, 跳转到时间: ${Number(newTime).toFixed(2)}s`);

    // 获取当前播放状态
    const wasPlaying = uiState.isPlaying;

    // 设置新的时间点
    audioElement.value.currentTime = newTime;
    uiState.currentTime = newTime;

    // 不再直接设置 progressValue.value，避免循环更新
    // progressValue.value = value;

    // 更新当前内容索引
    updateCurrentIndex();

    // 如果之前是播放状态但当前已暂停，恢复播放
    if (wasPlaying && audioElement.value.paused) {
      audioElement.value.play().catch(error => {
        console.error('跳转后恢复播放失败:', error);
      });
    }
  }
}

// 切换静音
const toggleMute = () => {
  uiState.isMuted = !uiState.isMuted;

  if (audioElement.value) {
    audioElement.value.volume = uiState.isMuted ? 0 : uiState.volume;
  }
};

// 设置音量
const setVolume = (value) => {
  // 标记用户正在交互
  controlState.isUserInteracting = true;

  uiState.volume = value;

  if (audioElement.value) {
    audioElement.value.volume = uiState.isMuted ? 0 : value;
  }

  // 如果设置了音量，取消静音
  if (value > 0) {
    uiState.isMuted = false;
  } else {
    uiState.isMuted = true;
  }

  // 短暂延迟后恢复自动更新
  setTimeout(() => {
    controlState.isUserInteracting = false;
  }, 200);
};

// 设置播放速度
const setPlaybackRate = (value) => {
  // 标记用户正在交互
  controlState.isUserInteracting = true;

  uiState.playbackRate = value;

  if (audioElement.value) {
    audioElement.value.playbackRate = value;
  }

  // 短暂延迟后恢复自动更新
  setTimeout(() => {
    controlState.isUserInteracting = false;
  }, 200);
};

// 全屏功能已移除

// 格式化时间
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 处理文本样式变更
const handleTextStyleChange = (newStyle) => {
  if (newStyle.fontSizePercent !== undefined) {
    // 更新百分比值
    textStyle.fontSizePercent = Math.max(fontSizeRange.min, Math.min(fontSizeRange.max, newStyle.fontSizePercent));
  }

  if (newStyle.color !== undefined) {
    textStyle.color = newStyle.color;
  }
  if (newStyle.textShadow !== undefined) {
    textStyle.textShadow = newStyle.textShadow;
  }

};

// 切换播放列表显示/隐藏
const togglePlaylist = (value) => {
  // 更新本地状态
  uiState.showPlaylist = value;

  // 保存到用户配置
  updateUserConfig('player.showPlaylist', value);

  // 发送切换播放列表事件
  emit('toggle-playlist');
};

// 跳转到指定索引
const jumpToIndex = (index) => {
  if (!props.timeline || index < 0 || index >= props.timeline.length) return;

  // 检查音频是否可用，不显示警告消息
  if (!checkAudioAvailability(false)) {
    // 音频未合成好时，什么也不做
    return;
  }

  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 获取目标时间
  const item = props.timeline[index];
  const startTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;

  // 获取当前播放状态
  const wasPlaying = uiState.isPlaying;

  // 记录跳转操作
  console.log(`跳转到索引 ${index}, 时间: ${startTime.toFixed(2)}s`);

  // 如果正在播放，先暂停
  if (wasPlaying) {
    audioElement.value.pause();
  }

  // 更新UI状态
  uiState.currentIndex = index;

  // 设置新的时间点
  audioElement.value.currentTime = startTime;
  uiState.currentTime = startTime;

  // 立即更新进度条值，确保UI与实际位置同步
  if (uiState.duration) {
    progressValue.value = (startTime / uiState.duration) * 100;
  }

  // 添加一个小延迟，确保音频已经准备好
  setTimeout(() => {
    // 无论之前是否在播放，都开始播放
    // 这确保了点击列表项、前进后退按钮时都会自动开始播放
    audioElement.value.play().catch(error => {
      console.error('跳转后开始播放失败:', error);
      ElMessage.error('播放失败，请刷新页面重试');
      updatePlayState(false);
      return;
    });

    // 更新播放状态（不保持控制区域可见）
    updatePlayState(true);

    // 延迟后恢复自动更新，增加延迟时间以确保不会立即被timeupdate事件覆盖
    setTimeout(() => {
      controlState.isUserInteracting = false;
    }, 1000);
  }, 50);
};

// 跳转到当前句子开头
const jumpToCurrentSentenceStart = () => {
  if (!props.timeline || uiState.currentIndex < 0 || uiState.currentIndex >= props.timeline.length) return;

  // 检查音频是否可用，显示警告消息
  if (!checkAudioAvailability(true)) {
    return;
  }

  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 获取当前句子的起始时间
  const item = props.timeline[uiState.currentIndex];
  const startTime = item.actualStartTime !== undefined ? item.actualStartTime : item.startTime;

  // 获取当前播放状态
  const wasPlaying = uiState.isPlaying;

  // 记录跳转操作
  console.log(`跳转到当前句子(索引 ${uiState.currentIndex})的开头, 时间: ${startTime.toFixed(2)}s`);

  // 如果正在播放，先暂停
  if (wasPlaying) {
    audioElement.value.pause();
  }

  // 设置新的时间点
  audioElement.value.currentTime = startTime;
  uiState.currentTime = startTime;

  // 立即更新进度条值，确保UI与实际位置同步
  if (uiState.duration) {
    progressValue.value = (startTime / uiState.duration) * 100;
  }

  // 添加一个小延迟，确保音频已经准备好
  setTimeout(() => {
    // 无论之前是否在播放，都开始播放
    audioElement.value.play().catch(error => {
      console.error('跳转后开始播放失败:', error);
      ElMessage.error('播放失败，请刷新页面重试');
      updatePlayState(false);
      return;
    });

    // 更新播放状态（不保持控制区域可见）
    updatePlayState(true);

    // 延迟后恢复自动更新，增加延迟时间以确保不会立即被timeupdate事件覆盖
    setTimeout(() => {
      controlState.isUserInteracting = false;
    }, 1000);
  }, 50);
};

// 跳转到指定时间
const jumpToTime = (time) => {
  // 检查音频是否可用，显示警告消息
  if (!checkAudioAvailability(true)) {
    return;
  }

  // 标记用户正在交互
  controlState.isUserInteracting = true;

  // 获取当前播放状态
  const wasPlaying = uiState.isPlaying;

  // 记录跳转操作
  console.log(`跳转到指定时间: ${time.toFixed(2)}s`);

  // 如果正在播放，先暂停
  if (wasPlaying) {
    audioElement.value.pause();
  }

  // 设置新的时间点
  audioElement.value.currentTime = time;
  uiState.currentTime = time;

  // 立即更新进度条值，确保UI与实际位置同步
  if (uiState.duration) {
    progressValue.value = (time / uiState.duration) * 100;
  }

  // 添加一个小延迟，确保音频已经准备好
  setTimeout(() => {
    // 无论之前是否在播放，都开始播放
    audioElement.value.play().catch(error => {
      console.error('跳转后开始播放失败:', error);
      ElMessage.error('播放失败，请刷新页面重试');
      updatePlayState(false);
      return;
    });

    // 更新播放状态（不保持控制区域可见）
    updatePlayState(true);

    // 延迟后恢复自动更新，增加延迟时间以确保不会立即被timeupdate事件覆盖
    setTimeout(() => {
      controlState.isUserInteracting = false;
    }, 1000);
  }, 50);
};

// 处理音频结束
const handleAudioEnded = () => {
  // 重置时间
  uiState.currentTime = 0;
  if (audioElement.value) {
    audioElement.value.currentTime = 0;
  }

  // 循环播放逻辑
  if (props.loop && audioElement.value) {
    audioElement.value.play().then(() => {
      updatePlayState(true);
    }).catch(error => {
      console.error('循环播放失败:', error);
      updatePlayState(false);
    });
    return;
  }

  // 非循环模式，更新为暂停状态
  updatePlayState(false);
  console.log('音频播放结束');
};

// 监听音频时间更新 - 用于更新进度条
const handleTimeUpdate = () => {
  // 如果用户正在交互或者正在拖动进度条，不更新进度条值
  if (controlState.isUserInteracting || controlState.isDraggingProgress) return;

  // 更新进度条值
  if (audioElement.value && audioElement.value.duration) {
    progressValue.value = (audioElement.value.currentTime / audioElement.value.duration) * 100;
  }
};

// 监听音频元数据加载完成
const handleMetadataLoaded = () => {
  console.log('音频元数据已加载，duration =', audioElement.value?.duration);

  // 设置总时长
  if (audioElement.value && audioElement.value.duration && audioElement.value.duration !== Infinity) {
    uiState.duration = audioElement.value.duration;
    console.log('从元数据更新总时长:', uiState.duration);

    // 重置进度条位置
    progressValue.value = 0;
  }
};

// 监听音频可以播放
const handleCanPlay = () => {
  console.log('音频可以播放，readyState =', audioElement.value?.readyState);

  // 确保总时长已设置
  if (audioElement.value && audioElement.value.duration && audioElement.value.duration !== Infinity) {
    uiState.duration = audioElement.value.duration;
    console.log('音频可播放，更新总时长:', uiState.duration);

    // 确保进度条位置与当前时间同步
    if (uiState.currentTime > 0) {
      progressValue.value = (uiState.currentTime / uiState.duration) * 100;
    } else {
      progressValue.value = 0;
    }
  }

  // 通知父组件可以隐藏加载提示
  emit('loading-change', false);
};

// 全屏功能已移除

// 写入字符串
const writeString = (view, offset, string) => {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
};

/**
 * 将AudioBuffer转换为WAV格式（优化大小）
 * @param {AudioBuffer} buffer - 音频缓冲区
 * @param {Object} options - 转换选项
 * @returns {ArrayBuffer} WAV格式的ArrayBuffer
 */
const audioBufferToWav = (buffer, options = {}) => {
  // 默认配置
  const config = {
    // 使用固定的24kHz采样率
    targetSampleRate: options.targetSampleRate || 24000,
    // 使用单声道以减小文件大小
    channels: options.channels || 1,
    // 使用16位PCM格式
    bitDepth: options.bitDepth || 16
  };

  // 获取原始参数
  const originalNumOfChan = buffer.numberOfChannels;
  const originalSampleRate = buffer.sampleRate;

  // 计算降采样比例
  const resampleRatio = config.targetSampleRate / originalSampleRate;

  // 计算新的采样数
  const newLength = Math.floor(buffer.length * resampleRatio);

  // 计算每个样本的字节数
  const bytesPerSample = config.bitDepth / 8;

  // 计算数据长度
  const dataLength = newLength * config.channels * bytesPerSample;

  // 创建 WAV 文件头
  const buffer2 = new ArrayBuffer(44 + dataLength);
  const view = new DataView(buffer2);

  // RIFF chunk descriptor
  writeString(view, 0, 'RIFF');
  view.setUint32(4, 36 + dataLength, true);
  writeString(view, 8, 'WAVE');

  // FMT sub-chunk
  writeString(view, 12, 'fmt ');
  view.setUint32(16, 16, true); // 子块大小
  view.setUint16(20, 1, true); // PCM 格式
  view.setUint16(22, config.channels, true); // 通道数
  view.setUint32(24, config.targetSampleRate, true); // 采样率
  view.setUint32(28, config.targetSampleRate * config.channels * bytesPerSample, true); // 字节率
  view.setUint16(32, config.channels * bytesPerSample, true); // 块对齐
  view.setUint16(34, config.bitDepth, true); // 位深度

  // 数据子块
  writeString(view, 36, 'data');
  view.setUint32(40, dataLength, true);

  // 写入 PCM 样本（降采样和混合通道）
  let offsetPos = 44;
  for (let i = 0; i < newLength; i++) {
    // 计算原始采样索引
    const originalIndex = Math.floor(i / resampleRatio);

    // 如果使用单声道，混合所有通道
    if (config.channels === 1) {
      // 混合所有通道的样本
      let mixedSample = 0;
      for (let channel = 0; channel < originalNumOfChan; channel++) {
        mixedSample += buffer.getChannelData(channel)[originalIndex];
      }
      // 平均值
      mixedSample /= originalNumOfChan;

      // 限制范围
      mixedSample = Math.max(-1, Math.min(1, mixedSample));

      // 将 -1.0 到 1.0 的浮点数转换为整数
      const value = mixedSample < 0 ? mixedSample * 0x8000 : mixedSample * 0x7FFF;

      // 写入样本
      view.setInt16(offsetPos, value, true);
      offsetPos += 2;
    } else {
      // 保留原始通道数（最多到config.channels）
      for (let channel = 0; channel < Math.min(originalNumOfChan, config.channels); channel++) {
        // 获取样本
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[originalIndex]));

        // 将 -1.0 到 1.0 的浮点数转换为整数
        const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;

        // 写入样本
        view.setInt16(offsetPos, value, true);
        offsetPos += 2;
      }
    }
  }

  return buffer2;
};

// 监听props.currentIndex变化 - 只在用户主动操作时执行跳转
watch(() => props.currentIndex, (newIndex) => {
  if (newIndex === uiState.currentIndex) return;

  // 跳转到新索引
  jumpToIndex(newIndex);
});

// 监听加载状态变化
watch(() => props.isLoading, (isLoading) => {
  console.log('加载状态变化:', isLoading);

  // 如果加载完成，尝试初始化音频
  if (!isLoading && props.audioBuffer) {
    initAudio();
  }
});

// 生命周期钩子
onMounted(() => {
  // 加载用户配置
  const userConfig = loadUserConfig();

  // 应用播放列表显示状态
  if (userConfig.player && userConfig.player.showPlaylist !== undefined) {
    uiState.showPlaylist = userConfig.player.showPlaylist;
  }

  // 应用文本样式设置
  if (userConfig.player && userConfig.player.textStyle) {
    // 应用百分比值
    if (userConfig.player.textStyle.fontSizePercent !== undefined) {
      textStyle.fontSizePercent = userConfig.player.textStyle.fontSizePercent;
    }

    textStyle.color = userConfig.player.textStyle.color || textStyle.color;
    textStyle.textShadow = userConfig.player.textStyle.textShadow || textStyle.textShadow;
  }

  // 定时关闭功能不再从本地配置加载

  // 只有在不加载状态时才初始化音频
  if (!props.isLoading && props.audioBuffer) {
    initAudio();
  }

  // 全屏功能已移除

  // 开始UI更新循环
  startUIUpdateLoop();

  // 初始显示控制元素
  controlState.showControls = true;

  // 初始化后立即检查是否可以播放
  if (!props.isLoading && audioElement.value && audioElement.value.readyState >= 2) {
    emit('loading-change', false);
  }

  // 同步初始索引
  uiState.currentIndex = props.currentIndex;
});

onUnmounted(() => {
  // 停止UI更新循环
  stopUIUpdateLoop();

  // 停止定时关闭计时器
  if (autoShutdownState.timerId) {
    clearInterval(autoShutdownState.timerId);
    autoShutdownState.timerId = null;
  }

  // 清除控制定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
  }

  // 全屏功能已移除

  // 释放音频资源
  if (audioSource.value) {
    try {
      audioSource.value.disconnect();
    } catch (error) {
      console.error('断开音频源连接失败:', error);
    }
  }

  if (audioContext.value) {
    try {
      audioContext.value.close();
    } catch (error) {
      console.error('关闭音频上下文失败:', error);
    }
  }

  // 释放Blob URL
  if (audioElement.value && audioElement.value.src) {
    try {
      URL.revokeObjectURL(audioElement.value.src);
    } catch (error) {
      console.error('释放Blob URL失败:', error);
    }
  }
});

// 显示控制元素（仅在鼠标移动时调用）
const showControlsElements = () => {
  // 显示控制元素
  controlState.showControls = true;

  // 清除之前的定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
    controlState.controlsTimeout = null;
  }

  // 如果正在播放，设置自动隐藏定时器
  if (uiState.isPlaying) {
    controlState.controlsTimeout = setTimeout(() => {
      if (uiState.isPlaying) {
        controlState.showControls = false;
      }
    }, 2000);
  }
};

// 立即隐藏控制元素（鼠标离开时调用）
const hideControlsImmediately = () => {
  // 清除定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
    controlState.controlsTimeout = null;
  }

  // 如果正在播放，立即隐藏控制区域
  if (uiState.isPlaying) {
    controlState.showControls = false;
  }
};

// 重置控制元素隐藏定时器
const resetControlsTimeout = () => {
  // 显示控制元素
  controlState.showControls = true;

  // 清除之前的定时器
  if (controlState.controlsTimeout) {
    clearTimeout(controlState.controlsTimeout);
  }

  // 如果正在播放，设置新的定时器
  if (uiState.isPlaying) {
    controlState.controlsTimeout = setTimeout(() => {
      // 只有在播放状态下才隐藏控制区域
      if (uiState.isPlaying) {
        controlState.showControls = false;
      }
    }, 2000);
  }
};

// 跳转到上一个句子 - 复杂逻辑，先回到当前内容开头，再跳到上一句
const jumpToPreviousSentence = () => {
  if (!props.timeline || props.timeline.length === 0 || !audioElement.value) return false;

  // 获取当前索引和当前时间
  const currentIdx = uiState.currentIndex;
  const currentTime = uiState.currentTime;

  // 获取当前句子的起始时间
  const currentItem = props.timeline[currentIdx];
  const currentStartTime = currentItem.actualStartTime !== undefined ? currentItem.actualStartTime : currentItem.startTime;

  // 判断当前是否已经接近句子开头（0.3秒内）
  const isNearStart = Math.abs(currentTime - currentStartTime) < 0.3;

  // 如果不在句子开头附近，先跳转到当前句子开头
  if (!isNearStart) {
    jumpToCurrentSentenceStart();
    return true;
  }
  // 如果已经在当前句子开头附近，且不是第一句，则跳转到上一句
  else if (currentIdx > 0) {
    jumpToIndex(currentIdx - 1);
    return true;
  }
  // 如果已经是第一句的开头，触发边界反馈
  else {
    console.log('已经是第一句的开头');
    // 触发一个事件，让父组件知道已经到达边界
    emit('boundary-reached', 'start');
    return false;
  }
};

// 跳转到下一个句子 - 简化逻辑，只跳转到下一句
const jumpToNextSentence = () => {
  if (!props.timeline || props.timeline.length === 0 || !audioElement.value) return false;

  // 获取当前索引
  const currentIdx = uiState.currentIndex;

  // 如果不是最后一句，跳转到下一句
  if (currentIdx < props.timeline.length - 1) {
    // 跳转到下一个索引
    jumpToIndex(currentIdx + 1);
    return true;
  }
  // 如果已经是最后一句，触发边界反馈
  else {
    console.log('已经是最后一句');
    // 触发一个事件，让父组件知道已经到达边界
    emit('boundary-reached', 'end');
    return false;
  }
};

// 检查是否可以跳转到上一个句子
const canJumpToPreviousSentence = computed(() => {
  if (!props.timeline || props.timeline.length === 0) return false;
  return uiState.currentIndex > 0;
});

// 检查是否可以跳转到下一个句子
const canJumpToNextSentence = computed(() => {
  if (!props.timeline || props.timeline.length === 0) return false;
  return uiState.currentIndex < props.timeline.length - 1;
});

// 启动定时关闭计时器
const startAutoShutdownTimer = () => {
  // 先清除可能存在的定时器
  stopAutoShutdownTimer();

  if (!autoShutdownState.enabled) return;

  // 设置剩余秒数
  autoShutdownState.remainingSeconds = autoShutdownState.minutes * 60;

  // 创建定时器，每秒更新一次
  autoShutdownState.timerId = setInterval(() => {
    // 减少剩余秒数
    autoShutdownState.remainingSeconds--;

    // 如果剩余秒数为0，执行关闭操作
    if (autoShutdownState.remainingSeconds <= 0) {
      executeAutoShutdown();
    }
  }, 1000);

  console.log(`定时关闭计时器已启动，将在${autoShutdownState.minutes}分钟后关闭`);
};

// 停止定时关闭计时器
const stopAutoShutdownTimer = () => {
  if (autoShutdownState.timerId) {
    clearInterval(autoShutdownState.timerId);
    autoShutdownState.timerId = null;
    console.log('定时关闭计时器已停止');
  }
};

// 执行定时关闭操作
const executeAutoShutdown = () => {
  // 停止定时器
  stopAutoShutdownTimer();

  // 暂停播放
  if (audioElement.value && uiState.isPlaying) {
    audioElement.value.pause();
    updatePlayState(false);
  }

  // 显示提示
  ElMessage.success('已按设定时间自动停止播放');

  // 禁用定时关闭功能
  autoShutdownState.enabled = false;
};

// 设置定时关闭时间
const setAutoShutdownTime = (minutes) => {
  autoShutdownState.minutes = minutes;

  // 如果定时器已启动，重新启动
  if (autoShutdownState.enabled) {
    startAutoShutdownTimer();
  }
};

// 切换定时关闭状态
const toggleAutoShutdown = () => {
  autoShutdownState.enabled = !autoShutdownState.enabled;

  if (autoShutdownState.enabled) {
    startAutoShutdownTimer();
    ElMessage.success(`已启用定时关闭，将在${autoShutdownState.minutes}分钟后停止播放`);
  } else {
    stopAutoShutdownTimer();
    ElMessage.info('已取消定时关闭');
  }
};

// 格式化剩余时间显示
const formatRemainingTime = () => {
  if (!autoShutdownState.enabled) return '';

  const minutes = Math.floor(autoShutdownState.remainingSeconds / 60);
  const seconds = autoShutdownState.remainingSeconds % 60;

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 导出音频 - 简化版本，直接下载当前播放的音频
const exportAudio = () => {
  if (!audioElement.value || !audioElement.value.src) {
    ElMessage.warning('没有可导出的音频');
    return;
  }

  try {
    // 直接使用当前音频元素的src（Blob URL）
    const audioSrc = audioElement.value.src;

    // 创建下载链接
    const link = document.createElement('a');
    link.href = audioSrc;
    link.download = `audio_export_${new Date().getTime()}.wav`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理DOM（不释放URL，因为它仍在被音频元素使用）
    document.body.removeChild(link);

    ElMessage.success('音频导出成功');
  } catch (error) {
    console.error('导出音频失败:', error);
    ElMessage.error('导出音频失败，请稍后重试');
  }
};

// 处理内容元素点击
const handleContentElementClick = (event) => {
  // 触发父组件的事件
  emit('content-element-click', event);
};

// 暴露方法给父组件
defineExpose({
  uiState,
  controlState,
  textStyle,
  autoShutdownState,
  togglePlay,
  jumpToIndex,
  jumpToCurrentSentenceStart,
  jumpToTime,
  jumpToPreviousSentence,
  jumpToNextSentence,
  canJumpToPreviousSentence,
  canJumpToNextSentence,
  setVolume,
  setPlaybackRate,
  // toggleFullscreen 已移除
  handleProgressChange,
  startProgressDrag,
  endProgressDrag,
  toggleMute,
  handleTextStyleChange,
  togglePlaylist,
  formatTime,
  progressValue,
  showControlsElements,
  hideControlsImmediately,
  resetControlsTimeout,
  currentItem,
  isImageContent,
  imageUrl,
  audioElement,
  exportAudio,
  // 定时关闭相关方法
  toggleAutoShutdown,
  setAutoShutdownTime,
  formatRemainingTime,
  startAutoShutdownTimer,
  stopAutoShutdownTimer,
  fontSizeRange,
  colorOptions
});
</script>

<template>
  <div class="video-player-base">
    <!-- 音频元素 -->
    <audio ref="audioElement" @ended="handleAudioEnded" @timeupdate="handleTimeUpdate" @canplay="handleCanPlay"
      @loadedmetadata="handleMetadataLoaded" crossorigin="anonymous" style="display: none;">
    </audio>

    <!-- 内容显示区域 -->
    <div class="content-container">
      <!-- 根据内容类型显示不同内容 -->
      <template v-if="isImageContent">
        <!-- 图片内容显示 -->
        <div class="image-display" @click="togglePlay">
          <img :src="imageUrl" alt="图片内容" class="content-image" crossorigin="anonymous" />
          <!-- 播放按钮已移至具体播放器组件中实现 -->
        </div>
      </template>
      <template v-else>
        <!-- 文本内容显示 -->
        <ContentDisplay :item="currentItem" :copyright="props.videoConfig.copyright" :textStyle="{
          fontSize: textStyle.fontSize,
          color: textStyle.color,
          textShadow: textStyle.textShadow,
          backgroundColor: 'transparent'
        }" @content-element-click="handleContentElementClick" />
      </template>
    </div>
  </div>
</template>

<style scoped>
.video-player-base {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #000;
}

.content-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  background-color: inherit;
}

/* 图片内容显示样式 */
.image-display {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  cursor: pointer;
}

.content-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* 播放按钮样式已移至具体播放器组件中 */

/* 调试信息样式 */
.debug-info {
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 0.5rem;
  font-size: 0.75rem;
  z-index: 10;
  max-width: 100%;
  word-break: break-all;
}
</style>
