import { defineStore } from "pinia";
import publicContentService from "@/services/publicContentService";

/**
 * 公开内容存储
 * 管理公开内容的状态
 */
export const usePublicContentStore = defineStore("publicContent", {
  state: () => ({
    loading: false,
    error: null,
    contents: [],
    hotContents: [],
    latestContents: [],
    pagination: {
      total: 0,
      page: 1,
      pageSize: 100, // 设置一个较大的值，确保一次性加载所有内容
    },
    filters: {
      search: "",
      tag: "",
      sortBy: "updatedAt",
      sortOrder: "desc",
    },
  }),

  getters: {
    /**
     * 获取过滤后的内容
     */
    filteredContents(state) {
      let result = [...state.contents];

      // 搜索过滤
      if (state.filters.search) {
        const searchQuery = state.filters.search.toLowerCase();
        result = result.filter(
          (item) =>
            item.name.toLowerCase().includes(searchQuery) ||
            (item.description &&
              item.description.toLowerCase().includes(searchQuery))
        );
      }

      // 标签过滤
      if (state.filters.tag) {
        result = result.filter((item) => {
          if (!item.tags) return false;
          const tags = Array.isArray(item.tags)
            ? item.tags
            : item.tags.split(",").map((tag) => tag.trim());
          return tags.includes(state.filters.tag);
        });
      }

      // 排序
      result.sort((a, b) => {
        const key = state.filters.sortBy;
        const orderFactor = state.filters.sortOrder === "desc" ? -1 : 1;
        return orderFactor * (a[key] < b[key] ? -1 : 1);
      });

      // 更新总数
      this.pagination.total = result.length;

      return result;
    },
  },

  actions: {
    /**
     * 获取公开内容列表
     */
    async fetchPublicContents() {
      this.loading = true;
      try {
        const response = await publicContentService.getPublicContents({
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          sortBy: this.filters.sortBy,
          sortOrder: this.filters.sortOrder,
        });

        if (response && response.success) {
          this.contents = response.contents || [];
          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "获取公开内容列表失败");
        }
      } catch (err) {
        console.error("获取公开内容列表失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取热门内容
     */
    async fetchHotContents(limit = 6) {
      try {
        const response = await publicContentService.getHotContents(limit);
        if (response && response.success) {
          this.hotContents = response.contents || [];
        } else {
          throw new Error(response?.error || "获取热门内容失败");
        }
      } catch (err) {
        console.error("获取热门内容失败:", err);
      }
    },

    /**
     * 获取最新内容
     */
    async fetchLatestContents(limit = 6) {
      try {
        const response = await publicContentService.getLatestContents(limit);
        if (response && response.success) {
          this.latestContents = response.contents || [];
        } else {
          throw new Error(response?.error || "获取最新内容失败");
        }
      } catch (err) {
        console.error("获取最新内容失败:", err);
      }
    },

    /**
     * 更新过滤条件
     */
    updateFilters(filters) {
      this.filters = { ...this.filters, ...filters };
    },

    /**
     * 更新分页
     */
    updatePagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination };
      this.fetchPublicContents();
    },

    /**
     * 搜索内容
     */
    async searchContents(keyword) {
      this.loading = true;
      try {
        const response = await publicContentService.searchContents(keyword);
        if (response && response.success) {
          this.contents = response.contents || [];
          if (response.pagination) {
            this.pagination = {
              ...this.pagination,
              ...response.pagination,
            };
          }
        } else {
          throw new Error(response?.error || "搜索内容失败");
        }
      } catch (err) {
        console.error("搜索内容失败:", err);
        this.error = err.message;
      } finally {
        this.loading = false;
      }
    },
  },
});
