/**
 * 播放器配置管理 Composable
 * 统一管理播放器配置的保存、加载、模板匹配等逻辑
 */
import { ref, computed } from "vue";
import { useTemplateStore } from "@/stores/templateStore";
import {
  validatePlaybackConfig,
  normalizePlaybackConfig,
  comparePlaybackConfigs,
  createDefaultPlaybackConfig,
  optimizePlaybackConfig,
} from "@/utils/playbackStrategy";

export function usePlayerConfig(contentId) {
  const templateStore = useTemplateStore();

  // 配置状态
  const settings = ref({ sections: [] });
  const currentTemplate = ref(null);
  const serverConfig = ref({ sections: [] });

  // 存储键名
  const storageKeys = computed(() => ({
    settings: `player_settings_${contentId.value}`,
    template: `player_template_${contentId.value}`,
  }));

  /**
   * 深拷贝工具
   */
  const deepClone = (obj) => {
    return JSON.parse(JSON.stringify(obj));
  };

  /**
   * 保存配置到localStorage
   */
  const saveToStorage = () => {
    try {
      const keys = storageKeys.value;

      // 保存设置配置
      localStorage.setItem(keys.settings, JSON.stringify(settings.value));
      console.log("已保存设置到localStorage:", keys.settings, settings.value);

      // 保存模板ID
      if (currentTemplate.value) {
        localStorage.setItem(keys.template, currentTemplate.value.id);
        console.log(
          "已保存模板ID到localStorage:",
          keys.template,
          currentTemplate.value.id
        );
      } else {
        localStorage.removeItem(keys.template);
        console.log("已清除模板ID从localStorage:", keys.template);
      }
    } catch (error) {
      console.error("保存配置到localStorage失败:", error);
    }
  };

  /**
   * 从localStorage加载配置
   */
  const loadFromStorage = () => {
    try {
      const keys = storageKeys.value;
      const savedSettings = localStorage.getItem(keys.settings);
      const savedTemplateId = localStorage.getItem(keys.template);

      console.log("从localStorage加载配置:", {
        settingsKey: keys.settings,
        templateKey: keys.template,
        savedSettings: savedSettings ? "found" : "not found",
        savedTemplateId: savedTemplateId || "none",
      });

      return {
        settings: savedSettings ? JSON.parse(savedSettings) : null,
        templateId: savedTemplateId,
      };
    } catch (error) {
      console.error("从localStorage加载配置失败:", error);
      return {
        settings: null,
        templateId: null,
      };
    }
  };

  /**
   * 清除存储
   */
  const clearStorage = () => {
    const keys = storageKeys.value;
    localStorage.removeItem(keys.settings);
    localStorage.removeItem(keys.template);
  };

  /**
   * 检查设置是否与模板匹配（使用播放策略工具）
   */
  const isSettingsMatchTemplate = (
    template,
    settingsToCheck = settings.value
  ) => {
    if (!template?.config) return false;
    return comparePlaybackConfigs(template.config, settingsToCheck);
  };

  /**
   * 智能更新模板状态
   */
  const updateTemplateState = () => {
    if (!currentTemplate.value) return;

    const isMatch = isSettingsMatchTemplate(currentTemplate.value);
    if (!isMatch) {
      console.log("设置已修改，清除模板状态");
      currentTemplate.value = null;
      const keys = storageKeys.value;
      localStorage.removeItem(keys.template);
    }
  };

  /**
   * 应用默认配置（使用播放策略工具）
   */
  const applyDefaultConfig = () => {
    const defaultConfig = createDefaultPlaybackConfig(serverConfig.value);
    settings.value = defaultConfig;
    currentTemplate.value = null;
    console.log("应用默认配置:", settings.value);
  };

  /**
   * 应用模板（只加载到内存，不保存到localStorage）
   */
  const applyTemplate = async (template) => {
    try {
      console.log("应用模板:", template.name);

      // 1. 应用模板配置到内存
      const appliedConfig = templateStore.applyTemplateToContent(
        serverConfig.value,
        template
      );
      settings.value = appliedConfig;

      // 2. 设置当前模板（临时状态）
      currentTemplate.value = template;

      // 注意：不保存到localStorage，只有在用户点击"应用设置"时才保存

      // 3. 增加模板使用次数
      if (template.type === "user") {
        templateStore.useTemplate(template.id).catch(console.error);
      }

      console.log("模板应用成功（仅内存）:", template.name);
      return true;
    } catch (error) {
      console.error("应用模板失败:", error);
      return false;
    }
  };

  /**
   * 智能加载配置
   */
  const smartLoadConfig = async () => {
    try {
      console.log("开始智能加载配置...");

      // 1. 从localStorage加载保存的数据
      const savedData = loadFromStorage();

      // 2. 如果有保存的用户设置，使用用户设置
      if (savedData.settings) {
        console.log("找到保存的用户设置，正在应用...");
        settings.value = savedData.settings;

        // 3. 如果同时有模板ID，恢复模板状态
        if (savedData.templateId) {
          console.log("检查保存的模板ID:", savedData.templateId);
          try {
            const savedTemplate = templateStore.templateById(
              savedData.templateId
            );
            if (savedTemplate) {
              currentTemplate.value = savedTemplate;
              console.log("已恢复模板状态:", savedTemplate.name);
            } else {
              // 模板不存在，清除模板状态
              console.log("模板不存在，清除模板状态");
              currentTemplate.value = null;
              const keys = storageKeys.value;
              localStorage.removeItem(keys.template);
            }
          } catch (templateError) {
            console.warn("查找模板时出错:", templateError);
            currentTemplate.value = null;
            const keys = storageKeys.value;
            localStorage.removeItem(keys.template);
          }
        }
        return true;
      }

      // 4. 如果没有保存的用户设置，检查是否有服务器配置
      if (
        serverConfig.value &&
        serverConfig.value.sections &&
        serverConfig.value.sections.length > 0
      ) {
        console.log("没有保存的用户设置，使用服务器默认配置");
        applyDefaultConfig();
      } else {
        console.log("没有保存的用户设置，且服务器配置未准备好，跳过配置加载");
        // 不应用任何配置，等待内容加载完成后再处理
        return false;
      }
      return true;
    } catch (error) {
      console.error("智能加载配置失败:", error);
      return false;
    }
  };

  /**
   * 验证和优化配置
   */
  const validateAndOptimizeConfig = (config) => {
    // 验证配置
    const validation = validatePlaybackConfig(config);
    if (!validation.valid) {
      console.warn("配置验证失败:", validation.errors);
      return null;
    }

    // 标准化和优化配置
    const normalized = normalizePlaybackConfig(config);
    const optimized = optimizePlaybackConfig(normalized);

    return optimized;
  };

  return {
    // 状态
    settings,
    currentTemplate,
    serverConfig,
    storageKeys,

    // 方法
    deepClone,
    saveToStorage,
    loadFromStorage,
    clearStorage,
    isSettingsMatchTemplate,
    updateTemplateState,
    applyDefaultConfig,
    applyTemplate,
    smartLoadConfig,
    validateAndOptimizeConfig,
  };
}
