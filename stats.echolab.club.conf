server {
    server_name stats.echolab.club;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 设置正确的协议头
        proxy_set_header X-Forwarded-Proto https;
        
        # 移除可能冲突的 CSP 头，让 Next.js 中间件处理
        proxy_hide_header Content-Security-Policy;
        
        # 只保留基本的安全头
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/echolab.club/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/echolab.club/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = stats.echolab.club) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name stats.echolab.club;
    return 404; # managed by Certbot
}