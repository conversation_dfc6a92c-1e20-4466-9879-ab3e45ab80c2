/**
 * 统计分析工具函数
 * 提供与 Umami 统计工具交互的方法
 */

/**
 * 跟踪用户事件
 * @param {string} eventName - 事件名称
 * @param {Object} [eventData={}] - 事件数据
 * @returns {boolean} - 是否成功发送事件
 */
export function trackEvent(eventName, eventData = {}) {
  if (window.umami) {
    try {
      window.umami.track(eventName, eventData);
      return true;
    } catch (error) {
      console.error("统计事件跟踪失败:", error);
      return false;
    }
  }
  return false;
}

/**
 * 设置用户属性
 * @param {Object} attributes - 用户属性
 * @returns {boolean} - 是否成功设置属性
 */
export function setUserAttributes(attributes) {
  if (window.umami) {
    try {
      // 注意：Umami 目前不直接支持用户属性设置
      // 这里使用自定义事件来模拟
      window.umami.track("set_user_attributes", attributes);
      return true;
    } catch (error) {
      console.error("设置用户属性失败:", error);
      return false;
    }
  }
  return false;
}

/**
 * 检查统计工具是否已加载
 * @returns {boolean} - 统计工具是否已加载
 */
export function isAnalyticsLoaded() {
  return !!window.umami;
}

/**
 * 禁用统计跟踪
 * @param {boolean} [disabled=true] - 是否禁用
 */
export function disableTracking(disabled = true) {
  if (window.umami) {
    window.umami.disabled = disabled;
  }
}

/**
 * 跟踪错误事件
 * @param {Error|string} error - 错误对象或错误消息
 * @param {Object} [context={}] - 错误上下文信息
 * @returns {boolean} - 是否成功发送错误事件
 */
export function trackError(error, context = {}) {
  try {
    // 构建错误信息
    const errorInfo = {
      message: typeof error === "string" ? error : error.message,
      stack: error.stack ? error.stack.substring(0, 500) : undefined, // 限制stack长度
      name: error.name || "Error",
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      ...context,
    };

    // 发送错误事件到Umami
    return trackEvent(EVENTS.ERROR_OCCURRED, errorInfo);
  } catch (trackingError) {
    console.error("错误事件跟踪失败:", trackingError);
    return false;
  }
}

/**
 * 跟踪API错误
 * @param {string} endpoint - API端点
 * @param {number} statusCode - HTTP状态码
 * @param {string} errorMessage - 错误消息
 * @param {Object} [context={}] - 额外上下文
 * @returns {boolean} - 是否成功发送错误事件
 */
export function trackApiError(
  endpoint,
  statusCode,
  errorMessage,
  context = {}
) {
  return trackError(`API Error: ${errorMessage}`, {
    type: "api_error",
    endpoint,
    statusCode,
    ...context,
  });
}

/**
 * 常用事件名称常量
 * 使用常量可以确保事件名称的一致性
 */
export const EVENTS = {
  // 页面交互
  NODE_CREATED: "node_created",
  NODE_CONNECTED: "node_connected",
  NODE_CONFIGURED: "node_configured",
  PROJECT_SAVED: "project_saved",
  PROJECT_LOADED: "project_loaded",

  // 核心功能
  VIDEO_GENERATED: "video_generated",
  VIDEO_PLAYED: "video_played",
  VIDEO_DOWNLOADED: "video_downloaded",
  CONFIG_EXPORTED: "config_exported",
  CONFIG_IMPORTED: "config_imported",

  // 用户旅程
  FIRST_VISIT: "first_visit",
  FIRST_PROJECT_COMPLETED: "first_project_completed",
  ADVANCED_FEATURE_USED: "advanced_feature_used",

  // 错误事件
  ERROR_OCCURRED: "error_occurred",
  API_ERROR: "api_error",
  RESOURCE_ERROR: "resource_error",
};

export default {
  trackEvent,
  trackError,
  trackApiError,
  setUserAttributes,
  isAnalyticsLoaded,
  disableTracking,
  EVENTS,
};
