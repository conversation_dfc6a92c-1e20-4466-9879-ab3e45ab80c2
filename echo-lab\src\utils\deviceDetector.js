/**
 * 设备检测工具
 * 用于检测当前设备类型（桌面端、平板、手机）和浏览器信息
 * 使用mobile-detect库进行更准确的设备检测
 */
import MobileDetect from "mobile-detect";

/**
 * 检测当前设备类型
 * @returns {Object} 设备类型信息
 */
export function detectDevice() {
  const md = new MobileDetect(window.navigator.userAgent);

  // 使用mobile-detect库检测设备类型
  const isMobileDevice = !!md.mobile();
  const isTablet = !!md.tablet();
  const isPhone = isMobileDevice && !isTablet;

  // 检测操作系统
  const isIOS = !!md.is("iOS");
  const isAndroid = !!md.is("AndroidOS");

  // 设置设备类型
  let deviceType = "desktop";
  if (isTablet) {
    deviceType = "tablet";
  } else if (isPhone) {
    deviceType = "mobile";
  }

  return {
    deviceType,
    isMobile: deviceType === "mobile",
    isTablet: deviceType === "tablet",
    isDesktop: deviceType === "desktop",
    isIOS,
    isAndroid,
  };
}

/**
 * 检查当前设备是否为移动设备（仅手机）
 * @returns {boolean} 是否为移动设备
 */
export function isMobileDevice() {
  const device = detectDevice();
  return device.isMobile;
}

/**
 * 检查当前设备是否为平板设备
 * @returns {boolean} 是否为平板设备
 */
export function isTabletDevice() {
  const device = detectDevice();
  return device.isTablet;
}

/**
 * 获取需要在桌面端访问的路由路径列表
 * @returns {string[]} 路由路径列表
 */
export const desktopOnlyPaths = ["/editor", "/content", "/templates"];

/**
 * 检查路径是否需要在桌面端访问
 * @param {string} path 路由路径
 * @returns {boolean} 是否需要在桌面端访问
 */
export function isDesktopOnlyPath(path) {
  // 特殊情况：明确排除 /content-info 路径
  if (path === "/content-info") return false;

  // 检查路径是否匹配仅桌面路径
  return desktopOnlyPaths.some((desktopPath) => {
    // 精确匹配路径
    if (path === desktopPath) return true;

    // 匹配子路径，如 /editor/123
    return path.startsWith(desktopPath + "/");
  });
}

/**
 * 获取浏览器信息
 * @returns {Object} 浏览器信息对象
 */
export function getBrowserInfo() {
  const userAgent = navigator.userAgent;
  let browserName = "Unknown";
  let browserVersion = "Unknown";

  // 检测常见浏览器
  if (/Edge|Edg/.test(userAgent)) {
    browserName = "Edge";
    browserVersion =
      userAgent.match(/(Edge|Edg)\/(\d+\.\d+)/)?.[2] || "Unknown";
  } else if (/Firefox/.test(userAgent)) {
    browserName = "Firefox";
    browserVersion = userAgent.match(/Firefox\/(\d+\.\d+)/)?.[1] || "Unknown";
  } else if (/Chrome/.test(userAgent)) {
    browserName = "Chrome";
    browserVersion = userAgent.match(/Chrome\/(\d+\.\d+)/)?.[1] || "Unknown";
  } else if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    browserName = "Safari";
    browserVersion = userAgent.match(/Version\/(\d+\.\d+)/)?.[1] || "Unknown";
  } else if (/MSIE|Trident/.test(userAgent)) {
    browserName = "Internet Explorer";
    browserVersion = userAgent.match(/(MSIE |rv:)(\d+\.\d+)/)?.[2] || "Unknown";
  }

  const device = detectDevice();

  return {
    name: browserName,
    version: browserVersion,
    userAgent: userAgent,
    isIOS: device.isIOS,
    isAndroid: device.isAndroid,
    isMobile: device.isMobile,
  };
}

/**
 * 检测是否支持 PWA 安装
 * @returns {boolean} 是否支持 PWA 安装
 */
export function isPWAInstallable() {
  // 检查基本的 PWA 功能支持
  const hasPwaSupport = "serviceWorker" in navigator;

  // 检查是否已经安装为独立应用
  const isStandalone =
    window.matchMedia("(display-mode: standalone)").matches ||
    window.navigator.standalone === true;

  // 如果已经安装为独立应用，则不需要再安装
  if (isStandalone) {
    return false;
  }

  return hasPwaSupport;
}

/**
 * 检测 PWA 是否已安装
 * @returns {boolean} 是否已安装为独立应用
 */
export function isPWAInstalled() {
  // 检查是否已经安装为独立应用
  return (
    window.matchMedia("(display-mode: standalone)").matches ||
    window.navigator.standalone === true
  );
}
