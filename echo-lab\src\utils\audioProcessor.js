/**
 * 音频处理工具
 * 处理音频速度变化和合并音频
 */

// 音频采样率常量
const VOICE_SAMPLE_RATE = 24000; // 24kHz for voice audio

// 导入静音生成工具
import { createSilenceBuffer } from "./silenceGenerator.js";

/**
 * 处理音频
 * @param {Array} audioUrls - 音频URL数组
 * @param {Array} timeline - 时间线数组
 * @param {Object} settings - 播放设置
 * @param {Function} progressCallback - 进度回调函数，参数为已完成的音频数量
 * @returns {Promise<AudioBuffer>} 处理后的音频缓冲区
 */
export async function processAudio(
  audioUrls,
  timeline,
  settings,
  progressCallback
) {
  try {
    // 创建音频上下文
    if (typeof window === "undefined") {
      throw new Error("当前环境不支持 AudioContext");
    }

    // 使用标准 AudioContext
    const AudioContextClass = window.AudioContext;
    const audioContext = new AudioContextClass({
      sampleRate: VOICE_SAMPLE_RATE, // 使用24kHz采样率
    });

    // 计算音频项目数量（排除封面项目）
    const audioItems = timeline.filter((item) => !item.isCover);
    const totalAudioItems = audioItems.length;

    // 如果没有回调函数，创建一个空函数
    const reportProgress =
      typeof progressCallback === "function" ? progressCallback : () => {};

    // 初始化进度计数
    let completedCount = 0;

    // 计算并报告进度的辅助函数
    const updateProgress = () => {
      // 将URL进度映射到时间线项目进度
      // 使用比例计算确保进度平滑
      const progress = Math.floor(
        (completedCount / audioUrls.length) * totalAudioItems
      );
      reportProgress(progress);
    };

    // 显示初始进度
    reportProgress(0);

    // 创建音频获取Promise，使用更简洁的错误处理
    const audioBuffersPromises = audioUrls.map((url) =>
      fetchAudioData(url, audioContext)
        .then((buffer) => {
          completedCount++;
          updateProgress();
          return buffer;
        })
        .catch((error) => {
          console.error(`获取音频数据失败 (${url}):`, error);
          completedCount++;
          updateProgress();
          return null; // 返回null表示获取失败
        })
    );

    const audioBuffers = await Promise.all(audioBuffersPromises);

    // 过滤掉获取失败的音频（直接在下面的循环中处理）

    // 创建音频缓冲区映射
    const audioBufferMap = {};
    audioUrls.forEach((url, index) => {
      if (audioBuffers[index] !== null) {
        audioBufferMap[url] = audioBuffers[index];
      }
    });

    // 处理时间线中的每个项目
    const processedBuffers = [];
    let totalDuration = 0;

    // 静音生成工具已在文件顶部导入

    for (let i = 0; i < timeline.length; i++) {
      const item = timeline[i];

      // 检查是否为封面项目
      if (item.isCover) {
        // 为封面创建静音音频
        const coverDuration = item.duration || 3;
        const silenceBuffer = createSilenceBuffer(audioContext, coverDuration);

        // 更新时间线项目的实际开始时间和持续时间
        timeline[i].actualStartTime = totalDuration;
        timeline[i].actualDuration = coverDuration;

        // 添加到处理后的缓冲区数组
        processedBuffers.push({
          buffer: silenceBuffer,
          startTime: totalDuration,
        });

        // 更新总时长
        totalDuration += coverDuration;

        // 继续处理下一个项目
        continue;
      }

      // 获取原始音频缓冲区
      let originalBuffer = audioBufferMap[item.audioUrl];

      // 如果没有找到音频，创建静音音频作为替代
      if (!originalBuffer) {
        // 使用时间线中的时长创建静音
        const silenceDuration = item.duration || 2;
        originalBuffer = createSilenceBuffer(audioContext, silenceDuration);
      }

      // 确保buffer有效
      if (
        !originalBuffer ||
        originalBuffer.length <= 0 ||
        originalBuffer.numberOfChannels <= 0
      ) {
        originalBuffer = createSilenceBuffer(audioContext, item.duration || 2);
      }

      // 处理音频速度 - 确保speed有效，默认为1.0
      const speed =
        item.speed !== undefined && item.speed !== null && !isNaN(item.speed)
          ? item.speed
          : 1.0;

      const processedBuffer = await changePlaybackRate(
        originalBuffer,
        speed,
        audioContext
      );

      // 计算停顿时长
      const pauseDuration = getPauseDuration(item, settings);

      // 计算项目的实际开始时间和持续时间

      // 更新时间线项目的实际开始时间和持续时间
      timeline[i].actualStartTime = totalDuration;
      timeline[i].actualDuration = processedBuffer.duration;

      // 添加到处理后的缓冲区数组
      processedBuffers.push({
        buffer: processedBuffer,
        startTime: totalDuration,
      });

      // 更新总时长
      totalDuration += processedBuffer.duration + pauseDuration;

      // 如果需要停顿，添加静音
      if (pauseDuration > 0) {
        const silenceBuffer = createSilenceBuffer(audioContext, pauseDuration);
        processedBuffers.push({
          buffer: silenceBuffer,
          startTime: totalDuration - pauseDuration,
        });
      }
    }

    // 合并所有处理后的音频缓冲区
    const finalBuffer = await mergeAudioBuffers(
      processedBuffers,
      totalDuration,
      audioContext
    );

    return finalBuffer;
  } catch (error) {
    console.error("处理音频失败:", error);
    throw error;
  }
}

/**
 * 获取音频数据
 * @param {string} url - 音频URL
 * @param {AudioContext} audioContext - 音频上下文
 * @returns {Promise<AudioBuffer>} 音频缓冲区
 */
async function fetchAudioData(url, audioContext) {
  try {
    // 检查URL是否有效
    if (!url || typeof url !== "string" || !url.startsWith("http")) {
      throw new Error(`无效的音频URL: ${url}`);
    }

    let arrayBuffer;

    try {
      // 获取音频数据 - PWA的Service Worker会自动处理缓存
      // 不指定mode，让浏览器自动选择合适的模式
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Accept: "audio/*",
        },
        credentials: "omit",
      });

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`获取音频失败，状态码: ${response.status}`);
      }

      // 获取音频数据
      arrayBuffer = await response.arrayBuffer();

      // 检查数据大小
      if (arrayBuffer.byteLength === 0) {
        throw new Error("音频数据为空");
      }
    } catch (error) {
      console.error(`获取音频数据失败: ${url}`, error);

      // 创建2秒的静音音频作为替代
      return createSilenceBuffer(audioContext, 2);
    }

    // 解码音频数据
    try {
      const decodedBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // 如果采样率不是24kHz，进行重采样
      if (decodedBuffer.sampleRate !== VOICE_SAMPLE_RATE) {
        return resampleAudio(decodedBuffer, VOICE_SAMPLE_RATE);
      }
      return decodedBuffer;
    } catch (decodeError) {
      console.error(`解码音频数据失败: ${url}`, decodeError);

      // 创建2秒的静音音频作为替代
      return createSilenceBuffer(audioContext, 2);
    }
  } catch (error) {
    console.error("获取音频数据失败:", error);

    // 创建2秒的静音音频作为替代
    return createSilenceBuffer(audioContext, 2);
  }
}

/**
 * 改变音频播放速度
 * @param {AudioBuffer} buffer - 原始音频缓冲区
 * @param {number} rate - 播放速度
 * @param {AudioContext} audioContext - 音频上下文（未使用）
 * @returns {Promise<AudioBuffer>} 处理后的音频缓冲区
 */
async function changePlaybackRate(buffer, rate, _audioContext) {
  return new Promise((resolve, reject) => {
    try {
      // 安全检查，确保buffer有效
      if (
        !buffer ||
        !buffer.length ||
        buffer.length <= 0 ||
        !buffer.numberOfChannels ||
        buffer.numberOfChannels <= 0
      ) {
        // 创建一个1秒的静音音频缓冲区
        const silenceBuffer = _audioContext.createBuffer(
          1,
          _audioContext.sampleRate,
          _audioContext.sampleRate
        );
        resolve(silenceBuffer);
        return;
      }

      // 确保rate是有效的数字，默认为1.0
      const validRate =
        rate !== undefined && rate !== null && !isNaN(rate) ? rate : 1.0;

      // 如果速度是1.0，直接返回原始缓冲区
      if (validRate === 1.0) {
        resolve(buffer);
        return;
      }

      // 计算新的长度，确保至少为1帧
      const newLength = Math.max(1, Math.floor(buffer.length / validRate));

      // 创建离线音频上下文，使用统一的采样率
      const offlineContext = new OfflineAudioContext(
        buffer.numberOfChannels,
        newLength,
        VOICE_SAMPLE_RATE
      );

      // 创建音频源
      const source = offlineContext.createBufferSource();
      source.buffer = buffer;
      source.playbackRate.value = validRate;
      source.connect(offlineContext.destination);

      // 开始处理
      source.start(0);
      offlineContext
        .startRendering()
        .then((renderedBuffer) => {
          resolve(renderedBuffer);
        })
        .catch((error) => {
          reject(error);
        });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 合并音频缓冲区
 * 优化版本：直接在时域合并音频数据，避免使用 OfflineAudioContext
 * @param {Array} buffers - 音频缓冲区数组
 * @param {number} totalDuration - 总时长
 * @param {AudioContext} audioContext - 音频上下文
 * @returns {Promise<AudioBuffer>} 合并后的音频缓冲区
 */
async function mergeAudioBuffers(buffers, totalDuration, audioContext) {
  return new Promise((resolve, reject) => {
    try {
      if (buffers.length === 0) {
        resolve(
          audioContext.createBuffer(1, VOICE_SAMPLE_RATE, VOICE_SAMPLE_RATE)
        );
        return;
      }

      // 创建单声道的输出缓冲区
      const outputBuffer = audioContext.createBuffer(
        1,
        Math.ceil(VOICE_SAMPLE_RATE * totalDuration),
        VOICE_SAMPLE_RATE
      );
      const outputData = outputBuffer.getChannelData(0);

      // 直接在时域合并音频数据
      buffers.forEach((item) => {
        const inputData = item.buffer.getChannelData(0);
        const startOffset = Math.floor(item.startTime * VOICE_SAMPLE_RATE);
        for (let i = 0; i < inputData.length; i++) {
          if (startOffset + i < outputData.length) {
            outputData[startOffset + i] += inputData[i];
          }
        }
      });

      // 标准化音频数据，避免削波
      let maxAmplitude = 0;
      // 找出最大振幅
      for (let i = 0; i < outputData.length; i++) {
        const absValue = Math.abs(outputData[i]);
        if (absValue > maxAmplitude) {
          maxAmplitude = absValue;
        }
      }

      // 如果需要，进行音量标准化
      if (maxAmplitude > 1) {
        const normalizeRatio = 0.95 / maxAmplitude; // 留出一些余量，防止削波
        for (let i = 0; i < outputData.length; i++) {
          outputData[i] *= normalizeRatio;
        }
      }

      resolve(outputBuffer);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 获取停顿时长
 * @param {Object} item - 时间线项目
 * @param {Object} settings - 播放设置
 * @returns {number} 停顿时长（秒）
 */
function getPauseDuration(item, settings) {
  // 查找对应的环节设置
  const section = settings.sections.find((s) => s.id === item.sectionId);
  if (!section) return 0;

  // 如果有重复索引且有重复停顿数组，使用对应的停顿时长
  if (
    item.repeatIndex !== undefined &&
    section.repeatPauses &&
    Array.isArray(section.repeatPauses) &&
    section.repeatPauses.length > item.repeatIndex
  ) {
    return section.repeatPauses[item.repeatIndex] / 1000;
  }

  // 否则使用基础停顿时长（毫秒转秒）
  return (section.pauseDuration || 3000) / 1000;
}

/**
 * 将 AudioBuffer 转换为 WAV 格式
 * @param {AudioBuffer} audioBuffer - 音频缓冲区
 * @param {Object} options - 转换选项
 * @param {number} options.channels - 通道数
 * @param {number} options.targetSampleRate - 目标采样率
 * @returns {ArrayBuffer} WAV 格式的数据
 */
export function audioBufferToWav(audioBuffer, options = {}) {
  try {
    // 安全检查
    if (!audioBuffer || !audioBuffer.numberOfChannels || !audioBuffer.length) {
      console.error("无效的音频缓冲区");
      throw new Error("无效的音频缓冲区");
    }

    const channels = options.channels || audioBuffer.numberOfChannels;

    // 重采样处理 - 简化处理，不进行实际重采样
    const numChannels = Math.min(channels, audioBuffer.numberOfChannels);
    const length = audioBuffer.length;
    const sampleRate = audioBuffer.sampleRate; // 使用原始采样率

    // 计算文件大小并创建缓冲区
    const dataSize = length * numChannels * 2; // 16位采样 = 2字节
    const bufferSize = 44 + dataSize; // 44字节的WAV头 + 数据大小

    // 创建 WAV 文件头
    const buffer = new ArrayBuffer(bufferSize);
    const view = new DataView(buffer);

    // 安全写入字符串函数
    const safeWriteString = (view, offset, string) => {
      for (let i = 0; i < string.length && i + offset < view.byteLength; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    // RIFF 标识
    safeWriteString(view, 0, "RIFF");
    // 文件大小 (文件总大小 - 8字节RIFF头)
    view.setUint32(4, bufferSize - 8, true);
    // WAVE 标识
    safeWriteString(view, 8, "WAVE");
    // fmt 子块
    safeWriteString(view, 12, "fmt ");
    // 子块大小
    view.setUint32(16, 16, true);
    // 音频格式（PCM）
    view.setUint16(20, 1, true);
    // 通道数
    view.setUint16(22, numChannels, true);
    // 采样率
    view.setUint32(24, sampleRate, true);
    // 字节率
    view.setUint32(28, sampleRate * numChannels * 2, true);
    // 块对齐
    view.setUint16(32, numChannels * 2, true);
    // 位深度
    view.setUint16(34, 16, true);
    // data 子块
    safeWriteString(view, 36, "data");
    // 数据大小
    view.setUint32(40, dataSize, true);

    // 写入音频数据
    const dataOffset = 44;
    const channelData = [];

    // 获取每个通道的数据
    for (let i = 0; i < numChannels; i++) {
      channelData.push(audioBuffer.getChannelData(i));
    }

    // 交错写入音频数据
    let offset = 0;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        // 确保不超出缓冲区范围
        if (dataOffset + offset + 1 >= view.byteLength) {
          console.warn("达到缓冲区末尾，停止写入");
          return buffer;
        }

        // 将 -1.0 到 1.0 的浮点数转换为 -32768 到 32767 的整数
        const sample = Math.max(-1, Math.min(1, channelData[channel][i]));
        const value = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
        view.setInt16(dataOffset + offset, value, true);
        offset += 2;
      }
    }

    return buffer;
  } catch (error) {
    console.error("转换音频格式失败:", error);
    // 返回一个小的有效WAV文件（1秒静音）
    return createEmptyWavBuffer();
  }
}

/**
 * 创建一个空的WAV缓冲区（1秒静音）
 * @returns {ArrayBuffer} 空的WAV缓冲区
 */
function createEmptyWavBuffer() {
  const sampleRate = VOICE_SAMPLE_RATE;
  const numChannels = 1;
  const length = sampleRate; // 1秒
  const buffer = new ArrayBuffer(44 + length * 2);
  const view = new DataView(buffer);

  // RIFF 标识
  for (let i = 0; i < 4; i++) {
    view.setUint8(i, "RIFF".charCodeAt(i));
  }
  // 文件大小
  view.setUint32(4, 36 + length * 2, true);
  // WAVE 标识
  for (let i = 0; i < 4; i++) {
    view.setUint8(8 + i, "WAVE".charCodeAt(i));
  }
  // fmt 子块
  for (let i = 0; i < 4; i++) {
    view.setUint8(12 + i, "fmt ".charCodeAt(i));
  }
  // 子块大小
  view.setUint32(16, 16, true);
  // 音频格式（PCM）
  view.setUint16(20, 1, true);
  // 通道数
  view.setUint16(22, numChannels, true);
  // 采样率
  view.setUint32(24, sampleRate, true);
  // 字节率
  view.setUint32(28, sampleRate * numChannels * 2, true);
  // 块对齐
  view.setUint16(32, numChannels * 2, true);
  // 位深度
  view.setUint16(34, 16, true);
  // data 子块
  for (let i = 0; i < 4; i++) {
    view.setUint8(36 + i, "data".charCodeAt(i));
  }
  // 数据大小
  view.setUint32(40, length * 2, true);

  // 写入静音数据（全0）
  for (let i = 0; i < length; i++) {
    view.setInt16(44 + i * 2, 0, true);
  }

  return buffer;
}

/**
 * 重采样音频
 * @param {AudioBuffer} audioBuffer - 原始音频缓冲区
 * @param {number} targetSampleRate - 目标采样率
 * @returns {AudioBuffer} 重采样后的音频缓冲区
 */
function resampleAudio(audioBuffer, targetSampleRate) {
  // 如果采样率相同，直接返回原始缓冲区
  if (targetSampleRate === audioBuffer.sampleRate) {
    return audioBuffer;
  }

  // 创建一个新的离线音频上下文用于重采样
  const numChannels = audioBuffer.numberOfChannels;
  const duration = audioBuffer.duration;
  const newLength = Math.round(duration * targetSampleRate);

  const offlineContext = new OfflineAudioContext(
    numChannels,
    newLength,
    targetSampleRate
  );

  const bufferSource = offlineContext.createBufferSource();
  bufferSource.buffer = audioBuffer;
  bufferSource.connect(offlineContext.destination);
  bufferSource.start();

  return offlineContext.startRendering();
}
