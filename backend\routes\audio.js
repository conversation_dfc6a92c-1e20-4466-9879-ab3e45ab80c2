/**
 * 音频管理路由
 * 处理音频记录的创建、更新、查询和删除
 * 简化版本：只支持 audio_source 字段
 */
const express = require("express");
const router = express.Router();
const multer = require("multer");
const { nanoid } = require("nanoid");
const db = require("../models");
const { Op } = require("sequelize");
const ossService = require("../services/ossService");

// 配置multer用于处理音频文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 限制50MB
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimes = [
      "audio/mpeg",
      "audio/wav",
      "audio/mp3",
      "audio/mp4",
      "audio/m4a",
      "audio/webm",
      "audio/ogg",
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("不支持的音频文件格式"), false);
    }
  },
});

/**
 * 上传音频文件
 * POST /api/audio/upload
 */
router.post("/upload", upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: "未提供音频文件",
      });
    }

    const { language = "auto", type = "manual" } = req.body;

    // 生成唯一ID
    const audioId = `audio_${nanoid()}`;

    // 从MIME类型获取文件扩展名
    const mimeToExt = {
      "audio/mpeg": "mp3",
      "audio/wav": "wav",
      "audio/mp3": "mp3",
      "audio/mp4": "mp4",
      "audio/m4a": "m4a",
      "audio/webm": "webm",
      "audio/ogg": "ogg",
    };

    const format = mimeToExt[req.file.mimetype] || "mp3";

    // 根据类型确定OSS路径
    const ossPath = `audio/${type}/${audioId}.${format}`;

    // 上传到OSS
    const ossResult = await ossService.put(ossPath, req.file.buffer, {
      mime: req.file.mimetype,
    });

    console.log(`音频文件上传成功: ${audioId}`);

    return res.json({
      success: true,
      data: {
        audioId: audioId,
        url: ossResult.url,
        ossKey: ossPath,
        format: format,
        size: req.file.size,
        originalName: req.file.originalname,
      },
    });
  } catch (error) {
    console.error("音频文件上传失败:", error);
    return res.status(500).json({
      success: false,
      error: "音频文件上传失败: " + error.message,
    });
  }
});

/**
 * 保存音频记录
 * POST /api/audio
 */
router.post("/", async (req, res) => {
  try {
    const {
      text,
      language,
      speed = 1.0,
      speaker,
      audioUrl,
      duration,
      audioSource = "tts",
      md5Hash,
    } = req.body;

    // 验证必需字段
    if (!text || !language || !speaker || !audioUrl || duration === undefined) {
      return res.status(400).json({
        success: false,
        error: "缺少必需字段: text, language, speaker, audioUrl, duration",
      });
    }

    // 检查是否已存在相同的音频记录
    let existingAudio = null;

    if (md5Hash) {
      // 基于MD5哈希查找
      existingAudio = await db.Audio.findOne({
        where: {
          md5Hash,
        },
      });
    }

    let audioRecord;

    if (existingAudio) {
      // 更新现有记录
      await existingAudio.update({
        text,
        language,
        speed,
        speaker,
        ossUrl: audioUrl,
        ossKey: extractOssKey(audioUrl),
        duration,
        audioSource,
        md5Hash: md5Hash || existingAudio.md5Hash,
      });

      audioRecord = existingAudio;
    } else {
      // 创建新记录
      audioRecord = await db.Audio.create({
        text,
        language,
        speed,
        speaker,
        ossUrl: audioUrl,
        ossKey: extractOssKey(audioUrl),
        duration,
        audioSource,
        md5Hash: md5Hash || generateMd5Hash(text, language, speed, speaker),
      });
    }

    res.json({
      success: true,
      audio: {
        id: audioRecord.id,
        text: audioRecord.text,
        language: audioRecord.language,
        speed: audioRecord.speed,
        speaker: audioRecord.speaker,
        url: audioRecord.ossUrl,
        duration: audioRecord.duration,
        audioSource: audioRecord.audioSource,
        createdAt: audioRecord.createdAt,
        updatedAt: audioRecord.updatedAt,
      },
    });
  } catch (error) {
    console.error("保存音频记录失败:", error);
    res.status(500).json({
      success: false,
      error: "保存音频记录失败: " + error.message,
    });
  }
});

/**
 * 批量保存音频记录
 * POST /api/audio/batch
 */
router.post("/batch", async (req, res) => {
  try {
    const { audioItems } = req.body;

    if (!audioItems || !Array.isArray(audioItems) || audioItems.length === 0) {
      return res.status(400).json({
        success: false,
        error: "缺少有效的音频项数组",
      });
    }

    const results = [];
    const errors = [];

    // 使用事务确保数据一致性
    const transaction = await db.sequelize.transaction();

    try {
      for (let i = 0; i < audioItems.length; i++) {
        const item = audioItems[i];

        try {
          const {
            text,
            language,
            speed = 1.0,
            speaker,
            audioUrl,
            duration,
            audioSource = "tts",
            md5Hash,
          } = item;

          // 验证必需字段
          if (
            !text ||
            !language ||
            !speaker ||
            !audioUrl ||
            duration === undefined
          ) {
            throw new Error(`项目 ${i + 1}: 缺少必需字段`);
          }

          // 检查是否已存在（基于MD5哈希）
          let existingAudio = null;
          if (md5Hash) {
            existingAudio = await db.Audio.findOne({
              where: { md5Hash },
              transaction,
            });
          }

          let audioRecord;

          if (existingAudio) {
            // 更新现有记录
            await existingAudio.update(
              {
                text,
                language,
                speed,
                speaker,
                ossUrl: audioUrl,
                ossKey: extractOssKey(audioUrl),
                duration,
                audioSource,
                md5Hash: md5Hash || existingAudio.md5Hash,
              },
              { transaction }
            );

            audioRecord = existingAudio;
          } else {
            // 创建新记录
            audioRecord = await db.Audio.create(
              {
                text,
                language,
                speed,
                speaker,
                ossUrl: audioUrl,
                ossKey: extractOssKey(audioUrl),
                duration,
                audioSource,
                md5Hash:
                  md5Hash || generateMd5Hash(text, language, speed, speaker),
              },
              { transaction }
            );
          }

          results.push({
            success: true,
            audio: {
              id: audioRecord.id,
              text: audioRecord.text,
              language: audioRecord.language,
              speed: audioRecord.speed,
              speaker: audioRecord.speaker,
              url: audioRecord.ossUrl,
              duration: audioRecord.duration,
              audioSource: audioRecord.audioSource,
            },
          });
        } catch (itemError) {
          console.error(`处理音频项 ${i + 1} 失败:`, itemError);
          errors.push({
            index: i,
            error: itemError.message,
          });
          results.push({
            success: false,
            error: itemError.message,
          });
        }
      }

      // 提交事务
      await transaction.commit();

      res.json({
        success: true,
        results,
        errors: errors.length > 0 ? errors : undefined,
        summary: {
          total: audioItems.length,
          successful: results.filter((r) => r.success).length,
          failed: errors.length,
        },
      });
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }
  } catch (error) {
    console.error("批量保存音频记录失败:", error);
    res.status(500).json({
      success: false,
      error: "批量保存音频记录失败: " + error.message,
    });
  }
});

/**
 * 查询音频记录
 * GET /api/audio
 */
router.get("/", async (req, res) => {
  try {
    const { language, audioSource, page = 1, limit = 50 } = req.query;

    const where = {};

    if (language) where.language = language;
    if (audioSource) where.audioSource = audioSource;

    const offset = (page - 1) * limit;

    const { count, rows } = await db.Audio.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [["createdAt", "DESC"]],
    });

    const audioItems = rows.map((audio) => ({
      id: audio.id,
      text: audio.text,
      language: audio.language,
      speed: audio.speed,
      speaker: audio.speaker,
      url: audio.ossUrl,
      duration: audio.duration,
      audioSource: audio.audioSource,
      createdAt: audio.createdAt,
      updatedAt: audio.updatedAt,
    }));

    res.json({
      success: true,
      data: audioItems,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error("查询音频记录失败:", error);
    res.status(500).json({
      success: false,
      error: "查询音频记录失败: " + error.message,
    });
  }
});

// 工具函数
function extractOssKey(url) {
  // 从URL中提取OSS键
  try {
    const urlObj = new URL(url);
    return urlObj.pathname.substring(1); // 移除开头的 '/'
  } catch (error) {
    // 如果URL解析失败，返回原始URL作为键
    return url;
  }
}

function generateMd5Hash(text, language, speed, speaker) {
  const crypto = require("crypto");
  const data = `${text}_${language}_${speed}_${speaker}`;
  return crypto.createHash("md5").update(data).digest("hex");
}

module.exports = router;
