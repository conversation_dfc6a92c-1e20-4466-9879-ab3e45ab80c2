{"name": "echo-lab-backend", "version": "1.0.0", "main": "app.js", "scripts": {"dev": "NODE_ENV=development node app.js", "start": "NODE_ENV=production node app.js", "db:create": "node database/create-tables.js", "db:init": "bash database/init-db.sh", "db:init-voices": "node scripts/initVoices.js", "db:init-features": "node scripts/initFeatureFlags.js", "db:init-levels": "node scripts/initUserLevels.js", "db:grant-permissions": "node scripts/grantUserPermissions.js", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@alicloud/pop-core": "1.8.0", "@google-cloud/translate": "9.0.1", "adm-zip": "0.5.16", "ali-oss": "6.22.0", "archiver": "7.0.1", "axios": "1.9.0", "baidu-aip-sdk": "4.16.16", "connect-timeout": "1.9.0", "cors": "2.8.5", "crypto-js": "4.2.0", "csurf": "1.11.0", "dotenv": "16.5.0", "express": "4.18.0", "express-rate-limit": "7.1.5", "get-audio-duration": "4.0.1", "helmet": "7.1.0", "ip-range-check": "0.2.0", "joi": "17.11.0", "jsonwebtoken": "9.0.2", "kuromoji": "0.1.2", "morgan": "1.10.0", "multer": "1.4.5-lts.2", "music-metadata": "11.2.1", "mysql2": "3.14.1", "nanoid": "3.3.4", "nodemailer": "6.10.1", "p-limit": "3.1.0", "rotating-file-stream": "3.1.1", "sequelize": "6.37.7", "sequelize-cli": "6.6.2", "shortid": "2.2.17", "ua-parser-js": "2.0.3", "uuid": "8.3.2"}}