<template>
  <div class="input-test-container">
    <h2>输入框测试</h2>
    
    <div class="input-group">
      <label>普通输入框</label>
      <input type="text" v-model="inputValue" placeholder="请输入内容" />
    </div>
    
    <div class="input-group">
      <label>Element Plus 输入框</label>
      <el-input v-model="elInputValue" placeholder="请输入内容"></el-input>
    </div>
    
    <div class="values">
      <p>普通输入框值: {{ inputValue }}</p>
      <p>Element Plus 输入框值: {{ elInputValue }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const inputValue = ref('');
const elInputValue = ref('');
</script>

<style scoped>
.input-test-container {
  max-width: 500px;
  margin: 50px auto;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 5px;
}

.input-group {
  margin-bottom: 20px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
}

input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.values {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}
</style>
