<template>
  <div class="template-manager">
    <!-- 页面头部 -->
    <div class="manager-header">
      <div class="header-content">
        <h2>播放策略模板管理</h2>
        <p class="header-desc">创建和管理您的播放策略模板，提升学习效率</p>
      </div>

      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon>
            <Plus />
          </el-icon>
          新建模板
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-number">{{ templateStore.userTemplates.length }}</div>
            <div class="stat-label">我的模板</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-number">{{ publicTemplateCount }}</div>
            <div class="stat-label">公开模板</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-card">
            <div class="stat-number">{{ templateStore.systemTemplates.length }}</div>
            <div class="stat-label">系统模板</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 模板列表 -->
    <div class="templates-section">
      <!-- 筛选和搜索 -->
      <div class="filter-bar">
        <div class="filter-left">
          <el-input v-model="searchKeyword" placeholder="搜索模板名称或描述" prefix-icon="Search" clearable style="width: 320px"
            size="default" />
        </div>

        <div class="filter-right">
          <el-select v-model="filterType" placeholder="模板类型" style="width: 130px" size="default">
            <el-option label="全部类型" value="" />
            <el-option label="我的模板" value="user" />
            <el-option label="系统模板" value="system" />
            <el-option label="公开模板" value="public" />
          </el-select>

          <el-select v-model="sortBy" placeholder="排序方式" style="width: 130px" size="default">
            <el-option label="创建时间" value="createdAt" />
            <el-option label="模板名称" value="name" />
          </el-select>
        </div>
      </div>

      <!-- 模板表格 -->
      <el-table :data="filteredTemplates" v-loading="templateStore.loading" class="templates-table" stripe border
        size="default">
        <el-table-column prop="name" label="模板名称" min-width="180">
          <template #default="{ row }">
            <div class="template-name-cell">
              <div class="name-content">
                <span class="template-name">{{ row.name }}</span>
                <el-tag v-if="row.type === 'system'" type="primary" size="small" effect="light">系统</el-tag>
                <el-tag v-else-if="row.isPublic" type="success" size="small" effect="light">公开</el-tag>
                <el-tag v-else type="info" size="small" effect="light">私有</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="description-text">{{ row.description || '暂无描述' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="环节数" width="90" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small" effect="plain">
              {{ row.config?.sections?.length || 0 }}个
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建者" width="100" align="center">
          <template #default="{ row }">
            <span class="creator-text" v-if="row.type === 'system'">系统</span>
            <span class="creator-text" v-else-if="row.creator">{{ row.creator.username }}</span>
            <span class="creator-text" v-else>{{ row.userId === userStore.user?.id ? '我' : '未知' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="110" align="center">
          <template #default="{ row }">
            <span class="date-text">{{ formatDate(row.createdAt) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="220" fixed="right" align="center">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button size="small" type="primary" link @click="previewTemplate(row)">
                <el-icon>
                  <View />
                </el-icon>
                预览
              </el-button>

              <el-button size="small" type="success" link @click="duplicateTemplate(row)">
                <el-icon>
                  <CopyDocument />
                </el-icon>
                复制
              </el-button>

              <template v-if="row.type === 'user' && row.userId === userStore.user?.id">
                <el-button size="small" type="primary" link @click="editTemplate(row)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                  编辑
                </el-button>
                <el-button size="small" type="danger" link @click="deleteTemplate(row)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                  删除
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="filteredTemplates.length === 0 && !templateStore.loading" class="empty-state">
        <el-empty description="暂无符合条件的模板">
          <el-button type="primary" @click="showCreateDialog = true" v-if="userStore.isLoggedIn">
            <el-icon>
              <Plus />
            </el-icon>
            创建第一个模板
          </el-button>
          <span v-else class="empty-tip">登录后可以创建和管理模板</span>
        </el-empty>
      </div>
    </div>

    <!-- 统一的模板查看器 -->
    <TemplateViewer v-model="showCreateDialog" :template="editingTemplate" :readonly="false"
      @saved="handleTemplateSaved" />

    <TemplateViewer v-model="showPreviewDialog" :template="previewingTemplate" :readonly="true" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Plus, View, Edit, Delete, CopyDocument } from '@element-plus/icons-vue';
import { useTemplateStore } from '@/stores/templateStore';
import { useUserStore } from '@/stores/userStore';
import TemplateViewer from './TemplateViewer.vue';

// Stores
const templateStore = useTemplateStore();
const userStore = useUserStore();

// State
const searchKeyword = ref('');
const filterType = ref('');
const sortBy = ref('createdAt');

const showCreateDialog = ref(false);
const showPreviewDialog = ref(false);
const editingTemplate = ref(null);
const previewingTemplate = ref(null);

// Computed
const filteredTemplates = computed(() => {
  const templates = [...templateStore.allTemplates];

  // 根据类型筛选
  let filtered = templates;
  if (filterType.value === 'user') {
    filtered = templates.filter(t => t.type === 'user' && t.userId === userStore.user?.id);
  } else if (filterType.value === 'system') {
    filtered = templates.filter(t => t.type === 'system');
  } else if (filterType.value === 'public') {
    filtered = templates.filter(t => t.type === 'user' && t.isPublic && t.userId !== userStore.user?.id);
  }

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(t =>
      t.name.toLowerCase().includes(keyword) ||
      (t.description && t.description.toLowerCase().includes(keyword))
    );
  }

  // 排序
  filtered.sort((a, b) => {
    if (sortBy.value === 'name') {
      return a.name.localeCompare(b.name);
    } else {
      return new Date(b.createdAt) - new Date(a.createdAt);
    }
  });

  return filtered;
});

const publicTemplateCount = computed(() => {
  return templateStore.userTemplates.filter(t => t.isPublic).length;
});

// Methods
const editTemplate = (template) => {
  editingTemplate.value = template;
  showCreateDialog.value = true;
};

const previewTemplate = (template) => {
  previewingTemplate.value = template;
  showPreviewDialog.value = true;
};

const duplicateTemplate = async (template) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新模板的名称',
      '复制模板',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: `${template.name} - 副本`,
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '模板名称不能为空';
          }
          if (value.trim().length > 100) {
            return '模板名称不能超过100个字符';
          }
          return true;
        }
      }
    );

    await templateStore.duplicateTemplate(template.id, newName.trim());
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制模板失败:', error);
    }
  }
};

const deleteTemplate = async (template) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？此操作不可撤销。`,
      '删除模板',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await templateStore.deleteTemplate(template.id);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error);
    }
  }
};

const handleTemplateSaved = () => {
  showCreateDialog.value = false;
  editingTemplate.value = null;

  // 不需要重新加载模板列表，因为 createTemplate 和 updateTemplate 已经更新了本地状态
  // templateStore.loadTemplates();
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// Lifecycle
onMounted(() => {
  templateStore.loadTemplates();
});
</script>

<style scoped>
.template-manager {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-content h2 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-desc {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.125rem;
  line-height: 1.6;
}

.stats-section {
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  font-weight: 500;
  color: #606266;
}

.templates-section {
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-left {
  display: flex;
  align-items: center;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 表格样式优化 */
.templates-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.templates-table .el-table__header-wrapper) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

:deep(.templates-table .el-table__header th) {
  background: transparent;
  color: #495057;
  font-weight: 600;
  font-size: 0.875rem;
  border-bottom: 2px solid #dee2e6;
}

:deep(.templates-table .el-table__row:hover) {
  background-color: #f8f9ff;
}

.template-name-cell {
  padding: 0.5rem 0;
}

.name-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.template-name {
  font-weight: 600;
  color: #303133;
  font-size: 0.95rem;
  line-height: 1.4;
}

.description-text {
  color: #606266;
  font-size: 0.875rem;
  line-height: 1.4;
}

.creator-text {
  color: #606266;
  font-size: 0.875rem;
}

.date-text {
  color: #909399;
  font-size: 0.8rem;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  flex-wrap: wrap;
}

:deep(.action-buttons .el-button) {
  margin: 0;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
}

:deep(.action-buttons .el-button .el-icon) {
  margin-right: 0.25rem;
  font-size: 0.875rem;
}

/* 空状态样式 */
.empty-state {
  margin-top: 2rem;
  padding: 2rem;
  text-align: center;
}

.empty-tip {
  color: #909399;
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-manager {
    padding: 1rem;
  }

  .manager-header {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-bar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .filter-right {
    justify-content: space-between;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>
