/**
 * 节点存储
 * 使用Pinia管理节点状态，包括节点数据、位置信息和处理结果缓存
 */

import { defineStore } from "pinia";
import nodeFactory from "../factories/NodeFactory";

export const useNodeStore = defineStore("node", {
  state: () => ({
    // 节点数据
    nodes: {},
    // 节点处理结果缓存
    nodeResults: {},
    // 当前选中的节点ID
    selectedNodeId: null,
    // 节点类型计数器（用于生成节点序号）
    nodeTypeCounters: {},
  }),

  getters: {
    /**
     * 获取所有节点
     * @returns {Object[]} 节点数组
     */
    getAllNodes: (state) => Object.values(state.nodes),

    /**
     * 获取节点
     * @param {string} id - 节点ID
     * @returns {Object|null} 节点对象
     */
    getNode: (state) => (id) => state.nodes[id] || null,

    /**
     * 获取节点处理结果
     * @param {string} id - 节点ID
     * @returns {*|null} 节点处理结果
     */
    getNodeResult: (state) => (id) => {
      const cachedResult = state.nodeResults[id];
      if (
        cachedResult &&
        cachedResult._timestamp >= (state.nodes[id]?._timestamp || 0)
      ) {
        return cachedResult.result;
      }
      return null;
    },

    /**
     * 获取选中的节点
     * @returns {Object|null} 选中的节点对象
     */
    getSelectedNode: (state) =>
      state.selectedNodeId ? state.nodes[state.selectedNodeId] : null,

    /**
     * 获取节点的源节点
     * @param {string} id - 节点ID
     * @returns {Object[]} 源节点数组
     */
    getNodeSources: (state) => (id) => {
      const node = state.nodes[id];
      if (!node || !node.sourceIds || node.sourceIds.length === 0) return [];

      return node.sourceIds
        .map((sourceId) => state.nodes[sourceId])
        .filter(Boolean);
    },

    /**
     * 获取节点的目标节点
     * @param {string} id - 节点ID
     * @returns {Object[]} 目标节点数组
     */
    getNodeTargets: (state) => (id) => {
      return Object.values(state.nodes).filter(
        (node) => node.sourceIds && node.sourceIds.includes(id)
      );
    },
  },

  actions: {
    /**
     * 添加节点
     * @param {Object} node - 节点对象
     */
    addNode(node) {
      // 更新节点类型计数器
      if (!this.nodeTypeCounters[node.type]) {
        this.nodeTypeCounters[node.type] = 0;
      }
      this.nodeTypeCounters[node.type]++;

      // 添加节点
      this.nodes[node.id] = {
        ...node,
        number: this.nodeTypeCounters[node.type],
      };

      // 清除结果缓存
      this.clearNodeResultCache(node.id);
    },

    /**
     * 创建并添加节点
     * @param {string} type - 节点类型
     * @param {Object} [params={}] - 节点参数
     * @param {string[]} [sourceIds=[]] - 源节点ID数组
     * @param {Object} [position={ x: 0, y: 0 }] - 节点位置
     * @param {string} [customName=""] - 自定义节点名称
     * @returns {string} 节点ID
     */
    createNode(
      type,
      params = {},
      sourceIds = [],
      position = { x: 0, y: 0 },
      customName = ""
    ) {
      // 创建节点
      const node = nodeFactory.createNode(
        type,
        params,
        sourceIds,
        position,
        customName
      );

      // 添加节点
      this.addNode(node);

      return node.id;
    },

    /**
     * 更新节点参数
     * @param {string} nodeId - 节点ID
     * @param {Object} params - 节点参数
     */
    updateNodeParams(nodeId, params) {
      if (!this.nodes[nodeId]) return;

      this.nodes[nodeId].params = { ...params };
      this.nodes[nodeId]._timestamp = Date.now();

      // 清除结果缓存
      this.clearNodeResultCache(nodeId);

      // 清除下游节点的结果缓存
      this.clearDownstreamResultCache(nodeId);
    },

    /**
     * 更新节点位置
     * @param {string} nodeId - 节点ID
     * @param {Object} position - 节点位置
     */
    updateNodePosition(nodeId, position) {
      if (!this.nodes[nodeId]) return;

      this.nodes[nodeId].position = { ...position };
    },

    /**
     * 更新节点自定义名称
     * @param {string} nodeId - 节点ID
     * @param {string} customName - 自定义名称
     */
    updateNodeCustomName(nodeId, customName) {
      if (!this.nodes[nodeId]) return;

      this.nodes[nodeId].customName = customName.trim();
    },

    /**
     * 连接节点
     * @param {string} sourceId - 源节点ID
     * @param {string} targetId - 目标节点ID
     * @returns {boolean} 连接是否成功
     */
    connectNodes(sourceId, targetId) {
      const sourceNode = this.nodes[sourceId];
      const targetNode = this.nodes[targetId];

      if (!sourceNode || !targetNode) return false;

      // 检查连接是否有效
      if (!nodeFactory.isValidConnection(sourceNode.type, targetNode.type)) {
        console.warn(`无效的连接: ${sourceNode.type} -> ${targetNode.type}`);
        return false;
      }

      // 检查是否已连接
      if (targetNode.sourceIds.includes(sourceId)) {
        return true;
      }

      // 检查是否会形成循环
      if (this.wouldFormCycle(sourceId, targetId)) {
        console.warn(`连接会形成循环: ${sourceId} -> ${targetId}`);
        return false;
      }

      // 添加连接
      targetNode.sourceIds.push(sourceId);
      targetNode._timestamp = Date.now();

      // 清除结果缓存
      this.clearNodeResultCache(targetId);

      // 清除下游节点的结果缓存
      this.clearDownstreamResultCache(targetId);

      return true;
    },

    /**
     * 断开节点连接
     * @param {string} sourceId - 源节点ID
     * @param {string} targetId - 目标节点ID
     * @returns {boolean} 断开连接是否成功
     */
    disconnectNodes(sourceId, targetId) {
      const targetNode = this.nodes[targetId];

      if (!targetNode) return false;

      // 检查是否已连接
      const index = targetNode.sourceIds.indexOf(sourceId);
      if (index === -1) {
        return false;
      }

      // 移除连接
      targetNode.sourceIds.splice(index, 1);
      targetNode._timestamp = Date.now();

      // 如果是翻译节点，清除相关数据
      if (targetNode.type === "translation") {
        // 保留翻译参数，但清除结果相关字段
        const updatedParams = { ...targetNode.params };
        delete updatedParams.sourceSegments;
        delete updatedParams.sourceContent;
        delete updatedParams.sourceId;
        delete updatedParams.sourceLanguage;

        // 更新节点参数
        targetNode.params = updatedParams;
      }

      // 清除结果缓存
      this.clearNodeResultCache(targetId);

      // 清除下游节点的结果缓存
      this.clearDownstreamResultCache(targetId);

      return true;
    },

    /**
     * 移除节点
     * @param {string} nodeId - 节点ID
     */
    removeNode(nodeId) {
      if (!this.nodes[nodeId]) return;

      // 断开与其他节点的连接
      Object.values(this.nodes).forEach((node) => {
        const index = node.sourceIds.indexOf(nodeId);
        if (index !== -1) {
          node.sourceIds.splice(index, 1);
          node._timestamp = Date.now();

          // 清除结果缓存
          this.clearNodeResultCache(node.id);

          // 清除下游节点的结果缓存
          this.clearDownstreamResultCache(node.id);
        }
      });

      // 清除结果缓存
      this.clearNodeResultCache(nodeId);

      // 移除节点
      delete this.nodes[nodeId];

      // 如果移除的是当前选中的节点，清除选中状态
      if (this.selectedNodeId === nodeId) {
        this.selectedNodeId = null;
      }
    },

    /**
     * 选择节点
     * @param {string} nodeId - 节点ID
     */
    selectNode(nodeId) {
      this.selectedNodeId = nodeId;
    },

    /**
     * 取消选择节点
     */
    deselectNode() {
      this.selectedNodeId = null;
    },

    /**
     * 处理节点
     * @param {string} nodeId - 节点ID
     * @returns {*} 节点处理结果
     */
    processNode(nodeId) {
      // 如果有缓存且节点未更新，直接返回缓存
      const cachedResult = this.getNodeResult(nodeId);
      if (cachedResult !== null) {
        return cachedResult;
      }

      // 获取节点
      const node = this.nodes[nodeId];
      if (!node) {
        throw new Error(`节点不存在: ${nodeId}`);
      }

      // 处理源节点
      const sourceResults = [];
      if (node.sourceIds && node.sourceIds.length > 0) {
        for (const sourceId of node.sourceIds) {
          sourceResults.push(this.processNode(sourceId));
        }
      }

      // 处理节点
      const result = nodeFactory.processNode(node, sourceResults);

      // 缓存结果
      this.nodeResults[nodeId] = {
        result,
        _timestamp: Date.now(),
      };

      return result;
    },

    /**
     * 清除节点结果缓存
     * @param {string} nodeId - 节点ID
     */
    clearNodeResultCache(nodeId) {
      delete this.nodeResults[nodeId];
    },

    /**
     * 清除下游节点的结果缓存
     * @param {string} nodeId - 节点ID
     */
    clearDownstreamResultCache(nodeId) {
      const visited = new Set();

      const clearCache = (id) => {
        if (visited.has(id)) return;
        visited.add(id);

        // 清除当前节点的缓存
        this.clearNodeResultCache(id);

        // 递归清除下游节点的缓存
        const targets = this.getNodeTargets(id);
        targets.forEach((target) => clearCache(target.id));
      };

      clearCache(nodeId);
    },

    /**
     * 检查连接是否会形成循环
     * @param {string} sourceId - 源节点ID
     * @param {string} targetId - 目标节点ID
     * @returns {boolean} 是否会形成循环
     */
    wouldFormCycle(sourceId, targetId) {
      const visited = new Set();

      const dfs = (currentId) => {
        if (currentId === sourceId) return true;
        if (visited.has(currentId)) return false;

        visited.add(currentId);

        const node = this.nodes[currentId];
        if (!node || !node.sourceIds || node.sourceIds.length === 0)
          return false;

        for (const id of node.sourceIds) {
          if (dfs(id)) return true;
        }

        return false;
      };

      return dfs(targetId);
    },

    /**
     * 导出节点数据
     * @returns {Object} 节点数据
     */
    exportNodes() {
      // 处理所有节点，确保获取最新的处理结果
      Object.keys(this.nodes).forEach((nodeId) => {
        try {
          this.processNode(nodeId);
        } catch (error) {
          console.warn(`处理节点 ${nodeId} 时出错:`, error);
        }
      });

      // 收集资源管理节点的数据
      const resources = {
        annotations: {},
        translations: {},
        audioItems: [],
      };

      // 查找所有资源管理节点
      const resourceNodes = Object.values(this.nodes).filter(
        (node) => node.type === "resource"
      );

      // 合并所有资源管理节点的数据
      resourceNodes.forEach((node) => {
        // 获取节点的处理结果
        const result = this.nodeResults[node.id]?.result;

        if (result) {
          // 合并标注
          if (result.annotations) {
            resources.annotations = {
              ...resources.annotations,
              ...result.annotations,
            };
          }

          // 合并翻译
          if (result.translations) {
            Object.entries(result.translations).forEach(
              ([lang, translations]) => {
                if (!resources.translations[lang]) {
                  resources.translations[lang] = {};
                }
                resources.translations[lang] = {
                  ...resources.translations[lang],
                  ...translations,
                };
              }
            );
          }

          // 合并音频项
          if (result.audioItems) {
            // 使用Map避免重复
            const audioMap = new Map();

            // 添加已有的音频项
            resources.audioItems.forEach((item) => {
              audioMap.set(item.id, item);
            });

            // 添加新的音频项
            result.audioItems.forEach((item) => {
              audioMap.set(item.id, item);
            });

            // 转换回数组
            resources.audioItems = Array.from(audioMap.values());
          }
        }
      });

      return {
        nodes: { ...this.nodes },
        nodeTypeCounters: { ...this.nodeTypeCounters },
        resources: resources,
      };
    },

    /**
     * 导入节点数据
     * @param {Object} data - 节点数据
     */
    importNodes(data) {
      // 保存导入的数据到localStorage，以便后续使用
      try {
        localStorage.setItem("importedData", JSON.stringify(data));
      } catch (error) {
        console.error("保存导入数据到localStorage失败:", error);
      }

      // 清空现有节点
      this.clearAllNodes();

      // 导入节点
      if (data.nodes) {
        this.nodes = { ...data.nodes };
      }

      // 导入节点类型计数器
      if (data.nodeTypeCounters) {
        this.nodeTypeCounters = { ...data.nodeTypeCounters };
      }

      // 导入资源数据到资源管理节点
      if (data.resources) {
        // 查找所有资源管理节点
        const resourceNodes = Object.values(this.nodes).filter(
          (node) => node.type === "resource"
        );

        // 如果有资源管理节点，将资源数据导入到第一个节点
        if (resourceNodes.length > 0) {
          const firstResourceNode = resourceNodes[0];

          // 更新节点参数
          const updatedParams = { ...firstResourceNode.params };

          // 导入标注
          if (data.resources.annotations) {
            updatedParams.annotations = { ...data.resources.annotations };
          }

          // 导入翻译
          if (data.resources.translations) {
            updatedParams.translations = { ...data.resources.translations };
          }

          // 导入音频项（作为参数存储）
          if (
            data.resources.audioItems &&
            data.resources.audioItems.length > 0
          ) {
            updatedParams.audioItems = [...data.resources.audioItems];

            // 确保音频项中的ID与标注和翻译中的ID一致
            const idMap = {};

            // 创建ID映射表
            updatedParams.audioItems.forEach((item) => {
              if (item.type === "audio") {
                const originalId = item.id;
                // 保留原始ID，不生成新ID
                idMap[originalId] = originalId;
              }
            });

            // 处理源节点，确保它们使用相同的ID
            Object.values(this.nodes).forEach((node) => {
              if (
                node.type === "textContent" &&
                node.params &&
                node.params.segments
              ) {
                // 更新分句ID，使用原始ID
                node.params.segments.forEach((segment) => {
                  if (idMap[segment.id]) {
                    segment.id = idMap[segment.id];
                  }
                });
              }
            });
          }

          // 更新节点参数
          this.nodes[firstResourceNode.id].params = updatedParams;
        }
      }

      // 清除所有结果缓存
      this.nodeResults = {};
    },

    /**
     * 清空所有节点
     */
    clearAllNodes() {
      this.nodes = {};
      this.nodeResults = {};
      this.selectedNodeId = null;
      this.nodeTypeCounters = {};
    },
  },
});
