<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/icons/logo-128.png" type="image/png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Echo Lab 是一个高效的精听学习工具，帮助你通过重复听力快速提升语言理解力。"
    />
    <meta
      name="keywords"
      content="日语,精听,听力训练,语言学习,日本语,N2,N3,N4,N5"
    />

    <!-- PWA支持 -->
    <meta name="theme-color" content="#ff0000" />
    <link rel="manifest" href="/manifest.webmanifest" />

    <!-- 移动设备支持 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <!-- iOS支持 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="apple-mobile-web-app-title" content="Echo Lab" />
    <link rel="apple-touch-icon" href="/icons/logo-192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/logo-256.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/logo-192.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/logo-256.png" />
    <title>Echo Lab - 日语精听工具</title>

    <style>
      html {
        font-size: 16px;
      }
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
      }
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #f8fafc;
        z-index: 9999;
      }
      .spinner {
        margin: 0 auto;
        width: 4.375rem;
        text-align: center;
      }
      .spinner > div {
        width: 1rem;
        height: 1rem;
        background-color: #60a5fa;
        border-radius: 100%;
        display: inline-block;
        animation: sk-bouncedelay 1.4s infinite ease-in-out both;
        opacity: 0.8;
      }
      .spinner .bounce1 {
        animation-delay: -0.32s;
      }
      .spinner .bounce2 {
        animation-delay: -0.16s;
      }
      @keyframes sk-bouncedelay {
        0%,
        80%,
        100% {
          transform: scale(0);
        }
        40% {
          transform: scale(1);
        }
      }
      .loading-text {
        margin-top: 1.5rem;
        font-size: 0.9375rem;
        color: #64748b;
        letter-spacing: 0.03125rem;
      }
    </style>
    <!-- Umami 统计代码 -->
    <script
      async
      defer
      src="//stats.echolab.club/script.js"
      data-website-id="b364c0ed-2ac8-47a9-87fa-381916f725a0"
      data-domains="echolab.club"
    ></script>
  </head>
  <body>
    <div id="app"></div>
    <div id="loading" class="loading-container">
      <div class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
      <div class="loading-text">正在加载资源中...</div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
