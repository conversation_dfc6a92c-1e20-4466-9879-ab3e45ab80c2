<script setup>
import { ref } from 'vue';
import DesktopVideoPlayer from './DesktopVideoPlayer.vue';
import PlaybackContextPanel from './PlaybackContextPanel.vue';

// 接收属性
const props = defineProps({
  timeline: {
    type: Array,
    default: () => []
  },
  audioBuffer: {
    type: Object,
    default: null
  },
  currentIndex: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  showContextPanel: {
    type: Boolean,
    default: true
  },
  videoConfig: {
    type: Object,
    default: () => ({
      cover: {
        imageUrl: "",
        duration: 3
      },
      copyright: {
        text: "",
        position: "bottomRight"
      }
    })
  },
  configJson: {
    type: Object,
    default: null
  },
  contentData: {
    type: Object,
    default: null
  },
  audioSynthesisProgress: {
    type: Object,
    default: () => ({
      total: 0,
      completed: 0,
      percentage: 0,
      isProcessing: false
    })
  }
});

// 事件
const emit = defineEmits([
  'index-change',
  'loading-change',
  'open-settings',
  'toggle-playlist',
  'select-item',
  'go-home'
]);

// 视频播放器引用
const videoPlayerRef = ref(null);

// 跳转到指定索引
const jumpToIndex = (index) => {
  emit('select-item', index);
};

// 暴露方法给父组件
defineExpose({
  videoPlayerRef
});
</script>

<template>
  <div class="desktop-player-layout">
    <!-- 主内容区 -->
    <div class="main-content" :class="{ 'full-width': !props.showContextPanel }">
      <div class="video-player-container" :class="{ 'centered-player': !props.showContextPanel }">


        <div class="aspect-ratio-container">
          <!-- 加载中或有时间线数据和音频缓冲区时显示播放器 -->
          <DesktopVideoPlayer ref="videoPlayerRef"
            v-if="props.isLoading || (props.timeline.length > 0 && props.audioBuffer)" :timeline="props.timeline"
            :audioBuffer="props.audioBuffer" :currentIndex="props.currentIndex" :isLoading="props.isLoading"
            :videoConfig="props.videoConfig" :configJson="props.configJson" :contentData="props.contentData"
            :audioSynthesisProgress="props.audioSynthesisProgress" @index-change="emit('index-change', $event)"
            @loading-change="emit('loading-change', $event)" @open-settings="emit('open-settings')"
            @toggle-playlist="emit('toggle-playlist')">
            <!-- 将收藏按钮放入右侧控制区 -->
            <template #favorite-button>
              <slot name="actions"></slot>
            </template>
          </DesktopVideoPlayer>

          <!-- 没有可播放内容且不在加载中时显示提示 -->
          <div v-else class="loading-placeholder">
            <el-empty description="视频准备中">
              <template #description>
                <div>
                  <p>视频内容尚未准备完成</p>
                  <p class="error-hint">请先完成内容配置并生成视频</p>
                </div>
              </template>
              <el-button type="primary" @click="emit('go-home')">返回首页</el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频内容播放列表 -->
    <div class="context-panel" v-if="props.showContextPanel">
      <div class="panel-header">
        <h3>视频内容播放列表</h3>
      </div>
      <PlaybackContextPanel :timeline="props.timeline" :currentIndex="props.currentIndex" @select-item="jumpToIndex" />
    </div>
  </div>
</template>

<style scoped>
.desktop-player-layout {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.25rem;
  overflow: hidden;
  justify-content: flex-start;
  /* 默认顶部对齐 */
}

.full-width {
  width: 100%;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.video-player-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.75rem rgba(0, 0, 0, 0.2);
  position: relative;
  width: 100%;
}

.aspect-ratio-container {
  width: 100%;
  max-width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  position: relative;
  overflow: hidden;
}

/* 播放列表关闭时，视频播放器居中显示 */
.centered-player {
  width: 80%;
  max-width: 80%;
}

.aspect-ratio-container>* {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.85);
  color: #fff;
  z-index: 10;
}

.error-hint {
  font-size: 0.875rem;
  color: #909399;
  margin-top: 0.625rem;
}

.context-panel {
  width: 25%;
  background-color: #fff;
  border-left: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
  box-shadow: -0.125rem 0 0.5rem rgba(0, 0, 0, 0.05);
  flex: 0 0 25%;
  /* 固定宽度，不伸缩 */
  overflow: hidden;
  /* 防止内容溢出 */
}

.panel-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}
</style>
