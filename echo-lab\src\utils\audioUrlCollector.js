/**
 * 音频URL收集工具
 * 统一处理从不同来源收集音频URL的逻辑
 */

/**
 * 从时间线收集音频URL
 * @param {Array} timeline 时间线数组
 * @returns {Set} 唯一音频URL集合
 */
export function collectUrlsFromTimeline(timeline) {
  const uniqueUrls = new Set();
  
  if (timeline && timeline.length > 0) {
    timeline.forEach(item => {
      if (item.audioUrl) {
        uniqueUrls.add(item.audioUrl);
      }
    });
  }
  
  return uniqueUrls;
}

/**
 * 从内容资源收集音频URL
 * @param {Object} content 内容对象
 * @returns {Set} 唯一音频URL集合
 */
export function collectUrlsFromResources(content) {
  const uniqueUrls = new Set();
  
  if (!content?.configJson?.resources) {
    return uniqueUrls;
  }
  
  const resources = content.configJson.resources;
  
  // 检查资源中是否有音频项目
  if (resources.audioItems && Array.isArray(resources.audioItems)) {
    resources.audioItems.forEach(item => {
      const url = item.url || item.audioUrl;
      if (url) {
        uniqueUrls.add(url);
      }
    });
  }
  
  return uniqueUrls;
}

/**
 * 收集所有音频URL
 * @param {Array} timeline 时间线数组
 * @param {Object} content 内容对象
 * @returns {Array} 音频URL数组
 */
export function collectAllAudioUrls(timeline, content) {
  // 从时间线收集URL
  const timelineUrls = collectUrlsFromTimeline(timeline);
  
  // 如果时间线中没有音频URL，尝试从资源中获取
  if (timelineUrls.size === 0) {
    const resourceUrls = collectUrlsFromResources(content);
    return Array.from(resourceUrls);
  }
  
  return Array.from(timelineUrls);
}

/**
 * 验证音频URL的有效性
 * @param {Array} audioUrls 音频URL数组
 * @returns {Object} 验证结果 { isValid: boolean, message: string }
 */
export function validateAudioUrls(audioUrls) {
  if (!audioUrls || audioUrls.length === 0) {
    return {
      isValid: false,
      message: '未找到音频资源，请先生成音频'
    };
  }
  
  // 检查URL格式
  const invalidUrls = audioUrls.filter(url => {
    try {
      new URL(url);
      return false;
    } catch {
      return true;
    }
  });
  
  if (invalidUrls.length > 0) {
    return {
      isValid: false,
      message: `发现无效的音频URL: ${invalidUrls.slice(0, 3).join(', ')}${invalidUrls.length > 3 ? '...' : ''}`
    };
  }
  
  return {
    isValid: true,
    message: `找到 ${audioUrls.length} 个音频资源`
  };
}
