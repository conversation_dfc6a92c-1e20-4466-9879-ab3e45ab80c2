/**
 * 播放策略处理工具
 * 简化和优化播放策略相关的逻辑
 */

/**
 * 播放策略类型枚举
 */
export const PLAYBACK_MODES = {
  SEQUENCE: "sequence", // 基于序列
  SOURCE: "source", // 基于源节点
};

/**
 * 重复播放策略
 */
export const REPEAT_STRATEGIES = {
  SIMPLE: "simple", // 简单重复
  TRANSLATION: "translation", // 带翻译
  KEYWORDS: "keywords", // 带关键词
};

/**
 * 验证播放策略配置
 */
export function validatePlaybackConfig(config) {
  const errors = [];

  if (!config || !config.sections) {
    errors.push("配置缺少sections字段");
    return { valid: false, errors };
  }

  config.sections.forEach((section, index) => {
    // 验证基础字段
    if (!section.id) {
      errors.push(`环节${index + 1}缺少id字段`);
    }

    if (!section.title) {
      errors.push(`环节${index + 1}缺少title字段`);
    }

    // 验证播放参数
    if (section.speed && (section.speed < 0.5 || section.speed > 2.0)) {
      errors.push(`环节${index + 1}的语速超出范围(0.5-2.0)`);
    }

    if (
      section.pauseDuration &&
      (section.pauseDuration < 0 || section.pauseDuration > 10000)
    ) {
      errors.push(`环节${index + 1}的停顿时长超出范围(0-10000ms)`);
    }

    if (
      section.repeatCount &&
      (section.repeatCount < 1 || section.repeatCount > 10)
    ) {
      errors.push(`环节${index + 1}的重复次数超出范围(1-10)`);
    }

    // 验证翻译配置
    if (section.enableTranslation) {
      if (!section.translationLanguage) {
        errors.push(`环节${index + 1}启用翻译但未设置翻译语言`);
      }

      if (
        section.translationPosition &&
        (section.translationPosition < 1 ||
          section.translationPosition > (section.repeatCount || 4))
      ) {
        errors.push(`环节${index + 1}的翻译位置超出重复次数范围`);
      }
    }

    // 验证关键词配置
    if (section.enableKeywords) {
      if (
        section.keywordPosition &&
        (section.keywordPosition < 1 ||
          section.keywordPosition > (section.repeatCount || 4))
      ) {
        errors.push(`环节${index + 1}的关键词位置超出重复次数范围`);
      }
    }

    // 验证数组字段
    if (
      section.repeatSpeeds &&
      section.repeatSpeeds.length !== (section.repeatCount || 4)
    ) {
      errors.push(`环节${index + 1}的重复速度数组长度与重复次数不匹配`);
    }

    if (
      section.repeatPauses &&
      section.repeatPauses.length !== (section.repeatCount || 4)
    ) {
      errors.push(`环节${index + 1}的重复停顿数组长度与重复次数不匹配`);
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * 标准化播放策略配置
 */
export function normalizePlaybackConfig(config) {
  if (!config || !config.sections) {
    return { sections: [] };
  }

  const normalizedSections = config.sections.map((section) => {
    const normalized = { ...section };

    // 确保基础字段存在
    normalized.speed = normalized.speed || 1.0;
    normalized.pauseDuration = normalized.pauseDuration || 3000;
    normalized.repeatCount = normalized.repeatCount || 4;

    // 标准化布尔字段
    normalized.enableTranslation = normalized.enableTranslation === true;
    normalized.enableKeywords = normalized.enableKeywords === true;

    // 确保位置字段在有效范围内
    if (normalized.enableTranslation) {
      normalized.translationPosition = Math.min(
        Math.max(1, normalized.translationPosition || 2),
        normalized.repeatCount
      );
    }

    if (normalized.enableKeywords) {
      normalized.keywordPosition = Math.min(
        Math.max(1, normalized.keywordPosition || 2),
        normalized.repeatCount
      );
      normalized.keywordRepeatCount = normalized.keywordRepeatCount || 2;
      normalized.keywordSpeed = normalized.keywordSpeed || 1.0;
    }

    // 确保数组字段存在且长度正确
    if (
      !Array.isArray(normalized.repeatSpeeds) ||
      normalized.repeatSpeeds.length !== normalized.repeatCount
    ) {
      normalized.repeatSpeeds = Array(normalized.repeatCount).fill(
        normalized.speed
      );
    }

    if (
      !Array.isArray(normalized.repeatPauses) ||
      normalized.repeatPauses.length !== normalized.repeatCount
    ) {
      normalized.repeatPauses = Array(normalized.repeatCount).fill(
        normalized.pauseDuration
      );
    }

    return normalized;
  });

  return { sections: normalizedSections };
}

/**
 * 计算播放策略的总时长估算
 */
export function estimatePlaybackDuration(config, sourceItems) {
  if (!config || !config.sections || !sourceItems || sourceItems.length === 0) {
    return 0;
  }

  let totalDuration = 0;

  config.sections.forEach((section) => {
    const sectionItems = getSectionItems(sourceItems, section);
    const sectionDuration = calculateSectionDuration(section, sectionItems);
    totalDuration += sectionDuration;
  });

  return totalDuration;
}

/**
 * 获取环节对应的源项目
 */
function getSectionItems(sourceItems, section) {
  if (section.processingMode === "source" && section.sourceNodeIds) {
    // 基于源节点的环节
    return sourceItems.filter((item) =>
      section.sourceNodeIds.includes(item.nodeId || item.id)
    );
  } else {
    // 基于序列的环节
    const sourceIndex = section.sourceIndex || 0;
    return sourceItems[sourceIndex] || [];
  }
}

/**
 * 计算单个环节的时长
 */
function calculateSectionDuration(section, items) {
  if (!items || items.length === 0) {
    return 0;
  }

  const repeatCount = section.repeatCount || 4;
  const pauseDuration = section.pauseDuration || 3000;
  const speed = section.speed || 1.0;

  let sectionDuration = 0;

  items.forEach((item) => {
    const itemDuration = (item.duration || 2) / speed;
    const itemTotalDuration =
      itemDuration * repeatCount + (pauseDuration / 1000) * repeatCount;

    // 如果启用翻译，添加翻译时长
    if (section.enableTranslation && section.translationLanguage) {
      const translationDuration = itemDuration * 0.8; // 估算翻译时长为原文的80%
      sectionDuration += translationDuration;
    }

    // 如果启用关键词，添加关键词时长
    if (section.enableKeywords) {
      const keywordCount = item.keywords ? item.keywords.length : 0;
      const keywordDuration =
        keywordCount * 1.5 * (section.keywordRepeatCount || 2); // 估算每个关键词1.5秒
      sectionDuration += keywordDuration;
    }

    sectionDuration += itemTotalDuration;
  });

  return sectionDuration;
}

/**
 * 生成播放策略摘要
 */
export function generatePlaybackSummary(config) {
  if (!config || !config.sections) {
    return "无播放策略";
  }

  const summaries = config.sections.map((section, index) => {
    const features = [];

    features.push(`${section.repeatCount || 4}次重复`);

    if (section.speed !== 1.0) {
      features.push(`${section.speed}x语速`);
    }

    if (section.enableTranslation) {
      features.push("含翻译");
    }

    if (section.enableKeywords) {
      features.push("含关键词");
    }

    return `环节${index + 1}: ${features.join(", ")}`;
  });

  return summaries.join(" | ");
}

/**
 * 比较两个播放策略配置是否相同
 */
export function comparePlaybackConfigs(config1, config2) {
  if (!config1 || !config2) {
    return config1 === config2;
  }

  if (!config1.sections || !config2.sections) {
    return false;
  }

  if (config1.sections.length !== config2.sections.length) {
    return false;
  }

  return config1.sections.every((section1, index) => {
    const section2 = config2.sections[index];
    return compareSections(section1, section2);
  });
}

/**
 * 比较两个环节配置是否相同
 */
function compareSections(section1, section2) {
  const fields = [
    "repeatCount",
    "speed",
    "pauseDuration",
    "keywordSpeed",
    "enableTranslation",
    "translationLanguage",
    "translationPosition",
    "enableKeywords",
    "keywordRepeatCount",
    "keywordPosition",
  ];

  for (const field of fields) {
    if (section1[field] !== section2[field]) {
      return false;
    }
  }

  // 比较数组字段
  const arrayFields = ["repeatSpeeds", "repeatPauses"];
  for (const field of arrayFields) {
    const array1 = section1[field];
    const array2 = section2[field];

    if (array1 && array2) {
      if (array1.length !== array2.length) {
        return false;
      }
      for (let i = 0; i < array1.length; i++) {
        if (array1[i] !== array2[i]) {
          return false;
        }
      }
    } else if (array1 || array2) {
      return false;
    }
  }

  return true;
}

/**
 * 创建默认播放策略配置
 * 直接返回服务器配置，不添加默认值
 */
export function createDefaultPlaybackConfig(serverConfig) {
  if (!serverConfig || !serverConfig.sections) {
    return { sections: [] };
  }

  // 直接返回服务器配置，保持原有的完整配置
  return serverConfig;
}

/**
 * 优化播放策略配置
 * 移除冗余数据，优化性能
 */
export function optimizePlaybackConfig(config) {
  if (!config || !config.sections) {
    return config;
  }

  const optimizedSections = config.sections.map((section) => {
    const optimized = { ...section };

    // 移除不需要的字段
    delete optimized.type;

    // 确保数组字段长度正确
    const repeatCount = optimized.repeatCount || 4;

    if (
      optimized.repeatSpeeds &&
      optimized.repeatSpeeds.length !== repeatCount
    ) {
      optimized.repeatSpeeds = Array(repeatCount).fill(optimized.speed || 1.0);
    }

    if (
      optimized.repeatPauses &&
      optimized.repeatPauses.length !== repeatCount
    ) {
      optimized.repeatPauses = Array(repeatCount).fill(
        optimized.pauseDuration || 3000
      );
    }

    return optimized;
  });

  return { sections: optimizedSections };
}
